import 'package:flutter/material.dart';

class CircleOfficialBadge extends StatelessWidget {
  final double height;
  final String text;
  const CircleOfficialBadge(
      {super.key, required this.height, required this.text});

  factory CircleOfficialBadge.channel({required double height}) =>
      CircleOfficialBadge(height: height, text: 'అధికారిక ఛానల్');
  factory CircleOfficialBadge.group({required double height}) =>
      CircleOfficialBadge(height: height, text: 'అధికారిక గ్రూప్');
  factory CircleOfficialBadge.circle({required double height}) =>
      CircleOfficialBadge(height: height, text: 'అధికారిక సర్కిల్');

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height,
      child: Stack(alignment: Alignment.centerLeft, children: [
        Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(height / 2),
              color: const Color(0xFFE9F7FF),
            ),
            margin: EdgeInsets.only(left: height / 3),
            padding: height > 36 ? EdgeInsets.all(height * 0.071) : null,
            child: Container(
              decoration: height > 36
                  ? BoxDecoration(
                      borderRadius: BorderRadius.circular(height / 2),
                      border: Border.all(
                        color: const Color(0xFFBDCBD5),
                        width: height / 40,
                      ),
                    )
                  : null,
              padding: EdgeInsets.only(
                  top: height > 36 ? height * 0.1 : height * 0.2,
                  left: 0.8 * height,
                  right: 0.5 * height,
                  bottom: height > 36 ? 0 : 0.05 * height),
              child: Text(text,
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: height > 36 ? height / 3 : height / 2.8,
                    fontWeight: FontWeight.bold,
                  )),
            )),
        Transform.translate(
          offset: Offset(-height * 0.071, 0),
          child: SizedBox(
              width: height,
              height: height,
              child: Image.asset("assets/images/circle-official-star.png")),
        ),
      ]),
    );
  }
}
