/// Flutter icons PrajaIcons
/// Copyright (C) 2024 by original authors @ fluttericon.com, fontello.com
/// This font was generated by FlutterIcon.com, which is derived from Fontello.
///
/// To use this font, place it in your fonts/ directory and include the
/// following in your pubspec.yaml
///
/// flutter:
///   fonts:
///    - family:  PrajaIcons
///      fonts:
///       - asset: fonts/PrajaIcons.ttf
///
///
///
import 'package:flutter/widgets.dart';

class PrajaIcons {
  PrajaIcons._();

  static const _kFontFam = 'PrajaIcons';
  static const String? _kFontPkg = null;

  static const IconData audio_message =
      IconData(0xe800, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData back =
      IconData(0xe801, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData channel_details_more_info =
      IconData(0xe802, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData channel_details_invite_link =
      IconData(0xe803, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData audio_message_fill =
      IconData(0xe804, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData cancel =
      IconData(0xe805, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData channel_details_channel_details =
      IconData(0xe806, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData channel_details_groups =
      IconData(0xe807, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData call =
      IconData(0xe808, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData channel_scroll =
      IconData(0xe809, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData channel_details_unute =
      IconData(0xe80a, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData channel_attach_media =
      IconData(0xe80b, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData channel_creative =
      IconData(0xe80c, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData channel_creative_1 =
      IconData(0xe80d, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData channel_share =
      IconData(0xe80e, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData channel_message_forward =
      IconData(0xe80f, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData channel_image =
      IconData(0xe810, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData channel_trend_active =
      IconData(0xe811, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData channel_video =
      IconData(0xe812, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData channel_trend_default =
      IconData(0xe813, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_add_user =
      IconData(0xe814, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData channel =
      IconData(0xe815, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_add_groups =
      IconData(0xe816, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData chevron_right =
      IconData(0xe817, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_admin =
      IconData(0xe818, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_birthday =
      IconData(0xe819, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_checkbox =
      IconData(0xe81a, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_fb_fill =
      IconData(0xe81b, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_checkbox_blank =
      IconData(0xe81c, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_favourite =
      IconData(0xe81d, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_fb_outline =
      IconData(0xe81e, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_group =
      IconData(0xe81f, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_exit =
      IconData(0xe820, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_chevron =
      IconData(0xe821, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_fb =
      IconData(0xe822, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_location =
      IconData(0xe823, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_notification =
      IconData(0xe824, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_leftarrow =
      IconData(0xe825, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_group_chat_fill =
      IconData(0xe826, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_more_info =
      IconData(0xe827, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_group_chat =
      IconData(0xe828, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_download =
      IconData(0xe829, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_notification_fill =
      IconData(0xe82a, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_leader_group =
      IconData(0xe82b, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_camera =
      IconData(0xe82c, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_messages =
      IconData(0xe82d, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_location_group =
      IconData(0xe82e, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_privacy =
      IconData(0xe82f, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_search =
      IconData(0xe830, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_settings =
      IconData(0xe831, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_forward_attachment =
      IconData(0xe832, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_tick =
      IconData(0xe833, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_trend =
      IconData(0xe834, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_twitter_fill =
      IconData(0xe835, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_terms =
      IconData(0xe836, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_praja =
      IconData(0xe837, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_whatsapp_fill =
      IconData(0xe838, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData delete =
      IconData(0xe839, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData crown_outline =
      IconData(0xe83a, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_user_fill =
      IconData(0xe83b, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData leftarrow =
      IconData(0xe83c, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData comments =
      IconData(0xe83d, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData emoji =
      IconData(0xe83e, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_video =
      IconData(0xe83f, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData forward_message =
      IconData(0xe840, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData dm =
      IconData(0xe841, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData doubletick =
      IconData(0xe842, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_user_line =
      IconData(0xe843, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_trending_fill =
      IconData(0xe844, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData mute_channel_home =
      IconData(0xe845, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData groups =
      IconData(0xe846, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_party_group =
      IconData(0xe847, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData notification =
      IconData(0xe848, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData message =
      IconData(0xe849, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData pushpin_fill =
      IconData(0xe84a, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData premium_crown =
      IconData(0xe84b, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData views =
      IconData(0xe84c, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData trend_1 =
      IconData(0xe84d, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData unmute_channel_home =
      IconData(0xe84e, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData send_message =
      IconData(0xe84f, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData tick =
      IconData(0xe850, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData draft_message =
      IconData(0xe851, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData subscriptions =
      IconData(0xe852, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData replyto_message =
      IconData(0xe853, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData send =
      IconData(0xe854, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData posters =
      IconData(0xe855, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_whatsapp_line =
      IconData(0xe856, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData trend =
      IconData(0xe857, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData search =
      IconData(0xe858, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData circle_share =
      IconData(0xe859, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData remove =
      IconData(0xe85a, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData whatsapp_fill =
      IconData(0xe85b, fontFamily: _kFontFam, fontPackage: _kFontPkg);
}
