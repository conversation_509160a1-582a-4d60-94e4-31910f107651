import 'package:flutter/material.dart';

class TimedLinearLoadingIndicator extends StatefulWidget {
  final Duration duration;
  final double height;
  final Color backgroundColor;
  final Color valueColor;
  final double borderRadius;
  final VoidCallback? onLoadComplete;

  const TimedLinearLoadingIndicator({
    super.key,
    required this.duration,
    this.height = 2,
    this.borderRadius = 2.0,
    this.backgroundColor = Colors.grey,
    this.valueColor = Colors.black,
    this.onLoadComplete,
  });

  @override
  State<TimedLinearLoadingIndicator> createState() =>
      _TimedLinearLoadingIndicatorState();
}

class _TimedLinearLoadingIndicatorState
    extends State<TimedLinearLoadingIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _animation = Tween(begin: 0.0, end: 1.0).animate(_controller);
    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        widget.onLoadComplete?.call();
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (_controller.isAnimating && _controller.duration != widget.duration) {
      _controller.reset();
      _controller.duration = widget.duration;
    }

    if (!_controller.isAnimating) {
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (_, __) => LinearProgressIndicator(
        value: _animation.value,
        backgroundColor: widget.backgroundColor,
        valueColor: AlwaysStoppedAnimation<Color>(widget.valueColor),
        minHeight: widget.height,
        borderRadius: BorderRadius.circular(widget.borderRadius),
      ),
    );
  }
}
