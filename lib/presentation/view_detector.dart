import 'dart:async';

import 'package:flutter/material.dart';
import 'package:praja/services/app_state.dart';
import 'package:visibility_detector/visibility_detector.dart';

typedef ViewDetectorBuilder = Widget Function(
    BuildContext context, bool isVisible);

typedef ViewCallback = void Function(int viewcount);

class ViewDetector extends StatefulWidget {
  final String uniqueId;
  final ViewDetectorBuilder builder;
  final ViewCallback onView;
  final VoidCallback? onOutOfView;
  final double threshold;

  const ViewDetector({
    super.key,
    required this.uniqueId,
    required this.builder,
    this.threshold = 0.8,
    required this.onView,
    this.onOutOfView,
  }) : assert(threshold >= 0 && threshold <= 1);

  @override
  State<ViewDetector> createState() => _ViewDetectorState();
}

class _ViewDetectorState extends State<ViewDetector> {
  bool _isVisible = false;
  bool _isInForeground = false;
  int viewCount = 0;

  StreamSubscription<AppLifecycleState>? _lifecycleStateSubscription;

  @override
  void initState() {
    super.initState();
    _isInForeground = AppState.isInForeground();
    _lifecycleStateSubscription = AppState.lifecycleStateStream.listen((state) {
      if (state.isInForeground() && !_isInForeground) {
        _isInForeground = true;
        if (_isVisible && mounted) widget.onView(++viewCount);
      } else if (state.isInBackground() && _isInForeground) {
        _isInForeground = false;
        if (_isVisible && mounted) {
          widget.onOutOfView?.call();
        }
      }
    });
  }

  @override
  void dispose() {
    _lifecycleStateSubscription?.cancel();
    super.dispose();
  }

  void _onVisibilityChanged(VisibilityInfo info) {
    if (!mounted) return;

    if (info.visibleFraction >= widget.threshold && !_isVisible) {
      setState(() {
        _isVisible = true;
      });
      widget.onView(++viewCount);
    } else if (info.visibleFraction < widget.threshold && _isVisible) {
      setState(() {
        _isVisible = false;
      });
      widget.onOutOfView?.call();
    }
  }

  @override
  Widget build(BuildContext context) {
    return VisibilityDetector(
        key: ValueKey(widget.uniqueId),
        onVisibilityChanged: _onVisibilityChanged,
        child: widget.builder(context, _isVisible));
  }
}

//TODO: Remove this by updating `ViewDetector` in the coming release
class ViewDetectorV2 extends StatefulWidget {
  final String uniqueId;
  final ViewDetectorBuilder builder;
  final ViewCallback onView;
  final VoidCallback? onOutOfView;
  final double threshold;

  const ViewDetectorV2({
    super.key,
    required this.uniqueId,
    required this.builder,
    this.threshold = 0.8,
    required this.onView,
    this.onOutOfView,
  }) : assert(threshold >= 0 && threshold <= 1);

  @override
  State<ViewDetectorV2> createState() => _ViewDetectorV2State();
}

class _ViewDetectorV2State extends State<ViewDetectorV2> {
  bool _isVisible = false; // tracks widget visibility
  bool _isInForeground = false; // tracks if the app is in the foreground
  int viewCount = 0;

  StreamSubscription<AppLifecycleState>? _lifecycleStateSubscription;

  @override
  void initState() {
    super.initState();
    _isInForeground = AppState.isInForeground();
    // listen app lifecycle state changes
    _lifecycleStateSubscription = AppState.lifecycleStateStream.listen((state) {
      if (state.isInForeground() && !_isInForeground) {
        _isInForeground = true;

        // onView if the widget is visible when app enters foreground
        if (_isVisible && mounted) {
          widget.onView(++viewCount);
        }
      } else if (state.isInBackground() && _isInForeground) {
        _isInForeground = false;

        // onOutOfView if the widget is visible when app enters background
        if (_isVisible && mounted) {
          _triggerOutOfView();
        }
      }
    });
  }

  @override
  void dispose() {
    _lifecycleStateSubscription?.cancel();
    super.dispose();
  }

  void _onVisibilityChanged(VisibilityInfo info) {
    if (!mounted) return;

    if (info.visibleFraction >= widget.threshold && !_isVisible) {
      setState(() {
        _isVisible = true;
      });

      // onView when the widget becomes visible
      if (_isInForeground) {
        widget.onView(++viewCount);
      }
    } else if (info.visibleFraction < widget.threshold && _isVisible) {
      setState(() {
        _isVisible = false;
      });

      // onOutOfView when the widget is no longer visible
      _triggerOutOfView();
    }
  }

  void _triggerOutOfView() {
    widget.onOutOfView?.call();
  }

  @override
  Widget build(BuildContext context) {
    return VisibilityDetector(
      key: ValueKey(widget.uniqueId),
      onVisibilityChanged: _onVisibilityChanged,
      child: widget.builder(context, _isVisible),
    );
  }
}
