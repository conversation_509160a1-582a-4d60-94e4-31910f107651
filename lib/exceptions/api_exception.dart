import 'dart:io';

import 'package:dio/dio.dart';

class ApiException implements Exception {
  final String message;
  final ApiFailureType failureType;
  final DioException? dioError;

  ApiException(this.message, this.failureType, this.dioError);

  factory ApiException.unprocessableResponse(String message) {
    return ApiException(message, ApiFailureType.unProcessable, null);
  }

  factory ApiException.fromDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return ApiException(
            'Connect timeout', ApiFailureType.connectTimedOut, error);
      case DioExceptionType.receiveTimeout:
        return ApiException(
            'Receive timeout', ApiFailureType.receiveTimedOut, error);
      case DioExceptionType.sendTimeout:
        return ApiException('Send timeout', ApiFailureType.sendTimedOut, error);
      case DioExceptionType.badResponse:
        switch (error.response?.statusCode) {
          case 400:
            return ApiException(
                'HTTP 400 - Bad request', ApiFailureType.badRequest, error);
          case 401:
            return ApiException(
                'HTTP 401 - Unauthorized', ApiFailureType.unauthorized, error);
          case 403:
            return ApiException(
                'HTTP 403 - Forbidden', ApiFailureType.forbidden, error);
          case 404:
            return ApiException(
                'HTTP 404 - Not found', ApiFailureType.notFound, error);
          case 409:
            return ApiException(
                'HTTP 409 - Conflict', ApiFailureType.conflict, error);
          case 414:
            return ApiException(
                'HTTP 414 - URI too long', ApiFailureType.badRequest, error);
          case 422:
            return ApiException('HTTP 422 - UnProcessable entity',
                ApiFailureType.unProcessable, error);
          case 500:
            return ApiException('HTTP 500 - Internal server error',
                ApiFailureType.serverError, error);
          case 502:
            return ApiException(
                'HTTP 502 - Bad gateway', ApiFailureType.serverError, error);
          case 503:
            return ApiException('HTTP 503 - Service unavailable',
                ApiFailureType.serverError, error);
          case 504:
            return ApiException('HTTP 504 - Gateway timeout',
                ApiFailureType.serverError, error);
          default:
            return ApiException("HTTP ${error.response?.statusCode}",
                ApiFailureType.serverError, error);
        }
      case DioExceptionType.cancel:
        return ApiException(
            'Request cancelled', ApiFailureType.requestCancelled, error);
      case DioExceptionType.badCertificate:
        return ApiException(
            'Bad certificate', ApiFailureType.otherNetworkError, error);
      case DioExceptionType.connectionError:
        return ApiException(
            'Connection error', ApiFailureType.otherNetworkError, error);
      case DioExceptionType.unknown:
        final sourceError = error.error;
        if (sourceError is SocketException) {
          final errorCode = sourceError.osError?.errorCode;
          if (errorCode == 7) {
            return ApiException(
                '$sourceError', ApiFailureType.noInternet, error);
          } else if (errorCode == 101) {
            return ApiException(
                '$sourceError', ApiFailureType.noInternet, error);
          }
        }
        return ApiException(
            'Unknown error: $sourceError', ApiFailureType.unknownError, error);
    }
  }

  @override
  String toString() {
    return 'ApiException: $message';
  }

  String? messageInResponse() {
    if (dioError?.response?.data is Map<String, dynamic>) {
      final Map<String, dynamic> data = dioError?.response?.data;
      return data['message'];
    }
    return null;
  }

  bool isNetworkError() {
    return failureType == ApiFailureType.noInternet ||
        failureType == ApiFailureType.connectTimedOut ||
        failureType == ApiFailureType.receiveTimedOut ||
        failureType == ApiFailureType.sendTimedOut ||
        failureType == ApiFailureType.otherNetworkError;
  }

  bool isAppError() {
    return failureType == ApiFailureType.badRequest ||
        failureType == ApiFailureType.conflict ||
        failureType == ApiFailureType.forbidden ||
        failureType == ApiFailureType.notFound ||
        failureType == ApiFailureType.unauthorized ||
        failureType == ApiFailureType.unknownError ||
        failureType == ApiFailureType.unProcessable;
  }
}

enum ApiFailureType {
  noInternet, // no internet or slow internet
  connectTimedOut,
  receiveTimedOut,
  sendTimedOut,
  otherNetworkError,
  serverError,
  unauthorized,
  unknownError,
  badRequest,
  forbidden,
  conflict,
  notFound,
  unProcessable,
  requestCancelled, // we cancelled the request using dio cancel token
}
