import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/common/users_suggestion/users_suggestion_view_model.dart';
import 'package:praja/features/suggestions/models/suggested_section.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/models/circle.dart';
import 'package:praja/presentation/user_avatar.dart';
import 'package:visibility_detector/visibility_detector.dart';

class UserSuggestionWidget extends StatelessWidget {
  final SuggestedSection suggestedSection;
  final double headerTextFontSize;
  final double subHeaderTextFontSize;
  final Function()? onFollowPressed;
  final double? buttonWidth;
  final double? buttonHeight;
  final double marginBottom;
  final double marginTop;
  final String source;
  final Circle? circle;
  UserSuggestionWidget({
    Key? key,
    required this.suggestedSection,
    this.headerTextFontSize = 20,
    this.subHeaderTextFontSize = 20,
    this.marginBottom = 20,
    this.marginTop = 20,
    this.onFollowPressed,
    this.buttonHeight,
    this.buttonWidth,
    required this.source,
    this.circle,
  }) : super(key: key);

  final Set<String> viewedListIds = {};

  static Widget shimmer() {
    return const _Shimmer();
  }

  Widget _getUsersStack(BuildContext context) {
    const avatarSize = 60.0;
    const avatarSpread = 26.0;
    final length = suggestedSection.users.length;
    final int usersCount = suggestedSection.usersCount;
    final int sizedBoxLength = usersCount > 0 ? length + 1 : length;
    return Container(
      padding: EdgeInsets.only(right: usersCount > 0 ? 14 : 0),
      width: avatarSize + (sizedBoxLength * avatarSpread),
      height: avatarSize,
      child: Stack(
        alignment: Alignment.center,
        clipBehavior: Clip.none,
        children: _getUserPhotos(context),
      ),
    );
  }

  List<Widget> _getUserPhotos(BuildContext context) {
    const avatarSize = 60.0;
    const avatarSpread = 38.0;
    double index = 0;
    List<Widget> photoWidgets = [];
    for (var eachUser in suggestedSection.users) {
      photoWidgets.add(
        Positioned(
          right: (index * avatarSpread),
          child: IgnorePointer(
            child: SizedBox(
              width: avatarSize,
              height: avatarSize,
              child: UserAvatar.fromIdentity(
                eachUser,
                size: 60,
              ),
            ),
          ),
        ),
      );
      index += 1;
      if (suggestedSection.usersCount > 0 && index == 5) {
        photoWidgets.insert(
          0,
          Positioned(
            left: index * (avatarSpread),
            child: CircleAvatar(
              radius: 26,
              backgroundColor: Colors.grey.shade300,
              child: AutoSizeText(
                '+${suggestedSection.usersCount}',
                maxLines: 1,
                overflow: TextOverflow.clip,
                minFontSize: 10,
                maxFontSize: 14,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        );
        break;
      }
    }
    return photoWidgets;
  }

  @override
  Widget build(BuildContext context) {
    final viewModel = context.getViewModel<UsersSuggestionViewModel>();
    return VisibilityDetector(
      key: ValueKey(suggestedSection.feedItemId),
      onVisibilityChanged: (info) {
        if (info.visibleFraction > 0.5) {
          final feedItemId = suggestedSection.feedItemId;
          if (feedItemId != null) {
            if (!viewedListIds.contains(feedItemId)) {
              viewedListIds.add(feedItemId);
              try {
                viewModel.trackList(feedItemId);
                Map<String, dynamic> params = {};
                final customProperties = suggestedSection.customProperties;
                if (customProperties != null) {
                  params.addAll(customProperties);
                }
                params.addAll({
                  "source": source,
                  "List Type": "Users",
                  "sub_type": suggestedSection.suggestedSubType,
                  "list_id": feedItemId,
                });
                final logCircle = circle;
                if (logCircle != null) {
                  params.addAll(
                    {
                      "circle_id": logCircle.id.toString(),
                      "circle_name": logCircle.name,
                    },
                  );
                }
                AppAnalytics.logEvent(
                    name: "view_suggested_lists", parameters: params);
              } catch (e) {
                AppAnalytics.logEvent(
                    name: "track_seen_list_fail",
                    parameters: {"id": feedItemId});
              }
              viewedListIds.add(feedItemId);
            }
          }
        }
      },
      child: Container(
        padding: const EdgeInsets.only(
          top: 30.0,
          left: 25,
          right: 25,
          bottom: 30,
        ),
        margin: EdgeInsets.only(
          bottom: marginBottom,
          right: 10,
          left: 10,
          top: marginTop,
        ),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Wrap(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        const Icon(
                          Icons.group_add,
                          size: 24,
                        ),
                        const SizedBox(
                          width: 10,
                        ),
                        Flexible(
                          child: AutoSizeText(
                            suggestedSection.headerText,
                            maxLines: 2,
                            minFontSize: 14,
                            maxFontSize: headerTextFontSize,
                            style: TextStyle(
                              fontSize: headerTextFontSize,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 15,
                    ),
                    AutoSizeText(
                      suggestedSection.subHeaderText,
                      maxLines: 2,
                      minFontSize: 12,
                      maxFontSize: subHeaderTextFontSize,
                      style: TextStyle(
                        fontSize: subHeaderTextFontSize,
                      ),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        _getUsersStack(context),
                      ],
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    Align(
                      alignment: Alignment.center,
                      child: TextButton.icon(
                        style: ButtonStyle(
                          elevation: MaterialStateProperty.all<double>(4),
                          backgroundColor: suggestedSection.users.isNotEmpty
                              ? MaterialStateProperty.all<Color>(
                                  Theme.of(context).primaryColor)
                              : MaterialStateProperty.all<Color>(Colors.grey),
                          minimumSize: MaterialStateProperty.all<Size>(
                            Size(
                                buttonWidth ??
                                    MediaQuery.of(context).size.width * 0.5,
                                buttonHeight ??
                                    MediaQuery.of(context).size.width * 0.16),
                          ),
                          shape: MaterialStateProperty.all<OutlinedBorder>(
                            RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        ),
                        onPressed: onFollowPressed,
                        icon: Icon(
                          // AppIcons.follow_icon,
                          Icons.person_add,
                          color: Colors.white,
                          size: MediaQuery.of(context).size.width * 0.07,
                        ),
                        label: const Text(
                          'ఫాలో',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            height: 2.0,
                          ),
                        ),
                      ),
                    ),
                  ],
                )
              ],
            )
          ],
        ),
      ),
    );
  }
}

//Shimmer
class _Shimmer extends StatelessWidget {
  const _Shimmer({Key? key}) : super(key: key);

  List<Widget> _getUserPhotos(BuildContext context) {
    double index = 0;
    List<Widget> photoWidgets = [];
    for (var i = 0; i < 5; i++) {
      photoWidgets.add(Positioned(
          left: index == 0
              ? null
              : index * -(MediaQuery.of(context).size.width * 0.2) * 0.5,
          child: IgnorePointer(
            child: Padding(
              padding: EdgeInsets.only(
                  left: MediaQuery.of(context).size.width * 0.26),
              child: CircleAvatar(
                radius: 27,
                backgroundColor: Colors.grey.shade300,
              ),
            ),
          )));
      index += 1;
    }
    return photoWidgets;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(
        top: 30.0,
        left: 25,
        right: 25,
        bottom: 30,
      ),
      margin: const EdgeInsets.only(
        bottom: 20,
        right: 10,
        left: 10,
        top: 20,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Wrap(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      const Icon(
                        Icons.group_add,
                        size: 24,
                      ),
                      const SizedBox(
                        width: 10,
                      ),
                      Container(
                        width: MediaQuery.of(context).size.width * 0.6,
                        height: 38,
                        color: Colors.grey[300],
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 15,
                  ),
                  Container(
                    height: 20,
                    color: Colors.grey[300],
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  Padding(
                    padding: EdgeInsets.only(
                      left: MediaQuery.of(context).size.width * 0.13,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Stack(
                          clipBehavior: Clip.none,
                          children: _getUserPhotos(context),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  Align(
                    alignment: Alignment.center,
                    child: TextButton.icon(
                      style: ButtonStyle(
                        elevation: MaterialStateProperty.all<double>(4),
                        backgroundColor:
                            MaterialStateProperty.all<Color>(Colors.grey),
                        minimumSize: MaterialStateProperty.all<Size>(
                          Size(MediaQuery.of(context).size.width * 0.5,
                              MediaQuery.of(context).size.width * 0.16),
                        ),
                        shape: MaterialStateProperty.all<OutlinedBorder>(
                          RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                      ),
                      onPressed: () {},
                      icon: Icon(
                        // AppIcons.follow_icon,
                        Icons.person_add,
                        color: Colors.white,
                        size: MediaQuery.of(context).size.width * 0.07,
                      ),
                      label: const Text(
                        'ఫాలో',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          height: 2.0,
                        ),
                      ),
                    ),
                  ),
                ],
              )
            ],
          )
        ],
      ),
    );
  }
}
