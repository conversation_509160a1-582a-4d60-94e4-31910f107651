import 'package:flutter/material.dart';
import 'package:praja/errors/error_delegate.dart';
import 'package:praja/features/posters/models/full_page_feed_error_feed_item.dart';
import 'package:praja/features/posters/models/posters_feed_view_carousel.dart';
import 'package:praja/features/posters/models/full_page_feed_shimmer_feed_item.dart';
import 'package:praja/common/full_page_feed/full_page_feed_error_widget.dart';
import 'package:praja/features/posters/posters_feed_view/posters_feed_view_carousel_widget.dart';
import 'package:praja/common/full_page_feed/full_page_feed_shimmer.dart';
import 'package:praja/features/premium_experience/models/upgrade_feed_item.dart';
import 'package:praja/features/premium_experience/upgrade/upgrade_feed_item_widget.dart';
import 'package:praja/features/video_posters/models/video_poster_preview_feed_item.dart';
import 'package:praja/features/video_posters/models/video_poster_carousel.dart';
import 'package:praja/features/video_posters/widgets/video_posters_preview_widget.dart';
import 'package:praja/features/video_posters/video_poster_carousel_widget.dart';

class FullPageFeedItemBuilder extends StatelessWidget {
  final dynamic item;
  final String source;
  final int index;
  final Function(Object? error)? onFullPageFeedTryAgain;
  final bool showHorizontalSwipeHint;
  final Function(String returnUrl)? onPhotoUpdated;

  /// Callback when a video poster operation starts, e.g., downloading or sharing
  /// right now using to disable scroll in Posters feed
  final VoidCallback? onVideoPosterOperationStarted;

  /// Callback when a video poster operation ends, e.g., after download or share is complete
  /// right now using to disable scroll in Posters feed
  final VoidCallback? onVideoPosterOperationEnded;

  const FullPageFeedItemBuilder({
    super.key,
    required this.item,
    required this.source,
    required this.index,
    this.onFullPageFeedTryAgain,
    this.showHorizontalSwipeHint = false,
    this.onPhotoUpdated,
    this.onVideoPosterOperationStarted,
    this.onVideoPosterOperationEnded,
  });

  @override
  Widget build(BuildContext context) {
    final feedItem = item;
    if (feedItem is PostersFeedViewCarousel) {
      return PostersFeedViewCarouselWidget(
        key: Key("posters-feed-view-carousel-${feedItem.feedItemId}-$source"),
        postersFeedViewCarousel: feedItem,
        source: source,
        pageIndex: index,
        showHorizontalSwipeHint: showHorizontalSwipeHint,
        onPhotoUpdated: (returnUrl) {
          onPhotoUpdated?.call(returnUrl);
        },
      );
    } else if (feedItem is UpgradeFeedItem) {
      return UpgradeFeedItemWidget(
          upgradeFeedItem: feedItem, source: source, scrollIndex: index);
    } else if (feedItem is FullPageFeedShimmerFeedItem) {
      return const FullPageFeedShimmer();
    } else if (feedItem is FullPageFeedErrorFeedItem) {
      return FullPageFeedErrorWidget(
        onTryAgain: onFullPageFeedTryAgain,
        message: localisedErrorMessage(feedItem.error),
      );
    } else if (feedItem is VideoPosterPreviewFeedItem) {
      return VideoPostersPreviewWidget(
          key: Key("${feedItem.feedItemId}"),
          sourceVideo: feedItem.sourceVideo,
          videoFrames: feedItem.videoFrames);
    } else if (feedItem is VideoPosterCarousel) {
      return VideoPosterCarouselWidget(
        key: Key("video-poster-carousel-${feedItem.feedItemId}-$source"),
        carousel: feedItem,
        source: source,
        pageIndex: index,
        onOperationStarted: onVideoPosterOperationStarted,
        onOperationEnded: onVideoPosterOperationEnded,
      );
    } else {
      return const SizedBox.shrink();
    }
  }
}
