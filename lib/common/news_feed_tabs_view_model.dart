import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/enums/news_feed_type.dart';
import 'package:praja/models/feed.dart';

@injectable
class NewsFeedTabsViewModel extends ViewModel {
  final MutableLiveData<List<Tab>> _newsFeedTabHeaders =
      MutableLiveData<List<Tab>>([]);
  LiveData<List<Tab>> get newsFeedTabHeaders => _newsFeedTabHeaders;

  final MutableLiveData<int> _selectedTabIndex = MutableLiveData<int>(0);
  LiveData<int> get selectedTabIndex => _selectedTabIndex;

  final List<ScrollController> _scrollControllers = [];
  List<ScrollController> get scrollControllers => _scrollControllers;

  final List<Feed> _feeds = [];
  List<Feed> get feeds => _feeds;

  void onFeedsReceived(List<dynamic> feeds) {
    _setFeeds(feeds);
  }

  void onTabChanged(int index) {
    _selectedTabIndex.value = index;
  }

  int indexOf(String feedId) {
    final index = _feeds.indexWhere((element) => element.id == feedId);
    return index;
  }

  int getMyFeedIndex() {
    final index =
        _feeds.indexWhere((element) => element.type == NewsFeedTypeEnum.myFeed);
    return index;
  }

  NewsFeedTypeEnum get firstFeedType {
    if (_feeds.isEmpty) {
      return NewsFeedTypeEnum.myFeed;
    }
    return _feeds.first.type;
  }

  void _setFeeds(List<dynamic> feeds) {
    _feeds.clear();
    _scrollControllers.clear();
    _feeds.addAll(feeds.map((e) => Feed.fromJson(e as Map<String, dynamic>)));
    _scrollControllers
        .addAll(List<ScrollController>.generate(_feeds.length, (index) {
      return ScrollController();
    }));
    _newsFeedTabHeaders.value = _newsFeedTabHeaders.value +
        _feeds.map((e) {
          return Tab(text: e.title);
        }).toList();
  }
}
