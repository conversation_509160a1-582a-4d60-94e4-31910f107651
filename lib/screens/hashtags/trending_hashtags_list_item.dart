import 'package:flutter/material.dart';
import 'package:praja/models/hashtag.dart';
import 'package:praja/models/v2/trending_hashtags.dart';
import 'package:praja/screens/hashtags/hashtag_list_item.dart';

class TrendingHashTagListItem extends StatelessWidget {
  final TrendingHashtags trendingHashtags;

  const TrendingHashTagListItem({
    super.key,
    required this.trendingHashtags,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        for (Hashtag hashtag in trendingHashtags.hashtagsList)
          HashtagListItem(
            hashtag,
            isLast: hashtag == trendingHashtags.hashtagsList.last,
          )
      ],
    );
  }
}
