import 'dart:async';

import 'package:achievement_view/achievement_view.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animator/flutter_animator.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get_it/get_it.dart';
import 'package:intl/intl.dart';
import 'package:praja/common/widgets/avatar_glow.dart';
import 'package:praja/constants/AppConstants.dart';
import 'package:praja/enums/video_mode.dart';
import 'package:praja/errors/error_delegate.dart';
import 'package:praja/extensions/text_style_extensions.dart';
import 'package:praja/features/direct_messaging/service/messaging_config.dart';
import 'package:praja/features/direct_messaging/ui/blink_highlight.dart';
import 'package:praja/features/impression_tracker/view_model/impression_tracker.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/features/photo_viewer/widgets/praja_photo_viewer.dart';
import 'package:praja/features/post/common/post_actions.dart';
import 'package:praja/features/post/enums/post_options.dart';
import 'package:praja/features/profile/my_profile/my_profile_page.dart';
import 'package:praja/features/share/share_icon_tutorial.dart';
import 'package:praja/features/post/extensions/post_sheet.dart';
import 'package:praja/features/post/widgets/views_count_and_tagged_circles_widget.dart';
import 'package:praja/features/share/ui/share_icon.dart';
import 'package:praja/features/share/ui/share_sheet.dart';
import 'package:praja/features/user/follow/state/user_follow_view_model.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/models/badge.dart';
import 'package:praja/models/circle.dart';
import 'package:praja/models/post.dart';
import 'package:praja/models/user.dart';
import 'package:praja/models/user_identity.dart';
import 'package:praja/models/video.dart';
import 'package:praja/presentation/circle_display_picture.dart';
import 'package:praja/presentation/media_carousel.dart';
import 'package:praja/presentation/user_avatar.dart';
import 'package:praja/screens/create/createPage/presentation/create_page.dart';
import 'package:praja/screens/posts/bubble.dart';
import 'package:praja/screens/posts/comment.dart';
import 'package:praja/screens/posts/detail.dart';
import 'package:praja/screens/posts/post_bloc/post_bloc.dart';
import 'package:praja/screens/posts/post_content.dart';
import 'package:praja/screens/posts/shape_of_view.dart';
import 'package:praja/screens/posts/share.dart';
import 'package:praja/screens/posts/share_sections.dart';
import 'package:praja/services/home.dart';
import 'package:praja/services/post/post_service.dart';
import 'package:praja/styles.dart';
import 'package:praja/utils/utils.dart';
import 'package:praja/utils/widgets/badge_banner_tag.dart';
import 'package:praja/utils/widgets/badge_strip_profile.dart';
import 'package:praja/utils/widgets/feed_player/multi_manager/flick_multi_player.dart';
import 'package:praja/utils/widgets/post_link_preview_widget.dart';
import 'package:praja/utils/widgets/trended_users.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:visibility_detector/visibility_detector.dart';

import '../../models/v2/photo.dart';
import '../../utils/widgets/page_transition_widget.dart';
import 'post_follow_button.dart';

// ignore: must_be_immutable
class PostListItem extends StatefulWidget {
  final int? index;
  final Post post;
  final User user;
  final Circle? circle;
  bool tappable;
  final bool isShare;
  final bool isOpinion;
  final bool enableTrendTutorial;
  final VoidCallback? onLike;
  final VoidCallback? onUnLike;
  final FocusNode? commentFocusNode;
  final Function? scrollToComments;
  final bool isParentPostPreview;
  final bool showParentPostPreview;
  bool showCommentBox;
  String? source;
  final bool isFirstElementInList;
  final bool fromPostDetailPage;
  final PostBloc? postBloc;
  final String heroTag;
  final bool disableProfileClick;
  final bool highlightShare;
  final double? width;
  final int? highlightPostId;
  final bool highlightPostCompleted;
  final VoidCallback? onHighlightCompleted;

  PostListItem(
      {super.key,
      this.index,
      required this.post,
      required this.user,
      this.width,
      this.circle,
      this.isFirstElementInList = false,
      this.enableTrendTutorial = false,
      this.tappable = true,
      this.isShare = false,
      this.isOpinion = false,
      this.onLike,
      this.onUnLike,
      this.commentFocusNode,
      this.scrollToComments,
      this.source,
      this.isParentPostPreview = false,
      this.showCommentBox = false,
      this.showParentPostPreview = true,
      this.fromPostDetailPage = false,
      this.postBloc,
      this.highlightShare = false,
      this.heroTag = "post_list_item",
      this.disableProfileClick = false,
      this.highlightPostId,
      this.highlightPostCompleted = false,
      this.onHighlightCompleted}) {
    if (isShare) {
      tappable = false;
    }
    showCommentBox = tappable;
  }

  @override
  State<PostListItem> createState() => _PostListItemState();
}

class _PostListItemState extends State<PostListItem>
    with TickerProviderStateMixin {
  bool postImageLoadError = false;

  _PostListItemState();

  double width = 0;
  int likesCount = 0;
  int commentsCount = 0;
  Timer? followTimer;

  Timer? pollTimer;
  Timer? _whatsappTimer;
  bool _highlightShareAction = false;
  bool showTrendTutorial = false;
  Duration? durationLeft;
  bool seenTrendTutorial = false;
  bool isViewPostElementTriggered = false;
  bool trackedTutorialView = false;

  double postVisibleFraction = 0.0;

//   double visibilityThreshold;
  double visibilityThreshold = Utils.getDefaultVisibilityThresholdvalue();
  late AppConstants appConstants;
  Timer? trendTimer;
  late StreamSubscription<bool> keyboardSubscription;
  late PostBloc postBloc;

  ///variable to fresh the views that are required to be updated.
  bool updateComment = true;

  postUpdateCallBack(Post post) {
    if (widget.circle != null) {
      Utils.showToast(
          context.getString(StringKey.postDeletedText, listen: false));
    } else {
      Utils.showToast(
          context.getString(StringKey.tagRemovedText, listen: false));
    }
    post.isPostRemoved = true;
    postBloc.add(PostUpdated(post));
  }

  void _openPostPhoto(List<Photo> photos, int index) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (BuildContext context) => PrajaPhotoViewer(
          photos: photos,
          post: widget.post,
          index: index,
        ),
      ),
    );
  }

  List<String> _userPermissions = [];
  late UserFollowViewModel userFollowViewModel;

  @override
  void initState() {
    super.initState();
    userFollowViewModel = context.userFollowViewModel(
        widget.post.user.id, widget.post.user.follows);
    likesCount = widget.post.likesCount;
    commentsCount = widget.post.commentsCount;
    postBloc = widget.postBloc ?? BlocProvider.of<PostBloc>(context);
    Utils.getPostVisibilityThreshold().then((v) {
      visibilityThreshold = v;
    });
    Utils.getUserPermissions().then((list) {
      _userPermissions = list;
    });
    appConstants = GetIt.I.get<AppConstants>();
    var keyboardVisibilityController = KeyboardVisibilityController();
    // Query

    // Subscribe
    keyboardSubscription =
        keyboardVisibilityController.onChange.listen((bool visible) {
      if (visible == false) {
        if (FocusScope.of(context).hasFocus) {
          FocusScope.of(context).unfocus();
        }
      }
    });
    _shareIconTuturial.init();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    width = MediaQuery.of(context).size.width;
  }

  @override
  void dispose() {
    keyboardSubscription.cancel();
    _whatsappTimer?.cancel();
    super.dispose();
  }

  Map<String, dynamic> _getAnalyticsParams() {
    Map<String, dynamic> param = {"post_id": widget.post.id.toString()};
    if (widget.source != null) {
      param["source"] = widget.source;
    }
    if (widget.index != null) {
      param["scroll_index"] = widget.index;
    }
    if (widget.user.badge != null && widget.user.badge!.active) {
      param["badge_holder"] = "Yes";
    } else {
      param["badge_holder"] = "No";
    }
    if (widget.post.videos.isNotEmpty) {
      param["format"] = "Contains Video";
    } else if (widget.post.photos.isNotEmpty) {
      param["format"] = "Contains Image";
    } else {
      param["format"] = "Only Text";
    }
    if (widget.post.userSeen) {
      param["already_seen"] = "Yes";
    } else {
      param["already_seen"] = "No";
    }
    if (widget.post.taggedCircles.isNotEmpty) {
      param['have_tagged_circles'] = "Yes";
      param['tagged_circles_count'] =
          widget.post.taggedCircles.length.toString();
    } else {
      param['have_tagged_circles'] = "No";
    }

    if (widget.post.customProperties.isNotEmpty) {
      param.addAll(widget.post.customProperties);
    }

    return param;
  }

  void logViewPostElement() {
    // Log only if this element is not detail page
    if (widget.tappable && !isViewPostElementTriggered) {
      isViewPostElementTriggered = true;

      AppAnalytics.logEvent(
        name: "view_post_element",
        parameters: _getAnalyticsParams(),
      );
    }
  }

  bool isBadgeUser(User user) {
    final badge = user.badge;
    if (badge != null && badge.active) {
      return true;
    }
    return false;
  }

  triggerTrendTutorial() {
    AppConstants appConstants = GetIt.I.get<AppConstants>();
    trendTimer = Timer.periodic(
        Duration(seconds: appConstants.trendTutorialTimeInSeconds), (timer) {
      if (mounted) {
        if (postVisibleFraction >= 0.7) {
          if (appConstants.trendTutorial) {
            setState(() {
              seenTrendTutorial = true;
              showTrendTutorial = true;
              appConstants.trendTutorial = false;
            });
          }
        }
      }
    });
  }

  viewsAndPopUpMenuWidget() {
    if (widget.isParentPostPreview) {
      return const SizedBox();
    }
    return getPopUpMenuButton();
  }

  Widget getViewCountAndTaggedCirclesWidget(
      {EdgeInsets padding = EdgeInsets.zero}) {
    return Padding(
      padding: padding,
      child: ViewsCountAndTaggedCirclesWidget(
        post: widget.post,
        isParentPostPreview: widget.isParentPostPreview,
        circle: widget.circle,
        source: widget.source ?? "list_item",
        postUpdateCallBack: postUpdateCallBack,
      ),
    );
  }

  void onUserTapped(BuildContext context, {required String source}) {
    widget.user.loggedInUser
        ? widget.disableProfileClick
            ? null
            : Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => MyProfilePage(
                    source: source,
                  ),
                ),
              )
        : Utils.openUserPage(context, widget.user.id, source: source);
  }

  @override
  Widget build(BuildContext context) {
    final avatarSize = widget.isParentPostPreview ? 40.0 : 44.0;
    final sideMargin = widget.isParentPostPreview ? 8.0 : 10.0;
    final contentAvatarMargin = widget.isParentPostPreview ? 8.0 : 10.0;
    final contentMargin = sideMargin + avatarSize + contentAvatarMargin;
    final contentPadding = EdgeInsets.only(
      left: contentMargin,
      right: sideMargin,
    );
    //using the  bloc builder to make use of post bloc so that we can
    //use the post object being displayed is from postBloc and maintain
    //it's that post state like
    //likes count, viewsCount, isDeleted, opinionsCount, commentsCount
    // and all the parameters which can be updated either being loaded from
    //api call from somewhere like detail page and make sure that update post
    //will be applied over all app, where ever the post is being displayed and
    //same as that every user action like trending, deleting and all updates needs to be
    //reflected for that post all over the application.
    return VisibilityDetector(
      key: ValueKey(widget.post.id.toString()),
      onVisibilityChanged: (visibilityInfo) {
        if (visibilityInfo.visibleFraction > 0.7) {
          logViewPostElement();
          GetIt.I.get<AppConstants>().onPostSeen(widget.post.id);
          if (mounted) {
            // we are having to use if present because of a crash likely because of the use of Hero
            // https://console.firebase.google.com/project/praja-007/crashlytics/app/android:buzz.praja.app/issues/b351cb74be22d405eb7a21e14dd385d1?time=last-twenty-four-hours&types=crash&versions=2404.16.05%20(24041605)&sessionEventKey=661FC49D037E0001578A62C372F80925_1937262700486261675
            context.impressionTrackerIfPresent()?.trackPost(widget.post.id);
            setState(() {
              widget.post.userSeen = true;
            });
          }
        }
      },
      child: BlocBuilder<PostBloc, PostState>(
          bloc: postBloc,
          builder: (context, state) {
            final statePost = state.post;
            if (statePost != null && statePost.id == widget.post.id) {
              widget.post.viewsCount = statePost.viewsCount;
              widget.post.active = statePost.active;
              widget.post.likesCount = statePost.likesCount;
              widget.post.whatsappCount = statePost.whatsappCount;
              widget.post.opinionsCount = statePost.opinionsCount;
              widget.post.commentsCount = statePost.commentsCount;
              widget.post.userLiked = statePost.userLiked;
              widget.post.taggedCircles = statePost.taggedCircles;
              widget.post.taggedCircleIds = statePost.taggedCircleIds;
              widget.post.adminOptions = statePost.adminOptions;
              widget.post.isPostRemoved = statePost.isPostRemoved;
              widget.post.commentsConfig = statePost.commentsConfig;
              widget.post.commentsEnabled = statePost.commentsEnabled;
              widget.post.user.follows = statePost.user.follows;
              widget.post.user = statePost.user;
            }
            if (!widget.post.active) {
              return Container(
                padding: const EdgeInsets.only(top: 10.0, bottom: 10.0),
                decoration: const BoxDecoration(
                  boxShadow: [
                    BoxShadow(color: Colors.black26),
                  ],
                  color: Colors.white,
                ),
                child: Center(
                  child: Text(context.getString(StringKey.postDeletedText)),
                ),
              );
            }
            final Circle? circle = widget.circle;
            if (widget.post.isPostRemoved &&
                circle != null &&
                !widget.post.taggedCircleIds.contains(circle.id)) {
              return const SizedBox();
            }
            return Material(
              child: VisibilityDetector(
                key: Key("post-${widget.post.id}"),
                onVisibilityChanged: (VisibilityInfo info) {
                  if (info.visibleFraction >= 0.80 &&
                      (widget.highlightShare ||
                          !_shareIconTuturial.userLearntShareIcon)) {
                    _whatsappTimer = Timer(const Duration(seconds: 3), () {
                      if (mounted) {
                        setState(() => _highlightShareAction = true);
                      }
                      _whatsappTimer = Timer(const Duration(seconds: 4), () {
                        if (mounted) {
                          setState(() => _highlightShareAction = false);
                          _onHighlightCompleted();
                        }
                      });
                    });
                  } else {
                    if (_whatsappTimer != null) {
                      _whatsappTimer!.cancel();
                    }
                    if (_highlightShareAction && info.visibleFraction <= 0.5) {
                      if (mounted) {
                        setState(() => _highlightShareAction = false);
                      }
                    }
                  }
                  postVisibleFraction = info.visibleFraction;

                  if ((info.visibleFraction > 0.7)) {
                    if (mounted) {
                      AppConstants appConstants = GetIt.I.get<AppConstants>();
                      if (appConstants.trendTutorial) {
                        if (appConstants.ifSeenCountExceeded()) {
                          if (widget.enableTrendTutorial) {
                            triggerTrendTutorial();
                          }
                        }
                      }
                    }
                  }
                },
                child: InkWell(
                  onTap: _goToPostDetail,
                  child: Container(
                    decoration: const BoxDecoration(
                      color: Colors.white,
                    ),
                    child: BlinkHighlight(
                      key: Key("post-highlight-${widget.post.id}"),
                      enabled: widget.post.id == widget.highlightPostId &&
                          !widget.highlightPostCompleted,
                      onComplete: () {
                        if (widget.highlightPostId == widget.post.id) {
                          widget.onHighlightCompleted?.call();
                        }
                      },
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          (widget.post.feedReason.isNotEmpty &&
                                  widget.post.feedReason != "")
                              ? Container(
                                  padding: const EdgeInsets.fromLTRB(
                                      10.0, 5.0, 10.0, 5.0),
                                  width: double.infinity,
                                  decoration: BoxDecoration(
                                    border: Border(
                                      bottom: BorderSide(
                                        color: Theme.of(context).dividerColor,
                                      ),
                                    ),
                                  ),
                                  child: Text(
                                    widget.post.feedReason,
                                    style: const TextStyle(
                                      fontSize: 12.0,
                                    ),
                                  ),
                                )
                              : Container(),
                          Column(
                            children: [
                              Stack(clipBehavior: Clip.none, children: [
                                Padding(
                                  padding: EdgeInsets.only(top: sideMargin),
                                  child: Column(children: [
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        SizedBox(width: sideMargin),
                                        GestureDetector(
                                            onTap: widget.isShare
                                                ? null
                                                : () {
                                                    onUserTapped(context,
                                                        source:
                                                            "post_user_avatar");
                                                  },
                                            child: UserAvatar.fromIdentity(
                                              widget.user.toIdentity(),
                                              size: avatarSize,
                                            )),
                                        SizedBox(width: contentAvatarMargin),
                                        Expanded(
                                            child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                              Row(
                                                  mainAxisSize:
                                                      MainAxisSize.max,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment
                                                          .baseline,
                                                  textBaseline:
                                                      TextBaseline.alphabetic,
                                                  children: [
                                                    Flexible(
                                                        child:
                                                            getUserNameWidget()),
                                                    const SizedBox(
                                                      width: 4,
                                                    ),
                                                    const Text('.',
                                                        style: TextStyle(
                                                          fontSize: 12.0,
                                                          color: Styles
                                                              .dimTextColor,
                                                        )),
                                                    const SizedBox(
                                                      width: 4,
                                                    ),
                                                    getTimeAgoTextWidget(),
                                                  ]),
                                              SizedBox(
                                                height: widget.post.user
                                                        .isUserHaveBadgeBanner()
                                                    ? 2
                                                    : 0,
                                              ),
                                              widget.post.user
                                                      .isUserHaveBadgeBanner()
                                                  ? getUserBadgeStrip(
                                                      widget.user)
                                                  : const SizedBox(),
                                              SizedBox(
                                                height: widget.post.user
                                                        .isUserHaveBadgeBanner()
                                                    ? 6
                                                    : 0,
                                              ),
                                            ])),
                                        if (!widget.post.user.loggedInUser)
                                          PostFollowButton(
                                            userId: widget.post.user.id,
                                            isFollowing:
                                                widget.post.user.follows,
                                            source: widget.source ?? 'post',
                                          ),
                                        if (!widget.fromPostDetailPage)
                                          viewsAndPopUpMenuWidget(),
                                      ],
                                    ),
                                    Padding(
                                      padding: contentPadding.copyWith(
                                          top: widget.post.user
                                                      .isUserHaveBadgeBanner() &&
                                                  (widget.post.content ?? "")
                                                      .isNotEmpty
                                              ? 8
                                              : 0,
                                          bottom: (widget.post.content ?? "")
                                                  .isNotEmpty
                                              ? 8
                                              : 0),
                                      child: PostContent(
                                        user: widget.user,
                                        source: widget.source,
                                        post: widget.post,
                                        isParentPostPreview:
                                            widget.isParentPostPreview,
                                        tappable: widget.tappable,
                                        isShare: widget.isShare,
                                        heroTag: widget.heroTag,
                                        initialMaxLines: widget.post.id ==
                                                widget.highlightPostId
                                            ? 8
                                            : 6,
                                      ),
                                    ),
                                    getMediaWidget(
                                      context,
                                      padding:
                                          contentPadding.copyWith(bottom: 8),
                                    ),
                                    getPreviewWidget(
                                      width: width,
                                      padding: contentPadding,
                                    ),
                                    if (!widget.isParentPostPreview &&
                                        (widget.post.taggedCircles.isNotEmpty ||
                                            widget.post.viewsCount > 0))
                                      getViewCountAndTaggedCirclesWidget(
                                        padding: contentPadding.copyWith(
                                            bottom: 8,
                                            top: widget
                                                    .post.taggedCircles.isEmpty
                                                ? 0
                                                : 8),
                                      ),
                                    Padding(
                                        padding: contentPadding,
                                        child: (widget.isParentPostPreview ||
                                                (widget.post.taggedCircles
                                                        .isEmpty) &&
                                                    widget.post.viewsCount == 0)
                                            ? const SizedBox(height: 8)
                                            : const Divider(
                                                thickness: 0.5,
                                                height: 3.5,
                                                color: Color(0xffC6C6C6),
                                              )),
                                    getReactionBarWidget(
                                        padding: contentPadding),
                                  ]),
                                ),
                                getTrendTutorialWidget(),
                                showTrendTutorial
                                    ? Positioned(
                                        bottom: -18,
                                        right: (width / 3.3),
                                        child: const IgnorePointer(
                                          child: AvatarGlow(
                                            curve: Curves.easeIn,
                                            endRadius: 30,
                                            glowColor: Color(0xfffe3902),
                                            child: SizedBox(),
                                          ),
                                        ),
                                      )
                                    : const SizedBox(),
                              ]),
                              if (widget.showCommentBox) ...[
                                if (updateComment) getCommentsPreviewWidget(),
                                // getCommentBoxWidget(),
                              ],
                              widget.tappable && !widget.isParentPostPreview
                                  ? const SizedBox(
                                      height: 10,
                                    )
                                  : const SizedBox(),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            );
          }),
    );
  }

  onTrendClickCallback() {
    setState(() {
      showTrendTutorial = false;
    });
  }

  getTrendTutorialWidget() {
    String tutorialText = GetIt.I.get<AppConstants>().trendTutorialText;

    return showTrendTutorial
        ? Positioned(
            bottom: 34,
            right: width / 28,
            child: VisibilityDetector(
              key: UniqueKey(),
              onVisibilityChanged: (info) async {
                if (info.visibleFraction > 0.6) {
                  try {
                    if (!trackedTutorialView) {
                      AppAnalytics.logEvent(name: 'saw_trend_tutorial');
                      await Home.sawTrendTutorial();
                      trackedTutorialView = true;
                    }
                  } catch (e) {
                    Utils.showToast(localisedErrorMessage(e));
                  }
                }
              },
              child: Column(
                children: [
                  InkWell(
                    onTap: () {
                      setState(() {
                        showTrendTutorial = false;
                        seenTrendTutorial = true;
                      });
                    },
                    child: ShapeOfView(
                      elevation: 10,
                      shape: BubbleShape(
                          position: BubblePosition.bottom,
                          arrowPositionPercent: 0.53,
                          borderRadius: 20,
                          arrowHeight: 15,
                          arrowWidth: 7.5),
                      child: Container(
                        color: Colors.grey[100],
                        padding: const EdgeInsets.only(
                            left: 15, right: 20, top: 20, bottom: 25),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Align(
                              alignment: Alignment.center,
                              child: Stack(
                                clipBehavior: Clip.none,
                                children: [
                                  SizedBox(
                                    width: width / 1.5,
                                    child: Center(
                                      child: AutoSizeText(
                                        tutorialText,
                                        textScaler:
                                            const TextScaler.linear(1.0),
                                        textAlign: TextAlign.center,
                                        style: const TextStyle(
                                            fontSize: 16, color: Colors.black),
                                      ),
                                    ),
                                  ),
                                  Positioned(
                                      top: -12,
                                      right: -10,
                                      child: Container(
                                        padding: const EdgeInsets.all(1),
                                        decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(10),
                                            color: Colors.blue[200]),
                                        child: const Icon(
                                          Icons.close,
                                          size: 12,
                                          color: Colors.white,
                                        ),
                                      ))
                                ],
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          )
        : const SizedBox();
  }

  getCommentsPreviewWidget() {
    if (!widget.isParentPostPreview) {
      if (widget.post.previewComments.isNotEmpty) {
        return ListView.builder(
          clipBehavior: Clip.none,
          padding: const EdgeInsets.only(top: 3),
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemBuilder: (context, index) {
            return PostComment(
              comment: widget.post.previewComments[index],
              avatarRadius: 15,
              post: widget.post,
            );
          },
          itemCount: widget.post.previewComments.length,
        );
      }
    }

    return const SizedBox();
  }

  getIconBasedOnCircleType(Circle circle) {
    return circle.type != "location"
        ? Padding(
            padding: const EdgeInsets.all(4.0),
            child: CircleDisplayPicture.fromCircle(circle, size: 20),
          )
        : Container(
            padding: const EdgeInsets.symmetric(horizontal: 3.0, vertical: 4),
            child: Row(
              children: [
                const Icon(
                  Icons.location_pin,
                  size: 13,
                ),
                const SizedBox(
                  width: 2,
                ),
                ConstrainedBox(
                  constraints: BoxConstraints(
                    minWidth: 0,
                    maxWidth: width * 0.35,
                  ),
                  child: Text(
                    circle.name,
                    textWidthBasis: TextWidthBasis.longestLine,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(fontSize: 11, color: Colors.black),
                  ),
                ),
              ],
            ),
          );
  }

  final _shareIconTuturial = GetIt.I.get<ShareIconTutorial>();
  void _onShare() {
    _shareIconTuturial.onInteractedWithShareIcon();
  }

  void _onHighlightCompleted() {
    _shareIconTuturial.onShareIconNudgeShown();
  }

  getReactionBarWidget({EdgeInsets padding = EdgeInsets.zero}) {
    if (widget.isParentPostPreview) {
      return const SizedBox();
    }
    return Padding(
      padding: padding,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Expanded(
            child: _ActionBar(
              source: widget.source,
              scrollToComments: widget.scrollToComments,
              showTrendTutorial: showTrendTutorial,
              post: widget.post,
              onTrend: onTrendClickCallback,
              tappable: widget.tappable,
              isShare: widget.isShare,
              onShare: _onShare,
              onLike: widget.onLike,
              onUnLike: widget.onUnLike,
              highlightShareAction: _highlightShareAction,
              commentFocusNode: widget.commentFocusNode,
              postBloc: postBloc,
              heroTag: widget.heroTag,
              analyticsParams: _getAnalyticsParams(),
            ),
          ),
        ],
      ),
    );
  }

  getPreviewWidget(
      {required double width, EdgeInsets padding = EdgeInsets.zero}) {
    final parentPost = widget.post.parentPost;
    if (widget.post.photos.isNotEmpty ||
        (widget.post.videos.isNotEmpty) ||
        !widget.showParentPostPreview) {
      return const SizedBox();
    }
    return !widget.isOpinion && parentPost != null
        ? Padding(
            padding: padding + const EdgeInsets.only(top: 5, bottom: 3),
            child: ParentPostPreview(
              parentPost: parentPost,
              width: width - padding.horizontal,
            ),
          )
        : widget.post.link != null
            ? Padding(
                padding: padding + const EdgeInsets.only(top: 5),
                child: PostLinkPreviewWidget(link: widget.post.link!))
            : const SizedBox();
  }

  logVideoPlayEvent() {
    AppAnalytics.logEvent(
        name: "clicked_play_video", parameters: {"post_id": widget.post.id});
  }

  logImageOpenEvent() {
    AppAnalytics.logEvent(name: "clicked_image", parameters: {
      "post_id": widget.post.id,
      "no_of_images": widget.post.photos.length
    });
  }

  Widget _videoContainer(Video video) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(7),
      ),
      width: double.infinity,
      height: 200,
      margin: const EdgeInsets.only(top: 5.0),
      child: FlickMultiPlayer(
        video: video,
        isFirstPostVideo: widget.isFirstElementInList,
        loop: GetIt.I.get<AppConstants>().videoLoopAfterCompletion,
        buildContext: context,
        post: widget.post,
        flickMultiManager: GetIt.I.get<AppConstants>().flickMultiManager,
        image: video.thumbnailUrl,
        source: widget.source,
        autoPlay: !widget.isParentPostPreview &&
            GetIt.I.get<AppConstants>().videoAutoPlay,
      ),
    );
  }

  void _goToPostDetail() async {
    final navigator = Navigator.of(context);
    if (widget.tappable) {
      Map<String, dynamic> param;
      if (widget.source != null) {
        param = {"post_id": widget.post.id.toString(), "source": widget.source};
        if (widget.user.badge != null) {
          param["badge_holder"] = "Yes";
        } else {
          param["badge_holder"] = "No";
        }
        if (widget.post.videos.isNotEmpty) {
          param["format"] = "Contains Video";
        } else if (widget.post.photos.isNotEmpty) {
          param["format"] = "Contains Image";
        } else {
          param["format"] = "Only Text";
        }
        AppAnalytics.logEvent(name: "view_post", parameters: param);
      }

      if (FocusScope.of(context).hasFocus) {
        FocusScope.of(context).unfocus();
      }
      // await Future.delayed(const Duration(milliseconds: 100));
      navigator.push(
        MaterialPageRoute(
          builder: (context) => PostDetail(
            id: widget.post.id,
            source: widget.source,
            preloadedPost: widget.post,
            circle: widget.circle,
            isShare: widget.isShare,
            user: widget.user,
            heroTag: widget.heroTag,
          ),
        ),
      );
    }
  }

  userSelectedOption(PostOptions? selectedOption) async {
    if (selectedOption != null) {
      switch (selectedOption) {
        case PostOptions.reportPost:
          PostActions.reportReason(context: context, post: widget.post);
          break;
        case PostOptions.share:
          PostActions.postShare(
            isShare: widget.isShare,
            post: widget.post,
            context: context,
          );
          break;
        case PostOptions.commentSettings:
          AppAnalytics.logEvent(
              name: "open_comment_options", parameters: {"source": "post"});
          PostActions.showCommentOptions(
              context: context, post: widget.post, postBloc: postBloc);
          break;
        case PostOptions.delete:
          PostActions.confirmDelete(
            context: context,
            post: widget.post,
            source: widget.source,
            postBloc: postBloc,
          );
          break;
        case PostOptions.follow:
          final success = await userFollowViewModel.followUser("post");
          if (success) {
            widget.post.user.follows = true;
          }
          postBloc.add(PostUpdated(widget.post));
          break;
        case PostOptions.unFollow:
          final success = await userFollowViewModel.unfollowUser();
          if (success) {
            widget.post.user.follows = false;
          }
          postBloc.add(PostUpdated(widget.post));
          break;
        case PostOptions.blockUser:
          // ignore: use_build_context_synchronously
          PostActions.userBlockReason(
            context: context,
            post: widget.post,
            postBloc: postBloc,
          );
          break;
        case PostOptions.reportUser:
          // ignore: use_build_context_synchronously
          PostActions.userReportReason(post: widget.post, context: context);
          break;
        default:
          break;
      }
    } else {
      return;
    }
  }

  Widget getPopUpMenuButton() {
    return InkWell(
      onTap: () async {
        final PostOptions? selectedOption = await context.showPostOptions(
            post: widget.post,
            isSuperAdmin: _userPermissions.contains("super_admin"));
        userSelectedOption(selectedOption);
      },
      child: const Padding(
        padding: EdgeInsets.only(top: 8.0, bottom: 8.0, left: 4.0, right: 4.0),
        child: Icon(
          Icons.more_vert_rounded,
          size: 24,
          color: Styles.dimIconColor,
        ),
      ),
    );
  }

  getTimeAgoTextWidget() {
    return Text(timeago.format(widget.post.createdAt, locale: 'te_short'),
        style: const TextStyle(
          fontSize: 12.0,
          color: Styles.dimTextColor,
        ));
  }

  getUserNameWidget() {
    double fontSize = widget.isParentPostPreview ? 15.0 : 16.0;
    if (!widget.post.user.isUserHaveBadgeBanner()) {
      fontSize = fontSize * 1.025;
    }
    return InkWell(
      onTap: !widget.isShare
          ? () {
              widget.user.loggedInUser
                  ? widget.disableProfileClick
                      ? null
                      : Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const MyProfilePage(
                              source: "post_user_name",
                            ),
                          ),
                        )
                  : Utils.openUserPage(context, widget.user.id,
                      source: "post_user_name");
            }
          : null,
      child: Text(
        widget.post.user.name,
        textWidthBasis: TextWidthBasis.longestLine,
        maxLines: 1,
        overflow: TextOverflow.fade,
        softWrap: false,
        style: TextStyle(
          fontSize: fontSize,
          fontWeight: FontWeight.bold,
        ).adjustBasedOnLocale(widget.post.user.name),
      ),
    );
  }

  getMediaWidget(BuildContext context, {EdgeInsets padding = EdgeInsets.zero}) {
    if (widget.post.isPoll) return const SizedBox();

    if (widget.post.videos.isNotEmpty) {
      List<Widget> media = [];
      List<StaggeredTile> mediaStaggeredTiles = [];

      for (var video in widget.post.videos) {
        media.add(_videoContainer(video));
        mediaStaggeredTiles.add(
          StaggeredTile.count(
            2,
            video.mode == VideoMode.portrait ? 2.5 : 1.2,
          ),
        );
      }

      final mediaWidget = Padding(
        padding: padding,
        child: StaggeredGridView.count(
          shrinkWrap: true,
          primary: false,
          crossAxisCount: 2,
          mainAxisSpacing: 4.0,
          crossAxisSpacing: 4.0,
          physics: const NeverScrollableScrollPhysics(),
          staggeredTiles: mediaStaggeredTiles,
          padding: EdgeInsets.zero,
          children: media,
        ),
      );

      return mediaWidget;
    } else if (widget.post.photos.isNotEmpty) {
      final mediaCarouselItems = widget.post.photos
          .map((photo) => MediaCarouselItem.fromPhoto(photo))
          .toList();

      return MediaCarousel(
          items: mediaCarouselItems,
          width: width,
          onItemTap: widget.isShare
              ? null
              : (index) {
                  logImageOpenEvent();
                  _openPostPhoto(widget.post.photos, index);
                },
          padding: padding);
    } else {
      return const SizedBox();
    }
  }

  getUserBadgeStrip(User user) {
    final badge = user.badge;
    if (badge != null) {
      if (badge.active) {
        if (badge.badgeBanner == BadgeBanner.none) {
          return BadgeBannerTag(badge: user.badge);
        }
        return GestureDetector(
          onTap: () {
            onUserTapped(context, source: "post_user_badge");
          },
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 13),
                child: BadgeStripProfile(
                  user.badge,
                  height: 14,
                  fontSize: 9,
                  maxWidth: MediaQuery.of(context).size.width * 0.6,
                  minWidth: 80,
                ),
              ),
            ],
          ),
        );
      }
    }
    return const SizedBox();
  }
}

class ParentPostPreview extends StatefulWidget {
  final Post parentPost;
  final double width;

  const ParentPostPreview(
      {super.key, required this.parentPost, required this.width});

  @override
  ParentPostPreviewState createState() => ParentPostPreviewState();
}

class ParentPostPreviewState extends State<ParentPostPreview> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.parentPost.active) {
      return Container(
        height: 50,
        margin: const EdgeInsets.only(top: 5.0),
        decoration: BoxDecoration(
            border: Border.all(color: Theme.of(context).dividerColor),
            borderRadius: BorderRadius.circular(3)),
        padding: const EdgeInsets.fromLTRB(10, 5.0, 10.0, 5.0),
        child: Center(
          child: Text(
            context.getString(StringKey.thisPostDeletedText),
            style: const TextStyle(color: Colors.grey),
          ),
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.only(top: 5.0),
      decoration: BoxDecoration(
          color: Colors.white54,
          border: Border.all(color: Theme.of(context).dividerColor),
          borderRadius: BorderRadius.circular(8)),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: PostListItem(
          post: widget.parentPost,
          width: widget.width,
          isParentPostPreview: true,
          user: widget.parentPost.user,
          showParentPostPreview: false,
        ),
      ),
    );
  }
}

// ignore: must_be_immutable
class _ActionBar extends StatefulWidget {
  final Post post;
  final bool tappable;
  final bool isShare;
  final bool showTrendTutorial;
  final VoidCallback? onShare;
  final VoidCallback? onTrend;
  final VoidCallback? onLike;
  final VoidCallback? onUnLike;
  final Function? scrollToComments;
  bool highlightShareAction;
  final FocusNode? commentFocusNode;
  String? source;
  PostBloc? postBloc;
  final String heroTag;
  Map<String, dynamic> analyticsParams;

  _ActionBar({
    this.scrollToComments,
    required this.post,
    this.showTrendTutorial = false,
    this.tappable = true,
    this.isShare = false,
    this.onShare,
    this.onTrend,
    this.onLike,
    this.onUnLike,
    this.highlightShareAction = false,
    this.commentFocusNode,
    this.source,
    this.postBloc,
    this.analyticsParams = const {},
    this.heroTag = "post_list_item",
  });

  @override
  __ActionBarState createState() => __ActionBarState();
}

class __ActionBarState extends State<_ActionBar> with TickerProviderStateMixin {
  late Post post;

  late AnimationController scaleInAnimationController;
  late AnimationController scaleOutAnimationController;
  double likeIconScale = 1.0;

  bool _whatsAppSharing = false;
  bool tappable = true;
  List<String> permissions = [];
  double actionIconBoxWidth = 40;
  GlobalKey trendKey = GlobalKey(); // declare a global key

  double? x, y;
  late PostBloc postBloc;

  MessagingConfig messagingConfig = GetIt.I.get<MessagingConfig>();
  final PostService _postService = GetIt.I.get<PostService>();

  @override
  void initState() {
    super.initState();
    post = widget.post;
    tappable = widget.tappable;
    postBloc = widget.postBloc ?? BlocProvider.of<PostBloc>(context);
    // like scale out animation config
    scaleOutAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 100),
    );
    final CurvedAnimation scaleOutCurve = CurvedAnimation(
      parent: scaleOutAnimationController,
      curve: Curves.easeOutCubic,
    );
    final scaleOutAnimation =
        Tween<double>(begin: 1.5, end: 1.0).animate(scaleOutCurve);
    scaleOutAnimation.addListener(
        () => setState(() => likeIconScale = scaleOutAnimation.value));

    // like scale in animation config
    scaleInAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 150),
    );
    final CurvedAnimation scaleInCurve = CurvedAnimation(
      parent: scaleInAnimationController,
      curve: Curves.easeInOutBack,
    );
    final scaleInAnimation =
        Tween<double>(begin: 1.0, end: 1.5).animate(scaleInCurve)
          ..addStatusListener((status) {
            if (status == AnimationStatus.completed) {
              scaleOutAnimationController.forward(from: 0.0);
            }
          });
    scaleInAnimation.addListener(
        () => setState(() => likeIconScale = scaleInAnimation.value));
  }

  @override
  void dispose() {
    scaleInAnimationController.dispose();
    scaleOutAnimationController.dispose();
    super.dispose();
  }

  void _circlePost() async {
    AppAnalytics.logEvent(
      name: "create_share_begin",
      parameters: widget.analyticsParams,
    );
    Navigator.of(context).push(MaterialPageRoute(
        builder: (context) => CreatePostPage(
              resharedPost: post,
              onSuccess: () {
                post.opinionsCount += 1;
                //updating the post in PostBloc as it is reshared by user, we need to increase count of reshare all over the app
                postBloc.add(PostUpdated(post));
                AppAnalytics.logEvent(
                  name: "create_share_completed",
                  parameters: {"parent_post_id": post.id},
                );
              },
              source: 'actionbar_repost',
            )));
  }

  Future<void> _likePostRequest() async {
    try {
      Map<String, dynamic> param = {"post id": widget.post.id.toString()};
      param["source"] = "Post";
      if (widget.source != null) {
        param["feed"] = widget.source;
      } else {
        param["feed"] = "Post page";
      }
      if (widget.source == "Circle Feed") {
        param["circle_type"] = widget.post.circle?.type;
        param["circle_name"] = widget.post.circle?.name;
      }

      if (widget.post.customProperties.isNotEmpty) {
        param.addAll(widget.post.customProperties);
      }
      AppConstants appConstants = GetIt.I.get<AppConstants>();

      if (appConstants.trendFeedBack == true) {
        showTrendFeedback();
        appConstants.trendTutorial = false;
        appConstants.trendFeedBack = false;
      }

      HapticFeedback.mediumImpact();
      if (widget.onTrend != null) {
        widget.onTrend!();
      }

      AppAnalytics.logEvent(name: "add_trend", parameters: param);

      setState(() {
        post.userLiked = true;
        post.likesCount++;
      });
      //updating the post in PostBloc as it is liked by user, we need to make it is liked over all app
      postBloc.add(PostUpdated(post));
      if (widget.onLike != null) {
        widget.onLike!();
      }

      scaleInAnimationController.forward(from: 0.0);

      final success = await _postService.likePost(post);

      if (!success) {
        post.userLiked = false;
        post.likesCount--;
        //updating the post in PostBloc as it is unliked by user, we need to make it is unliked over all app
        postBloc.add(PostUpdated(post));

        if (widget.onUnLike != null) {
          widget.onUnLike!();
        }
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text("Already liked!"),
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
      if (mounted) {
        postBloc.add(PostUpdated(post));
      }
    } catch (e) {
      Utils.showToast(localisedErrorMessage(e));
      post.userLiked = false;
      post.likesCount--;
    }
  }

  showTrendFeedback() {
    AchievementView(
      context,
      color: Colors.grey[50]!,
      alignment: const Alignment(0, 0.7),
      textStyleSubTitle: const TextStyle(color: Colors.black, fontSize: 16),
      icon: const Icon(
        Icons.whatshot,
        color: Colors.orange,
      ),
      title: '',
      subTitle: GetIt.I.get<AppConstants>().trendFeedbackText,
    ).show();
  }

  Future<void> _share(BuildContext context) {
    AppAnalytics.onPostShareClick(postId: post.id, source: widget.source);
    return ShareSheet.show(
        context, SharedEntity.post(post, widget.analyticsParams));
  }

  Future<void> _sharePostToWhatsappRequest() async {
    try {
      setState(() {
        _whatsAppSharing = true;
        post.whatsappCount++;
      });

      await _postService.shareToWhatsapp(post);
      //updating the post in PostBloc as it is shared by user, we need to increase the whatsapp share count
      //all over the application
      postBloc.add(PostUpdated(post));
      if (mounted) {
        Navigator.push(
          context,
          PageSlideRight(
            page: PostShare(
              post: post,
              toWhatsApp: true,
              onShare: () {
                AppAnalytics.logShare(
                  contentType: "post",
                  itemId: post.id.toString(),
                  method: "whatsapp",
                  otherParameters: widget.analyticsParams,
                );
                setState(() {
                  _whatsAppSharing = false;
                });
              },
            ),
          ),
        );
      }
    } catch (e) {
      Utils.showToast(localisedErrorMessage(e));
    }
  }

  Future<void> _unlikePostRequest() async {
    AppAnalytics.logEvent(
      name: "remove_trend",
      parameters: {
        "content_type": "post",
        "item_id": post.id.toString(),
      },
    );
    setState(() {
      post.userLiked = false;
      post.likesCount -= 1;
    });

    if (widget.onUnLike != null) {
      widget.onUnLike!();
    }
    //updating the post in PostBloc as it is liked by user, we need to make it is liked over all app
    postBloc.add(PostUpdated(post));
    try {
      final success = await _postService.unlikePost(post);

      if (!success) {
        post.userLiked = true;
        post.likesCount++;
        postBloc.add(PostUpdated(post));
        if (widget.onLike != null) {
          widget.onLike!();
        }
        post.likesCount += 1;
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text("Already unliked!"),
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      Utils.showToast(localisedErrorMessage(e));
      post.userLiked = true;
      post.likesCount++;
    }
  }

  void _showLikedUsers() async {
    AppAnalytics.logEvent(
      name: "show_trends",
      parameters: {
        "content_type": "post",
        "item_id": post.id.toString(),
      },
    );

    Navigator.push(
      context,
      PageSlideRight(
        page: TrendedUsers(
          trendedUsersType: TrendedUsersType.Post,
          post: post,
        ),
      ),
    );
  }

  void _showOpinions() async {
    AppAnalytics.logEvent(
      name: "show_opinions",
      parameters: {
        "content_type": "post",
        "item_id": post.id.toString(),
      },
    );

    Navigator.push(
      context,
      PageSlideRight(
        page: ShareSections(post),
      ),
    );
  }

  isCommentsEnabled() {
    if (widget.post.commentsEnabled) {
      return true;
    }

    return false;
  }

  @override
  Widget build(BuildContext context) {
    actionIconBoxWidth = (MediaQuery.of(context).size.width * 0.83 - 15) / 4;
    return Column(
      children: [
        !tappable
            ? Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  post.commentsCount > 0
                      ? InkWell(
                          onTap: () {
                            if (widget.scrollToComments != null) {
                              widget.scrollToComments!();
                            }
                          },
                          child: Container(
                            height: 35,
                            padding: const EdgeInsets.only(right: 10),
                            alignment: Alignment.center,
                            child: RichText(
                              text: TextSpan(
                                text: "",
                                style: DefaultTextStyle.of(context).style,
                                children: <TextSpan>[
                                  TextSpan(
                                    text: post.commentsCount.toString(),
                                    style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 11),
                                  ),
                                  const TextSpan(
                                      text: ' ',
                                      style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 11)),
                                  TextSpan(
                                    text: context.getPluralizedString(
                                        StringKey.commentLabel,
                                        post.commentsCount),
                                    style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 11), // Comments
                                  ),
                                ],
                              ),
                            ),
                          ),
                        )
                      : const SizedBox(),
                  post.opinionsCount > 0
                      ? InkWell(
                          onTap: _showOpinions,
                          child: Container(
                            height: 35,
                            padding: const EdgeInsets.only(right: 10),
                            alignment: Alignment.center,
                            child: RichText(
                              text: TextSpan(
                                text: "",
                                style: DefaultTextStyle.of(context).style,
                                children: <TextSpan>[
                                  TextSpan(
                                    text: post.opinionsCount.toString(),
                                    style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 11),
                                  ),
                                  const TextSpan(
                                      text: ' ',
                                      style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 11)),
                                  TextSpan(
                                    text: context.getPluralizedString(
                                        StringKey.opinionLabel,
                                        post.opinionsCount),
                                    style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 11), // Shares
                                  ),
                                ],
                              ),
                            ),
                          ),
                        )
                      : const SizedBox(),
                  post.likesCount > 0
                      ? InkWell(
                          onTap: _showLikedUsers,
                          child: Container(
                            height: 35,
                            padding: const EdgeInsets.only(right: 10),
                            alignment: Alignment.center,
                            child: RichText(
                              text: TextSpan(
                                text: "",
                                style: DefaultTextStyle.of(context).style,
                                children: <TextSpan>[
                                  TextSpan(
                                    text: post.likesCount.toString(),
                                    style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 11),
                                  ),
                                  const TextSpan(
                                      text: ' ',
                                      style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 11)),
                                  TextSpan(
                                    text: context.getPluralizedString(
                                        StringKey.trendLabel, post.likesCount),
                                    style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 11),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        )
                      : const SizedBox(),
                ],
              )
            : Container(),
        !tappable &&
                (post.likesCount > 0 ||
                    post.opinionsCount > 0 ||
                    post.commentsCount > 0)
            ? const Padding(
                padding: EdgeInsets.only(bottom: 5),
                child: Divider(
                  thickness: 0.5,
                  height: 2.5,
                  color: Color(0xffC6C6C6),
                ),
              )
            : Container(
                height: 3,
              ),
        Stack(
          clipBehavior: Clip.none,
          children: [
            Padding(
              padding: const EdgeInsets.only(bottom: 3, top: 3),
              child: Row(
                key: trendKey,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Expanded(
                      child: InkWell(
                    onTap: () {
                      if (tappable == true) {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => PostDetail(
                              id: post.id,
                              preloadedPost: post,
                              heroTag: widget.heroTag,
                              requestCommentBoxFocusNode: true,
                            ),
                          ),
                        );
                      } else {
                        if (!isCommentsEnabled()) {
                          final commentsConfig = post.commentsConfig;
                          if (commentsConfig != null &&
                              commentsConfig.commentsDisabledText.isNotEmpty) {
                            Utils.showSnackBarMessage(
                                context, commentsConfig.commentsDisabledText);
                          }
                        } else {
                          widget.commentFocusNode?.requestFocus();
                        }
                      }
                    },
                    child: Container(
                      width: actionIconBoxWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 3),
                      height: 20,
                      child: Row(
                        children: <Widget>[
                          Container(
                            padding: const EdgeInsets.only(right: 5),
                            height: 18,
                            child: Image.asset(
                                "assets/images/action/comment_icon.png",
                                color: !isCommentsEnabled()
                                    ? const Color(0xff757575).withOpacity(0.3)
                                    : const Color(0xff757575)),
                          ),
                          Expanded(
                              child: post.commentsCount > 0
                                  ? Container(
                                      padding: const EdgeInsets.only(left: 2),
                                      alignment: Alignment.centerLeft,
                                      child: Text(
                                        NumberFormat.compact()
                                            .format(widget.post.commentsCount),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        style: const TextStyle(
                                          fontSize: 12.0,
                                          color: Styles.dimTextColor,
                                        ),
                                      ))
                                  : Container())
                        ],
                      ),
                    ),
                  )),
                  const SizedBox(
                    width: 2,
                  ),
                  Expanded(
                      child: InkWell(
                    onLongPress: _showOpinions,
                    onTap: !widget.isShare ? _circlePost : () {},
                    child: Container(
                      width: actionIconBoxWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 3),
                      height: 20,
                      child: Row(
                        children: <Widget>[
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 5),
                            height: 18,
                            child: Image.asset(
                              "assets/images/action/repost_icon.png",
                              color: (post.userCircled)
                                  ? Colors.green
                                  : const Color(0xff757575),
                            ),
                          ),
                          Expanded(
                              child: post.opinionsCount > 0
                                  ? Container(
                                      padding: const EdgeInsets.only(left: 2),
                                      alignment: Alignment.centerLeft,
                                      child: Text(
                                        NumberFormat.compact()
                                            .format(post.opinionsCount),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                          fontSize: 12.0,
                                          color: (post.userCircled)
                                              ? Colors.green
                                              : Styles.dimTextColor,
                                        ),
                                      ),
                                    )
                                  : Container()),
                        ],
                      ),
                    ),
                  )),
                  const SizedBox(
                    width: 2,
                  ),
                  Expanded(
                    child: InkWell(
                      onLongPress: _showLikedUsers,
                      onTap: (post.userLiked)
                          ? !widget.isShare
                              ? _unlikePostRequest
                              : () {}
                          : !widget.isShare
                              ? _likePostRequest
                              : () {},
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 3),
                        width: actionIconBoxWidth,
                        height: 20,
                        child: Stack(
                          clipBehavior: Clip.none,
                          children: [
                            Row(
                              children: <Widget>[
                                (post.userLiked)
                                    ? Transform.scale(
                                        scale: likeIconScale,
                                        child: InkWell(
                                          onTap: !widget.isShare
                                              ? _unlikePostRequest
                                              : () {},
                                          child: Container(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 5),
                                            height: 18,
                                            child: Image.asset(
                                                "assets/images/action/trend_active_icon.png"),
                                          ),
                                        ),
                                      )
                                    : InkWell(
                                        onTap: !widget.isShare
                                            ? _likePostRequest
                                            : () {},
                                        child: Container(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 5),
                                          height: 18,
                                          child: Image.asset(
                                              "assets/images/action/trend_icon.png",
                                              color: const Color(0xff757575)),
                                        ),
                                      ),
                                Expanded(
                                    child: post.likesCount > 0
                                        ? Container(
                                            padding:
                                                const EdgeInsets.only(left: 2),
                                            alignment: Alignment.centerLeft,
                                            child: Text(
                                              NumberFormat.compact()
                                                  .format(post.likesCount),
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                              style: TextStyle(
                                                fontSize: 12.0,
                                                color: (post.userLiked)
                                                    ? Styles.circleOrange
                                                    : Styles.dimTextColor,
                                              ),
                                            ),
                                          )
                                        : Container()),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(
                    width: 2,
                  ),
                  if (messagingConfig.isEnabled)
                    Expanded(
                        child: InkWell(
                      onTap: widget.isShare
                          ? null
                          : () {
                              _share(context);
                              widget.onShare?.call();
                            },
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 3),
                        width: actionIconBoxWidth,
                        height: 20,
                        child: Row(
                          children: <Widget>[
                            Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 5),
                                child: ShareIcon(
                                  size: 20,
                                  color: const Color(0xFF757575),
                                  highlightColor:
                                      Theme.of(context).primaryColor,
                                  highlight: widget.highlightShareAction,
                                )),
                            Expanded(
                                child: post.whatsappCount > 0
                                    ? Container(
                                        padding: const EdgeInsets.only(left: 2),
                                        alignment: Alignment.centerLeft,
                                        child: Text(
                                          NumberFormat.compact().format(
                                            post.whatsappCount,
                                          ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                          style: const TextStyle(
                                            fontSize: 12.0,
                                            color: Styles.dimTextColor,
                                          ),
                                        ),
                                      )
                                    : Container())
                          ],
                        ),
                      ),
                    )),
                  if (!messagingConfig.isEnabled)
                    Expanded(
                        child: InkWell(
                      onTap: widget.isShare
                          ? null
                          : () {
                              if (!_whatsAppSharing) {
                                _sharePostToWhatsappRequest();
                                widget.onShare?.call();
                              }
                            },
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 3),
                        width: actionIconBoxWidth,
                        height: 20,
                        child: Row(
                          children: <Widget>[
                            widget.highlightShareAction
                                ? RubberBand(
                                    child: Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 5),
                                    height: 18,
                                    child: Image.asset(
                                        "assets/images/action/whatsapp_active_icon.png"),
                                  ))
                                : Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 5),
                                    height: 18,
                                    child: Image.asset(
                                      "assets/images/action/whatsapp_icon.png",
                                      color: const Color(0xff757575),
                                    ),
                                  ),
                            Expanded(
                                child: post.whatsappCount > 0
                                    ? Container(
                                        padding: const EdgeInsets.only(left: 2),
                                        alignment: Alignment.centerLeft,
                                        child: Text(
                                          NumberFormat.compact().format(
                                            post.whatsappCount,
                                          ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                          style: const TextStyle(
                                            fontSize: 12.0,
                                            color: Styles.dimTextColor,
                                          ),
                                        ),
                                      )
                                    : Container())
                          ],
                        ),
                      ),
                    )),
                  const SizedBox(
                    width: 2,
                  ),
                ],
              ),
            )
          ],
        ),
        !tappable
            ? const Divider(
                thickness: 0.5,
                // height: 2.5,
                color: Color(0xffC6C6C6),
              )
            : Container(),
      ],
    );
  }
}
