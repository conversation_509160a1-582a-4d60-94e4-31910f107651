import 'dart:io';
import 'dart:typed_data';

import 'package:auto_size_text/auto_size_text.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:clipboard/clipboard.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animator/flutter_animator.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get_it/get_it.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:image_picker/image_picker.dart';
import 'package:praja/common/take_screenshot.dart';
import 'package:praja/features/user/ui/app_user_editable_avatar.dart';
import 'package:praja/features/whatsapp_share/whatsapp_share.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/models/custom_gradient.dart';
import 'package:praja/models/post.dart';
import 'package:praja/models/share_card_text.dart';
import 'package:praja/models/user.dart';
import 'package:praja/models/user_identity.dart';
import 'package:praja/models/v2/photo.dart';
import 'package:praja/models/video.dart';
import 'package:praja/presentation/user_avatar.dart';
import 'package:praja/screens/posts/share_card/badge_banner_strip.dart';
import 'package:praja/screens/posts/share_card/message_bubble.dart';
import 'package:praja/screens/posts/share_card/post_text_content.dart';
import 'package:praja/services/app_cache_manager.dart';
import 'package:praja/services/post/post_service.dart';
import 'package:praja/utils/utils.dart';
import 'package:praja/utils/widgets.dart';
import 'package:share_plus/share_plus.dart';

class PostShareCardScreen extends StatefulWidget {
  final Post post;
  final User user;
  final AssetImage uploadProfilePicImage;
  final AssetImage appPostShareCardIcon;
  final bool isShare;
  final Function? afterUserProfileUpdateCallback;
  final double screenWidth;
  final Function(bool)? mediaLoadCallBack;

  const PostShareCardScreen({
    super.key,
    required this.post,
    this.isShare = false,
    required this.user,
    required this.uploadProfilePicImage,
    required this.screenWidth,
    required this.appPostShareCardIcon,
    this.afterUserProfileUpdateCallback,
    this.mediaLoadCallBack,
  });

  @override
  State<PostShareCardScreen> createState() => _PostShareCardScreen();
}

class _PostShareCardScreen extends State<PostShareCardScreen> {
  late final Post post;
  late User user;

  bool videoPreviewLoaded = false;
  Map<String, bool> imageLoadingFlags = {};
  bool mediaLoaded = false;

  ImageProvider? backgroundImage;
  late ShareCardText shareCardText;
  late CustomGradient customGradientValues;
  double headerHeight = 0;
  double footerHeight = 0;
  double contentMaxHeight = 0;
  double contentMinHeight = 0;
  int initialMaxLines = 5;
  double avatarRadius = 35;
  int maxLines = 0;
  int paddingAndMarginSizeInTextContainer = 20;
  bool isProfilePicLoading = false;
  double textHeight = 0;
  final TakeScreenshotController _takeScreenshotController =
      TakeScreenshotController();
  final PostService _postService = GetIt.I.get<PostService>();

  TextStyle contentStyle = const TextStyle(
    color: Colors.black,
    fontWeight: FontWeight.w400,
    fontSize: 17.0,
  );
  bool isDownLoading = false;
  bool isWhatsappShareLoading = false;
  bool showImageUploadMsg = false;
  bool isCurrentLoggedInUser = false;

  final double maxWidthFromDesign = 630;
  final double maxHeaderHeightFromDesign = 51;
  final double maxFooterHeightForBadgeUserFromDesign = 108;
  final double maxFooterHeightForNonBadgeUserFromDesign = 72;
  final double maxContentSizeFromDesign = 861;
  final double minContentSizeFromDesign = 570;
  final double appIconAspectRatioFromDesign = 54 / 76.85;
  final GlobalKey<AnimatorWidgetState> basicAnimation =
      GlobalKey<AnimatorWidgetState>();

  @override
  void initState() {
    super.initState();
    post = widget.post;
    user = widget.user;
    shareCardText = post.shareCardText;
    customGradientValues = post.customGradientValues;
    initiateMaxHeights();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          showImageUploadMsg = true;
        });
      }
    });
    Future.delayed(const Duration(seconds: 4), () {
      if (mounted) {
        setState(() {
          showImageUploadMsg = false;
        });
      }
    });
  }

  beforeProfileUpload() {
    if (mounted) {
      setState(() {
        isProfilePicLoading = true;
        isWhatsappShareLoading = true;
        showImageUploadMsg = false;
        isDownLoading = true;
      });
    }
  }

  getMaxNumberOfLines(TextStyle contentStyle) {
    final content = post.content;
    if (content == null) {
      return 0;
    }
    final boldRegex = RegExp(r'(\*)([^\*]+)(\*)(?=\s|$)');
    List<TextSpan> textSpans = [];
    String nonBoldText = content;
    Iterable allMatches = boldRegex.allMatches(content);
    String boldText;
    for (var match in allMatches) {
      boldText = content.substring(match.start, match.end);
      textSpans.add(TextSpan(
          text: boldText, style: const TextStyle(fontWeight: FontWeight.bold)));
      nonBoldText.replaceAll(boldText, "");
    }
    final span =
        TextSpan(text: nonBoldText, children: textSpans, style: contentStyle);
    final tp = TextPainter(text: span, textDirection: TextDirection.ltr);
    tp.layout(
        maxWidth: widget.screenWidth - paddingAndMarginSizeInTextContainer);
    setState(() {
      textHeight = tp.height / tp.computeLineMetrics().length;
    });
    return tp.computeLineMetrics().length;
  }

  initiateMaxHeights() {
    double width = widget.screenWidth;
    bool isSmallTextOnly = false;
    avatarRadius = isBadgeUser(user) ? width * 0.095 : width * 0.082;
    headerHeight = width / (maxWidthFromDesign / maxHeaderHeightFromDesign);
    footerHeight = isBadgeUser(user)
        ? (width / (maxWidthFromDesign / maxFooterHeightForBadgeUserFromDesign))
        : (width /
            (maxWidthFromDesign / maxFooterHeightForNonBadgeUserFromDesign));
    contentMaxHeight = width /
        (maxWidthFromDesign /
            (maxContentSizeFromDesign - headerHeight - footerHeight));
    contentMinHeight = width /
        (maxWidthFromDesign /
            (minContentSizeFromDesign - headerHeight - footerHeight));
    maxLines = getMaxNumberOfLines(contentStyle);
    if (maxLines <= initialMaxLines && !isMediaPresent()) {
      isSmallTextOnly = true;
      contentStyle = const TextStyle(
        fontSize: 20,
        color: Colors.black,
        fontWeight: FontWeight.w600,
      );
      maxLines = getMaxNumberOfLines(contentStyle);
      initialMaxLines = maxLines;
    } else if (maxLines > initialMaxLines && !isMediaPresent()) {
      initialMaxLines = 18;
    } else if (maxLines <= initialMaxLines && isMultiMedia()) {
      initialMaxLines = maxLines;
    } else if (maxLines > initialMaxLines && isMultiMedia()) {
      initialMaxLines = 4;
    } else if (isMultiMedia()) {
      initialMaxLines = 3;
    }

    if (textHeight * initialMaxLines >= contentMaxHeight - 50 ||
        textHeight > 24 && !isSmallTextOnly) {
      initialMaxLines = initialMaxLines - 2;
    }
  }

  bool hasText() {
    final content = post.content;
    return (content != null && content.trim().isNotEmpty);
  }

  isMediaPresent() {
    if (post.photos != null && post.photos.isNotEmpty ||
        (post.videos != null && post.videos.isNotEmpty) ||
        post.link != null) {
      return true;
    }
    return false;
  }

  bool isNotAMediaPost() {
    return post.photos.isEmpty && post.videos.isEmpty;
  }

  isMultiMedia() {
    return post.photos != null && post.photos.length > 1;
  }

  hasVideo() {
    return post.videos != null && post.videos.isNotEmpty;
  }

  bool isBadgeUser(User user) {
    final badge = user.badge;
    if (badge != null && badge.active) {
      return true;
    }
    return false;
  }

  profilePicCheck() {
    return user.hasProfilePicture && user.photo != null;
  }

  checkLoggedInUser() {
    return post.user.loggedInUser != null && post.user.loggedInUser;
  }

  checkForShowingCameraIcon() {
    return checkLoggedInUser() &&
        !profilePicCheck() &&
        !widget.isShare &&
        !isWhatsappShareLoading &&
        !isDownLoading;
  }

  gradientsBeginAlignment() {
    final gradientDirection = customGradientValues.gradientDirection;
    double x = gradientDirection != null && gradientDirection.beginX != null
        ? gradientDirection.beginX
        : 0.0;
    double y = gradientDirection != null && gradientDirection.beginY != null
        ? gradientDirection.beginY
        : -1;
    return Alignment(x, y);
  }

  gradientsEndAlignment() {
    final gradientDirection = customGradientValues.gradientDirection;
    double x = gradientDirection != null && gradientDirection.endX != null
        ? gradientDirection.endX
        : 0.0;
    double y = gradientDirection != null && gradientDirection.endY != null
        ? gradientDirection.endY
        : 1;
    return Alignment(x, y);
  }

  _headerTextColor() {
    return customGradientValues != null
        ? customGradientValues.headerTextColor
        : Colors.white;
  }

  _footerTextColor() {
    return customGradientValues != null
        ? customGradientValues.footerTextColor
        : Colors.white;
  }

  getTopHeaderText() {
    return shareCardText != null
        ? shareCardText.header
        : "Praja App ‌లో నన్ను ఫాలో అవ్వండి";
  }

  getTopHeader() {
    return AspectRatio(
      aspectRatio: maxWidthFromDesign / maxHeaderHeightFromDesign,
      child: Container(
        padding: const EdgeInsets.only(left: 20, right: 50),
        width: MediaQuery.of(context).size.width,
        alignment: Alignment.center,
        child: AutoSizeText(
          getTopHeaderText() ?? "Praja App ‌లో నన్ను ఫాలో అవ్వండి",
          maxLines: 1,
          minFontSize: 10,
          textScaler: const TextScaler.linear(1.0),
          textAlign: TextAlign.center,
          style: TextStyle(
              color: _headerTextColor(),
              fontSize: 15,
              fontWeight: FontWeight.w700),
        ),
      ),
    );
  }

  getCloseButton() {
    return SizedBox(
      height: 43,
      child: Row(mainAxisAlignment: MainAxisAlignment.end, children: [
        IconButton(
          // color: Colors.red,
          icon: const Icon(
            Icons.cancel,
            color: Colors.black,
            size: 30,
          ),
          onPressed: () {
            Navigator.of(context).pop();
          },
        )
      ]),
    );
  }

  onImageLoad(url) {
    imageLoadingFlags[url] = true;
    checkAllImagesLoaded();
  }

  checkAllImagesLoaded() {
    bool flag = true;
    for (var value in imageLoadingFlags.values) {
      flag = flag && value;
    }
    if (flag) {
      mediaLoadCallBackCheck();
    }
  }

  mediaLoadCallBackCheck() {
    if (widget.mediaLoadCallBack != null && !mediaLoaded) {
      mediaLoaded = true;
      widget.mediaLoadCallBack?.call(true);
    }
  }

  Widget _imageContainer(int index, String url, bool multiImage) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(7),
      child: CachedNetworkImage(
        imageBuilder: (context, imageProvider) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            onImageLoad(url);
          });
          return Container(
            alignment: Alignment.topCenter,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(7),
              image: DecorationImage(
                image: imageProvider,
                fit: BoxFit.cover,
              ),
            ),
          );
        },
        imageUrl: url,
        alignment: Alignment.center,
        fit: BoxFit.cover,
        fadeInDuration: Duration.zero,
        // placeholder: (context, url) => Widgets.imageLoader(context),
        errorWidget: (context, url, error) => const Icon(Icons.error),
        cacheManager: AppCacheManager.instance,
      ),
    );
  }

  Widget _videoContainer(Video video) {
    return Container(
      width: double.infinity,
      height: 200.0,
      margin: const EdgeInsets.only(top: 5.0),
      child: Stack(
        children: [
          CachedNetworkImage(
            imageUrl: video.thumbnailUrl,
            imageBuilder: (context, imageProvider) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                mediaLoadCallBackCheck();
              });
              return Container(
                alignment: Alignment.topCenter,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(7),
                  image: DecorationImage(
                    image: imageProvider,
                    fit: BoxFit.cover,
                  ),
                ),
              );
            },
            fadeInDuration: widget.isShare
                ? const Duration(milliseconds: 0)
                : const Duration(milliseconds: 300),
            alignment: Alignment.topCenter,
            errorWidget: (context, url, error) => const Icon(Icons.error),
            fit: BoxFit.cover,
            cacheManager: AppCacheManager.instance,
          ),
          Center(
            child: Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.black.withOpacity(0.5),
              ),
              child: const Padding(
                  padding: EdgeInsets.all(4),
                  child: Icon(
                    Icons.play_arrow_rounded,
                    color: Colors.white,
                    size: 32,
                  )),
            ),
          ),
        ],
      ),
    );
  }

  getMediaWidget() {
    List<Widget> media = [];
    List<StaggeredTile> mediaStaggeredTiles = [];

    bool multiImage = post.photos.length > 1;
    StaggeredTile? firstImageTile;
    for (var i = 0; i < post.photos.length; i++) {
      Photo photo = post.photos[i];
      imageLoadingFlags[photo.url] = false;
      media.add(_imageContainer(i, photo.url, multiImage));
      if (multiImage) {
        if (mounted) {
          setState(() {
            initialMaxLines = 5;
          });
        }
        if (photo.aspectRatio != null) {
          if (i == 0) {
            if (photo.aspectRatio >= 1) {
              firstImageTile = const StaggeredTile.count(2, 0.8);
            } else {
              firstImageTile = const StaggeredTile.count(1, 1.6);
            }
            mediaStaggeredTiles.add(firstImageTile);
          } else if (post.photos.length == 2) {
            if (firstImageTile == null) {
              if (photo.aspectRatio >= 1) {
                mediaStaggeredTiles.add(const StaggeredTile.count(2, 0.8));
              } else {
                mediaStaggeredTiles.add(const StaggeredTile.count(1, 0.8));
              }
            } else {
              mediaStaggeredTiles.add(firstImageTile);
            }
          } else {
            mediaStaggeredTiles.add(const StaggeredTile.count(1, 0.8));
          }
        } else {
          mediaStaggeredTiles.add(const StaggeredTile.count(1, 0.8));
        }
      } else {
        if (photo.aspectRatio <= 1) {
          return Expanded(
            child: ClipRRect(
              child: Padding(
                padding: const EdgeInsets.all(5),
                child: CachedNetworkImage(
                  imageUrl: post.photos.first.url,
                  alignment: Alignment.topCenter,
                  fit: BoxFit.cover,
                  imageBuilder: (context, imageProvider) {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      onImageLoad(post.photos.first.url);
                    });
                    return Container(
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(7),
                        image: DecorationImage(
                          image: imageProvider,
                          fit: BoxFit.cover,
                        ),
                      ),
                    );
                  },
                  fadeInDuration: Duration.zero,
                  // placeholder: (context, url) => Widgets.imageLoader(context),
                  errorWidget: (context, url, error) => const Icon(Icons.error),
                  cacheManager: AppCacheManager.instance,
                ),
              ),
            ),
          );
        } else {
          mediaStaggeredTiles.add(const StaggeredTile.count(2, 1.5));
        }
      }
    }

    for (var video in post.videos) {
      media.add(_videoContainer(video));
      mediaStaggeredTiles.add(const StaggeredTile.count(2, 1.2));
    }

    final mediaWidget = (!post.isPoll) && media.isNotEmpty
        ? Padding(
            padding: const EdgeInsets.only(top: 5),
            child: StaggeredGridView.count(
              shrinkWrap: true,
              primary: true,
              crossAxisCount: 2,
              mainAxisSpacing: 2.0,
              crossAxisSpacing: 2.0,
              physics: const NeverScrollableScrollPhysics(),
              staggeredTiles: mediaStaggeredTiles,
              padding: EdgeInsets.zero,
              children: media,
            ),
          )
        : Container();

    return mediaWidget;
  }

  getUserNameWidget() {
    return AutoSizeText(
      user.name,
      minFontSize: 14,
      maxLines: 1,
      textScaler: const TextScaler.linear(1.0),
      textAlign: TextAlign.center,
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
          color: _footerTextColor(), fontSize: 17, fontWeight: FontWeight.w600),
    );
  }

  getUserBadgeStrip(User user) {
    final badge = user.badge;
    if (badge != null) {
      if (badge.active && user.isUserHaveBadgeBanner()) {
        return Padding(
            padding: const EdgeInsets.only(top: 5, bottom: 8),
            child: BannerStrip(
              user: user,
              post: post,
            ));
      }
    }
    return const SizedBox();
  }

  getAppIcon() {
    return Positioned(
      right: 0,
      top: 0,
      child: SizedBox(
        width: 40,
        child: AspectRatio(
          aspectRatio: appIconAspectRatioFromDesign,
          child: Image(
            image: widget.appPostShareCardIcon,
            fit: BoxFit.fill,
          ),
        ),
      ),
    );
  }

  getToolTipText() {
    return shareCardText != null
        ? shareCardText.photoUploadToolTip
        : "tip: మీ ప్రొఫైల్ ఫోటోను జోడించండి";
  }

  getToolTipBoxColor() {
    return customGradientValues != null
        ? customGradientValues.uploadToolTipColor
        : const Color(0xff606060);
  }

  getToolTipTextColor() {
    return customGradientValues != null
        ? customGradientValues.uploadToolTipTextColor
        : Colors.white;
  }

  animatedUploadMessageWidget() {
    return checkForShowingCameraIcon() && showImageUploadMsg
        ? Visibility(
            visible: showImageUploadMsg,
            child: Positioned(
              bottom: 85,
              left: 10 +
                  (avatarRadius /
                      2), //10 means user avatar positioned 15 - whole widget left padding[5]
              child: FadeIn(
                preferences: const AnimationPreferences(
                    duration: Duration(milliseconds: 1000)),
                child: BounceIn(
                  preferences: const AnimationPreferences(
                      duration: Duration(milliseconds: 800)),
                  key: basicAnimation,
                  child: MessageBubble(
                    msg: getToolTipText(),
                    textColor: getToolTipTextColor(),
                    bubbleColor: getToolTipBoxColor(),
                  ),
                ),
              ),
            ),
          )
        : const SizedBox();
  }

  getUserAvatar() {
    bool isLoggedInUserPost = checkLoggedInUser();
    if (isLoggedInUserPost &&
        !widget.isShare &&
        !isDownLoading &&
        !isWhatsappShareLoading) {
      return AppUserEditableAvatar(
        source: "post_share_card",
        size: avatarRadius * 2,
        alwaysFullSize: true,
        showCameraAnimation: true,
      );
    }
    return UserAvatar.fromIdentity(
      user.toIdentity(),
      size: avatarRadius * 2,
      alwaysFullSize: true,
      noPhotoFallback: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(blurRadius: 5, color: Color(0xff848884), spreadRadius: 1)
          ],
        ),
        child: CircleAvatar(
          radius: avatarRadius - 4,
          backgroundImage: widget.uploadProfilePicImage,
        ),
      ),
    );
  }

  getButtonDecoration() {
    return ButtonStyle(
        backgroundColor: MaterialStateProperty.all(Colors.white));
  }

  getPostShareCardImage() async {
    Uint8List? postShareCardScreenImage;
    if (mounted) {
      postShareCardScreenImage =
          await _takeScreenshotController.captureAsPngBytes(pixelRatio: 2);
    }

    return postShareCardScreenImage;
  }

  downloadButton() {
    return Expanded(
        child: ElevatedButton.icon(
      style: getButtonDecoration(),
      icon: Container(
        child: isDownLoading || isProfilePicLoading
            ? Widgets.buttonLoader(size: 14, color: Colors.black)
            : const Icon(
                Icons.download,
                color: Colors.black,
              ),
      ),
      label: const Text("సేవ్", style: TextStyle(color: Colors.black)),
      onPressed: isDownLoading || isWhatsappShareLoading || isProfilePicLoading
          ? null
          : () async {
              bool tempIsLoggedInUser = isCurrentLoggedInUser;
              if (mounted) {
                setState(() {
                  isDownLoading = true;
                  showImageUploadMsg = false;
                  isCurrentLoggedInUser = false;
                  AppAnalytics.logEvent(name: "post_format_download_begin");
                });
              }
              try {
                final shareCard = await getPostShareCardImage();
                if (shareCard != null) {
                  await ImageGallerySaver.saveImage(shareCard,
                      quality: 60,
                      name:
                          "${DateTime.now().millisecondsSinceEpoch.toString()}.png");
                  AppAnalytics.logEvent(name: "post_format_download");
                  Utils.showToast("ఈ పోస్ట్ మీ గ్యాలరీకి డౌన్లోడ్ అయ్యింది.");
                }
              } catch (e) {
                Utils.showSnackBarMessage(context,
                    "ఈ పోస్ట్ మీ గ్యాలరీకి డౌన్లోడ్ చేయలేకపోయాము, మళ్ళీ ప్రయత్నించండి.");
                AppAnalytics.logShareOrDownloadFail(e.toString(), post.id,
                    isDownload: true, contentType: "post_format");
              }
              if (mounted) {
                setState(() {
                  isDownLoading = false;
                  isCurrentLoggedInUser = tempIsLoggedInUser;
                });
              }
            },
    ));
  }

  whatsappShareButton() {
    return Expanded(
      child: ElevatedButton.icon(
          style: getButtonDecoration(),
          onPressed: isDownLoading ||
                  isWhatsappShareLoading ||
                  isProfilePicLoading
              ? null
              : () async {
                  bool tempIsLoggedInUser = isCurrentLoggedInUser;
                  if (mounted) {
                    setState(() {
                      isWhatsappShareLoading = true;
                      showImageUploadMsg = false;
                      isCurrentLoggedInUser = false;
                      AppAnalytics.logEvent(
                          name: "share_begin",
                          parameters: {"content_type": "post_format"});
                    });
                  }
                  try {
                    String postShareText =
                        await _postService.getShareTextV1(post);
                    final postShareCardImage = await getPostShareCardImage();
                    final pathToImage =
                        await Utils.writeToTempFile(postShareCardImage);
                    if (postShareCardImage != null) {
                      if (Platform.isAndroid) {
                        await WhatsappShareAndroid.shareFiles(
                            [XFile(pathToImage.path)],
                            text: postShareText);
                      } else {
                        await Share.shareXFiles([XFile(pathToImage.path)],
                            subject: "Poster", text: "");
                        await FlutterClipboard.copy(postShareText);
                        // Post details are copied. Please paste in content!
                        Utils.showToast(
                          "పోస్ట వివరాలు కాపీ చేయబడ్డాయి.\nదయచేసి కంటెంట్‌లో పేస్ట్ చేయండి!",
                          toastLength: Toast.LENGTH_LONG,
                        );
                      }
                      AppAnalytics.logEvent(
                          name: "share",
                          parameters: {"content_type": "post_format"});
                    }
                  } catch (e) {
                    Utils.showSnackBarMessage(context,
                        "ఈ పోస్ట్ షేర్ చేయలేకపోయాము, మళ్ళీ ప్రయత్నించండి.");
                    AppAnalytics.logShareOrDownloadFail(e.toString(), post.id,
                        isWhatsapp: true, contentType: "post_format");
                  }
                  if (mounted) {
                    setState(() {
                      isWhatsappShareLoading = false;
                      isCurrentLoggedInUser = tempIsLoggedInUser;
                    });
                  }
                },
          icon: SizedBox(
              height: 20,
              width: 20,
              child: isWhatsappShareLoading || isProfilePicLoading
                  ? Widgets.buttonLoader(size: 14, color: Colors.green)
                  : Image.asset("assets/images/action/whatsapp.png")),
          label: const Text("షేర్", style: TextStyle(color: Colors.black))),
    );
  }

  getActionButtons() {
    if (widget.isShare) {
      return const SizedBox();
    }
    return Padding(
        padding: const EdgeInsets.only(left: 20, right: 20, top: 10),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            downloadButton(),
            const SizedBox(
              width: 20,
            ),
            whatsappShareButton(),
          ],
        ));
  }

  getVideoBannerText() {
    return shareCardText != null
        ? shareCardText.videoBannerText
        : "పూర్తి వీడియోని Praja App ‌లో చూడండి.";
  }

  getVideoBannerTextColor() {
    return customGradientValues != null
        ? customGradientValues.videoBannerTextColor
        : Colors.black;
  }

  getVideoBannerGradient() {
    final videoBannerGradientValues =
        customGradientValues.videoBannerGradientValues;
    return LinearGradient(
        colors: videoBannerGradientValues != null &&
                videoBannerGradientValues.colors != null
            ? videoBannerGradientValues.colors
            : [Colors.blue, Colors.blue],
        stops: videoBannerGradientValues != null &&
                videoBannerGradientValues.stops != null
            ? videoBannerGradientValues.stops
            : null);
  }

  getVideoBanner() {
    return hasVideo()
        ? Padding(
            padding: const EdgeInsets.only(top: 5),
            child: AspectRatio(
              aspectRatio:
                  hasText() ? maxWidthFromDesign / 60 : maxWidthFromDesign / 80,
              child: Container(
                alignment: Alignment.center,
                decoration: BoxDecoration(gradient: getVideoBannerGradient()),
                child: Text(
                  getVideoBannerText(),
                  style:
                      TextStyle(fontSize: 16, color: getVideoBannerTextColor()),
                ),
              ),
            ),
          )
        : const SizedBox();
  }

  getBackGroundGradients() {
    final backgroundGradientValues =
        customGradientValues.backgroundGradientValues;
    return LinearGradient(
      begin: gradientsBeginAlignment(),
      end: gradientsEndAlignment(),
      colors: backgroundGradientValues != null &&
              backgroundGradientValues.colors != null
          ? backgroundGradientValues.colors
          : [Colors.blue, Colors.blue],
      stops: backgroundGradientValues != null &&
              backgroundGradientValues.stops != null
          ? backgroundGradientValues.stops
          : null,
    );
  }

  getFooter() {
    return AspectRatio(
      aspectRatio: isBadgeUser(user)
          ? maxWidthFromDesign / maxFooterHeightForBadgeUserFromDesign
          : maxWidthFromDesign / maxFooterHeightForNonBadgeUserFromDesign,
      child: Padding(
        padding: const EdgeInsets.only(left: 100, right: 20),
        child: Align(
          alignment: const Alignment(-0.5, 0.5),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              getUserNameWidget(),
              getUserBadgeStrip(user),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // this is needed for triggering media load callback for posts with no media
      if (isNotAMediaPost()) widget.mediaLoadCallBack?.call(true);
    });
    return InkWell(
      highlightColor: Colors.transparent,
      splashColor: Colors.transparent,
      onTap: () {
        if (mounted) {
          setState(() {
            showImageUploadMsg = false;
          });
        }
      },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          widget.isShare ? const SizedBox() : getCloseButton(),
          TakeScreenshot(
            controller: _takeScreenshotController,
            child: FittedBox(
              fit: BoxFit.cover,
              child: Container(
                decoration: BoxDecoration(gradient: getBackGroundGradients()),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    Container(
                      margin: const EdgeInsets.only(left: 5, right: 5),
                      width: MediaQuery.of(context).size.width,
                      child: Column(
                        children: [
                          getTopHeader(),
                          ConstrainedBox(
                            constraints: BoxConstraints(
                                maxWidth: MediaQuery.of(context).size.width,
                                minWidth: MediaQuery.of(context).size.width,
                                maxHeight: contentMaxHeight,
                                minHeight: contentMinHeight),
                            child: Container(
                              color: Colors.white,
                              padding: const EdgeInsets.only(left: 5, right: 5),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  getMediaWidget(),
                                  getVideoBanner(),
                                  Container(
                                    padding: const EdgeInsets.only(
                                        top: 0, bottom: 0, left: 5, right: 5),
                                    margin: const EdgeInsets.only(
                                        left: 5, right: 5),
                                    child: PostTextContent(
                                      post: post,
                                      noOfLines: initialMaxLines,
                                      showReadMore: initialMaxLines < maxLines,
                                      contentStyle: contentStyle,
                                    ),
                                  ),
                                  SizedBox(
                                    height: hasText()
                                        ? initialMaxLines < maxLines
                                            ? 10
                                            : 30
                                        : 2,
                                  )
                                ],
                              ),
                            ),
                          ),
                          getFooter(),
                        ],
                      ),
                    ),
                    Positioned(
                      bottom: 15,
                      left: 15,
                      child: Align(
                        alignment: Alignment.centerRight,
                        child: Padding(
                          padding: const EdgeInsets.only(right: 15),
                          child: getUserAvatar(),
                        ),
                      ),
                    ),
                    getAppIcon(),
                    animatedUploadMessageWidget()
                  ],
                ),
              ),
            ),
          ),
          getActionButtons(),
        ],
      ),
    );
  }
}
