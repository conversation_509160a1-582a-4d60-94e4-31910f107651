part of 'post_bloc.dart';

abstract class PostEvent extends Equatable {
  @override
  List<Object> get props => [];
}

//PostUpdated event will contain the updated post as parameter
// it will make sure that the post with that specific id being updated
//in postsInBloc map of posts bloc so that it will be updated all over the app
class PostUpdated extends PostEvent {
  final Post post;

  PostUpdated(this.post);

  @override
  List<Object> get props => [post];
}

//Post Added event will contain the post needs to be added to the postBloc
//so that we can update or maintain it's state wherever we are using the that post
//object to show in feed or detail pages.

class PostAdded extends PostEvent {
  final Post post;

  PostAdded(this.post);

  @override
  List<Object> get props => [post];
}

class PostDelete extends PostEvent {
  final Post post;

  PostDelete(this.post);

  @override
  List<Object> get props => [post];
}
