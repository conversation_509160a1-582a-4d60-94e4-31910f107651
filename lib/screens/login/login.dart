import 'dart:async';
import 'dart:core';
import 'dart:io';

import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get_it/get_it.dart';
import 'package:phone_numbers_parser/phone_numbers_parser.dart';
import 'package:praja/core/ui/page.dart';
import 'package:praja/errors/error_delegate.dart';
import 'package:praja/features/on_boarding/models/login_response.dart';
import 'package:praja/features/on_boarding/service/auth_service.dart';
import 'package:praja/features/user/models/app_user.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/models/user.dart';
import 'package:praja/screens/login/otp.dart';
import 'package:praja/screens/login/signup.dart';
import 'package:praja/utils/utils.dart';
import 'package:praja/utils/widgets.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:smart_auth/smart_auth.dart';
import 'package:truecaller_sdk/truecaller_sdk.dart';

class LoginPage extends BasePage {
  static const String tag = '/login';

  final void Function(bool)? onSignedIn;

  @override
  String get pageName => 'login';

  LoginPage({this.onSignedIn, Key? key}) : super(key: key);

  buildContent(BuildContext context) {
    return LoginPageInner(onSignedIn: this.onSignedIn);
  }
}

// TODO: Convert to Stateless widget + ViewModel
class LoginPageInner extends StatefulWidget {
  final void Function(bool)? onSignedIn;

  const LoginPageInner({super.key, required this.onSignedIn});

  @override
  _LoginPageState createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPageInner> {
  final myController = TextEditingController();
  bool _loading = false;
  bool _keyboardVisible = false;
  bool _isTruecallerAvailable = false;
  bool _truecallerLoading = false;

  List<String> slidesText = [
    "మీ లోకల్ వార్తలు, రాజకీయ విషయాలు ట్రెండ్ చేయండి; మీ గ్రూప్ ని పెంచుకోండి",
    "లేటెస్ట్ వార్త విశేషాలు తెలుసుకోండి",
    "మీ లోకల్ లీడర్ గ్రూప్ లో జాయిన్ అవ్వండి; వారి ఫాలోవర్స్ అందరితో మీ అభిప్రాయాలను పంచుకోండి",
    "మీ రాజకియ పార్టీ గ్రూప్ లో చేరండి. పార్టీ విశేషాలు తెలుసుకొండి",
  ];
  StreamSubscription? streamSubscription;

  StreamSubscription<bool>? keyboardSubscription;
  final AuthService _authService = GetIt.I.get<AuthService>();

  bool isUsable = false;

  @override
  void initState() {
    super.initState();
    AppAnalytics.logEvent(name: "login_screen");
    initialiseTrueCaller();

    // our iOS app got rejected because we had android images in the 3rd and 4th slides
    // so we are only using the first 2 slides for iOS until we replace the images with iOS ones
    slidesText = slidesText.sublist(0, Platform.isIOS ? 2 : 4);
  }

  phoneHintRequest() async {
    final selectedAccount = await SmartAuth().requestHint(
      isPhoneNumberIdentifierSupported: true,
      isEmailAddressIdentifierSupported: false,
      showCancelButton: true,
    );

    if (selectedAccount != null && selectedAccount.id != null) {
      final val =
          PhoneNumber.parse(selectedAccount.id, callerCountry: IsoCode.IN);
      myController.text = val.nsn;
      myController.selection = TextSelection.fromPosition(
          TextPosition(offset: myController.text.length));
      otpRequest();
    }
  }

  initialiseTrueCaller() async {
    if (!Platform.isAndroid) {
      return;
    }

    TruecallerSdk.initializeSDK(
        sdkOptions: TruecallerSdkScope.SDK_OPTION_WITHOUT_OTP);

    isUsable = await TruecallerSdk.isUsable;
    if (isUsable) {
      setState(() {
        _isTruecallerAvailable = true;
      });
      truecallerInitiate();
    } else {
      phoneHintRequest();
    }
  }

  truecallerInitiate() {
    TruecallerSdk.setLocale("te");
    if (isUsable) {
      TruecallerSdk.getProfile;
      AppAnalytics.logEvent(name: "popup_truecaller_signup_or_login");
    }

    streamSubscription =
        TruecallerSdk.streamCallbackData.listen((truecallerSdkCallback) {
      switch (truecallerSdkCallback.result) {
        case TruecallerSdkCallbackResult.success:
          var profile = truecallerSdkCallback.profile!;
          bool truecallerProfilePic = profile.avatarUrl != null;
          AppAnalytics.logEvent(
              name: "signup_or_login_with_truecaller",
              parameters: {
                "state": "successful",
                "truecaller_user_name":
                    "${profile.firstName} ${profile.lastName}",
                "truecaller_email": profile.email ?? null,
                "truecaller_profile_picture": truecallerProfilePic
              });
          loginWithTruecaller(profile);

          break;
        case TruecallerSdkCallbackResult.failure:
          // Returns error code "2" if user closes truecaller pop-up
          //Returns error "14" if user closes trucaller pop-up using back button or back press
          var error = truecallerSdkCallback.error!;
          if (error.code == 2 || error.code == 14) {
            phoneHintRequest();
            AppAnalytics.logEvent(name: "skip_truecaller_signup_or_login");
          } else {
            Utils.showSnackBarMessage(
                context, "ఏదో సరిగ్గా లేదు. కాసేపు ప్రయత్నించండి");
            AppAnalytics.logEvent(
                name: "signup_or_login_with_truecaller",
                parameters: {"state": "unsuccessful"});
          }
          break;
        case TruecallerSdkCallbackResult.verification:
          //this is used only for SDK_OPTION_WITH_OTP
          break;
        default:
          Utils.showSnackBarMessage(
              context, "ఏదో సరిగ్గా లేదు. కాసేపు ప్రయత్నించండి");
          AppAnalytics.logEvent(
              name: "signup_or_login_with_truecaller",
              parameters: {"state": "unsuccessful", "substate": "unknown"});
      }
    });
  }

  void loginWithTruecaller(TruecallerUserProfile profile) async {
    if (!mounted) return;
    try {
      setState(() => _truecallerLoading = true);

      final response = await _authService.loginWithTruecaller(profile);

      setState(() => _truecallerLoading = false);

      if (response is ExistingUserLoginResponse && mounted) {
        final user = response.user;
        AppAnalytics.setAppUser(user, true);
        AppAnalytics.setSingularUserId(user.id.toString());
        AppAnalytics.logLogin(user: user, loginMethod: 'truecaller');

        AppAnalytics.logLoginSignupBegin(
            method: "truecaller",
            userType: "old_user",
            isInternal: user.internal);
        SharedPreferences prefs = await SharedPreferences.getInstance();
        prefs.remove('user.refer_user_hash_id');

        widget.onSignedIn?.call(false);
      } else {
        final resp = response as NewUserLoginResponse;
        AppAnalytics.setSingularUserId(resp.id.toString());
        AppAnalytics.logLoginSignupBegin(
            method: "truecaller",
            userType: "new_user",
            isInternal: resp.internal);
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (BuildContext context) => SignUpPage(
              phone: resp.phone,
              otp: resp.otp,
              truecallerUserProfile: profile,
              onSignUp: (AppUser user) async {
                AppAnalytics.logSignUp(user: user, signUpMethod: 'truecaller');
                widget.onSignedIn?.call(true);
              },
            ),
          ),
        );
      }
    } catch (e) {
      setState(() => this._truecallerLoading = false);
      return Utils.showToast(localisedErrorMessage(e));
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    var keyboardVisibilityController = KeyboardVisibilityController();
    keyboardSubscription =
        keyboardVisibilityController.onChange.listen((bool visible) {
      if (this.mounted) setState(() => _keyboardVisible = visible);
    });
    // _setAutoFillMobileNumber();
    // PermissionsService().checkAndRequestPhonePermission();
  }

  @override
  void dispose() {
    // Clean up the controller when the Widget is disposed
    myController.dispose();
    keyboardSubscription?.cancel();
    streamSubscription?.cancel();
    super.dispose();
  }

  // void _setAutoFillMobileNumber() {
  //   if (Platform.isAndroid) {
  //     PermissionsService().checkAndRequestPhonePermission().then((value) {
  //       if (value) {
  //         Utils.getAutoFillMobileNumber().then((number) {
  //           if (number.isNotEmpty) {
  //             setState(() => myController.text = number);
  //           }
  //         });
  //       }
  //     });
  //   }
  // }

  Future<void> otpRequest() async {
    var errorMsg = "";
    if (myController.text.isEmpty) {
      errorMsg = 'ఫోన్ నెంబర్ ఖాళీ గా ఉండకూడదు'; // Phone number cannot be blank
    } else if (int.tryParse(myController.text) == null) {
      errorMsg = 'ఫోన్ నెంబర్ సరిచూసుకోండి';
    } else if (myController.text.length != 10) {
      errorMsg =
          'ఫోన్ నెంబర్ లో 10 అంకెలు ఉండాలి'; // Phone number must be of 10 digits
    }

    if (errorMsg.isNotEmpty) {
      return Utils.showSnackBarMessage(context, errorMsg);
    }

    try {
      setState(() => this._loading = true);

      AppAnalytics.logEvent(
        name: "login_begin",
        parameters: {"method": "phone"},
      );

      int phone = int.parse(myController.text);

      final response = await _authService.requestOTP(phone: phone);

      setState(() => this._loading = false);

      if (response is ExistingUserLoginResponse && mounted) {
        final user = response.user;
        Utils.setAutoFillMobileNumber(user.phone.toString());
        AppAnalytics.setAppUser(user, true);
        AppAnalytics.setSingularUserId(user.id.toString());
        AppAnalytics.logLoginSignupBegin(
            method: "phone", userType: "old_user", isInternal: user.internal);
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (BuildContext context) => OtpPage(
              phone: phone,
              otp: user.otp,
              onSignedIn: (AppUser user) async {
                AppAnalytics.logLogin(user: user, loginMethod: 'phone');
                widget.onSignedIn?.call(false);
              },
            ),
          ),
        );
      } else {
        final resp = response as NewUserLoginResponse;
        AppAnalytics.setSingularUserId(resp.id.toString());
        Utils.setAutoFillMobileNumber(resp.phone.toString());
        AppAnalytics.logLoginSignupBegin(
            method: "phone", userType: "new_user", isInternal: resp.internal);
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (BuildContext context) => SignUpPage(
              phone: resp.phone,
              otp: resp.otp,
              onSignUp: (AppUser user) async {
                AppAnalytics.logSignUp(user: user, signUpMethod: 'phone');
                widget.onSignedIn?.call(true);
              },
            ),
          ),
        );
      }
    } catch (e) {
      setState(() => this._loading = false);
      return Utils.showToast(localisedErrorMessage(e));
    }
  }

  @override
  Widget build(BuildContext context) {
    final logo = Container(
      width: 125,
      height: 125,
      padding: EdgeInsets.all(5.0),
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(color: Colors.black38, blurRadius: 20.0, spreadRadius: 5.0)
        ],
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(10.0)),
      ),
      child: Image.asset("assets/images/logo.jpg"),
    );

    final phone = TextFormField(
      style: TextStyle(
        color: Colors.white,
        decorationColor: Colors.white,
      ),
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'\d')),
        LengthLimitingTextInputFormatter(10),
      ],
      cursorColor: Colors.white,
      controller: myController,
      autofocus: !_isTruecallerAvailable,
      onTap: () {
        AppAnalytics.logEvent(name: "Clicked_Enter_Mobile_Number");
      },
      keyboardType: TextInputType.phone,
      decoration: InputDecoration(
        hintText: "ఫోన్ నెంబర్",
        // Phone Number
        contentPadding: EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
        fillColor: Colors.white,
        focusColor: Colors.white,
        border: OutlineInputBorder(
          borderSide: BorderSide(color: Colors.white),
        ),
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(color: Colors.white),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(color: Colors.white),
        ),
        hintStyle: TextStyle(color: Colors.white),
      ),
    );

    final loginButton = ButtonTheme(
      minWidth: double.infinity,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
            padding: EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(5),
            )),
        onPressed: !this._loading ? otpRequest : () {},
        child: Container(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Text(
                'లాగిన్ / జాయిన్', // Login / Signup
                style: TextStyle(color: Colors.white),
              ),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.all(Radius.circular(20)),
                  color: Theme.of(context).primaryColorDark,
                ),
                child: this._loading
                    ? Widgets.buttonLoader(size: 14)
                    : Icon(
                        Icons.arrow_forward_ios,
                        color: Colors.white,
                        size: 14,
                      ),
              )
            ],
          ),
        ),
      ),
    );

    final truecallerButton = ButtonTheme(
      minWidth: double.infinity,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(5),
          ),
        ),
        onPressed: !this._loading
            ? () {
                streamSubscription?.cancel();
                truecallerInitiate();
              }
            : () {},
        child: Padding(
          padding: const EdgeInsets.all(0.0),
          child: Container(
            width: MediaQuery.of(context).size.width,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Row(
                  children: [
                    Image.asset(
                      "assets/images/truecaller.png",
                      scale: 3,
                      color: Theme.of(context).primaryColor,
                    ),
                    SizedBox(
                      width: 10,
                    ),
                    Container(
                      padding: EdgeInsets.only(top: 7),
                      child: Text(
                        'లాగిన్', // Login / Signup
                        style: TextStyle(color: Colors.black, fontSize: 17),
                      ),
                    ),
                  ],
                ),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.all(Radius.circular(20)),
                    color: Theme.of(context).primaryColorDark,
                  ),
                  child: this._truecallerLoading
                      ? Widgets.buttonLoader(size: 14)
                      : Icon(
                          Icons.arrow_forward_ios,
                          color: Colors.white,
                          size: 14,
                        ),
                )
              ],
            ),
          ),
        ),
      ),
    );

    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Stack(
          children: <Widget>[
            CarouselSlider(
              items: slidesText.map((slideText) {
                int index = slidesText.indexOf(slideText);
                return Builder(
                  builder: (BuildContext context) {
                    return Container(
                      decoration: BoxDecoration(
                        image: DecorationImage(
                          image: AssetImage(
                            "assets/images/intro/slide-${index + 1}.jpg",
                          ),
                          fit: BoxFit.cover,
                        ),
                      ),
                      child: Align(
                        alignment: Alignment.bottomCenter,
                        child: Container(
                          height: 0.6 * MediaQuery.of(context).size.height,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              // Where the linear gradient begins and ends
                              begin: Alignment.bottomCenter,
                              end: Alignment.topCenter,
                              // Add one stop for each color. Stops should increase from 0 to 1
                              stops: [0.0, 0.6, 1.0],
                              colors: [
                                Colors.black.withOpacity(0.95),
                                Colors.black.withOpacity(0.50),
                                Colors.transparent,
                              ],
                            ),
                          ),
                          child: Align(
                            alignment: Alignment(0, -0.4),
                            child: Text(
                              _keyboardVisible ? "" : slideText,
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: 22,
                                color: Colors.white,
                                shadows: [
                                  BoxShadow(
                                    color: Colors.black38,
                                    blurRadius: 5.0,
                                    spreadRadius: 50.0,
                                  )
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                );
              }).toList(),
              options: CarouselOptions(
                pauseAutoPlayOnTouch: true,
                viewportFraction: 1.0,
                height: double.infinity,
                autoPlay: true,
              ),
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Container(margin: EdgeInsets.only(top: 50), child: logo),
                Container(
                  padding: EdgeInsets.fromLTRB(20, 50, 20, 20),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      phone,
                      SizedBox(height: 10.0),
                      loginButton,
                      _isTruecallerAvailable
                          ? Column(
                              children: [
                                SizedBox(height: 10.0),
                                Row(
                                  mainAxisSize: MainAxisSize.max,
                                  children: [
                                    Expanded(
                                      child: Divider(
                                        color: Colors.white,
                                      ),
                                    ),
                                    SizedBox(width: 10.0),
                                    Text(
                                      "లేదా",
                                      style: TextStyle(color: Colors.white),
                                    ),
                                    SizedBox(width: 10.0),
                                    Expanded(
                                      child: Divider(
                                        color: Colors.white,
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 10.0),
                                truecallerButton
                              ],
                            )
                          : Container()
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
