import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_pagewise/flutter_pagewise.dart';
import 'package:get_it/get_it.dart';
import 'package:praja/features/notifications/model/notification.dart';
import 'package:praja/features/notifications/service/notification.dart';
import 'package:praja/screens/notifications/list_item.dart';
import 'package:praja/shimmers/notifications.dart';
import 'package:praja/utils/widgets.dart';

class NotificationsList extends StatefulWidget {
  final ScrollController? controller;
  final EdgeInsets padding;

  const NotificationsList({
    Key? key,
    this.controller,
    this.padding = EdgeInsets.zero,
  }) : super(key: key);

  @override
  NotificationsListState createState() => NotificationsListState();
}

class NotificationsListState extends State<NotificationsList> {
  static int perPageCount = 10;
  static int preLoadPageIndex = 0;
  final NotificationService _notificationService =
      GetIt.I.get<NotificationService>();

  late PagewiseLoadController _pageLoadController;

  @override
  void initState() {
    super.initState();
    Future<List<NotificationElement>> loadNotificationsPage(
      int? pageIndex,
    ) async {
      final notificationPageIndex = pageIndex ?? 0;
      preLoadPageIndex = notificationPageIndex;

      return await _notificationService.fetchPage(
        notificationPageIndex * perPageCount,
        perPageCount,
      );
    }

    _pageLoadController = PagewiseLoadController(
      pageSize: perPageCount,
      pageFuture: loadNotificationsPage,
    );
  }

  Future<void> refreshNotifications() async {
    _pageLoadController.reset();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: refreshNotifications,
        child: PagewiseListView(
          padding: widget.padding,
          controller: widget.controller,
          noItemsFoundBuilder: (context) {
            return Widgets.emptyNotificationWidget(); // No notifications yet
          },
          loadingBuilder: (_) {
            if (NotificationsListState.preLoadPageIndex == 0) {
              return const NotificationsShimmer(count: 5);
            } else {
              return const NotificationsShimmer(count: 2);
            }
          },
          pageLoadController: _pageLoadController,
          itemBuilder: (context, notification, index) {
            return NotificationListItem(
              notification: notification as NotificationElement,
            );
          },
        ),
      ),
    );
  }
}
