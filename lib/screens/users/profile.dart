import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:praja/models/user_details.dart';
import 'package:praja/screens/users/bloc/profile_bloc.dart';
import 'package:praja/screens/users/profile_info.dart';
import 'package:praja/utils/logger.dart';
import 'package:praja/utils/utils.dart';
import 'package:praja/utils/widgets.dart';
import 'package:praja/utils/widgets/badge_celebration_screen.dart';
import 'package:shared_preferences/shared_preferences.dart';

class MyProfile extends StatefulWidget {
  static const String tag = '/my-profile';

  final EdgeInsets padding;
  final ScrollController? scrollController;
  final String source;

  const MyProfile({
    super.key,
    this.padding = EdgeInsets.zero,
    this.scrollController,
    this.source = "my_profile",
  });

  @override
  State<MyProfile> createState() => _MyProfileState();
}

class _MyProfileState extends State<MyProfile> {
  Timer? timer;
  bool isBadgeCelebrationShown = false;

  @override
  void initState() {
    super.initState();
    addProfileFetchEvent();
  }

  addProfileFetchEvent() {
    BlocProvider.of<ProfileBloc>(context).add(FetchProfile());
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    SharedPreferences.getInstance().then((prefs) async {
      final int showNextAt =
          prefs.getInt('user.invite_prompt_show_next_at') ?? 0;
      int currentTime = DateTime.now().millisecondsSinceEpoch;

      // Show the prompt only after `x` days
      if (showNextAt > 0 && currentTime > showNextAt) {
        int delayDays = await Utils.getReferPromptDelay();
        int delayMilliSec = delayDays * 24 * 60 * 60 * 1000;

        timer = Timer(
          const Duration(seconds: 3),
          () {
            prefs.setInt(
              'user.invite_prompt_show_next_at',
              currentTime + delayMilliSec,
            );
            //_invitePrompt();
          },
        );
      }
    });
  }

  void _gotoCelebrationCard(UserDetails user) async {
    if (user.loggedInUser) {
      //user.notifyBadgeIssue will be null when visiting other user's profile.
      if (user.notifyBadgeIssue) {
        if (mounted) {
          if (isBadgeCelebrationShown == false) {
            isBadgeCelebrationShown = true;
            Navigator.of(context).push(MaterialPageRoute(
              builder: (BuildContext context) {
                return BadgeCelebrationScreen(
                  source: "profile_page",
                );
              },
            ));
          }
        }
      }
    }
    //});
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ProfileBloc, ProfileState>(
      listener: (context, state) {
        if (state is ProfileLoadSuccess) {
          _gotoCelebrationCard(state.user);
        }
      },
      builder: (context, state) {
        logInfo(state.toString());
        if (state is ProfileLoadInProgress) {
          return Widgets.renderCircularLoader(context);
        } else if (state is ProfileLoadSuccess) {
          return ProfileInfo(
            user: state.user,
            padding: widget.padding,
            scrollController: widget.scrollController,
            source: widget.source,
          );
        } else if (state is ProfileLoadFailure) {
          return Widgets.renderCircularLoader(context);
        } else {
          return Widgets.renderCircularLoader(context);
        }
      },
    );
  }
}
