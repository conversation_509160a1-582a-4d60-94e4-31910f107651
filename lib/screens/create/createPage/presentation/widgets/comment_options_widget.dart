import 'package:flutter/material.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/models/comment_option.dart';
import 'package:praja/models/comments_config.dart';

class CommentOptionsWidget extends StatefulWidget {
  final CommentsConfig commentsConfig;
  final CommentOption selectedCommentOption;
  const CommentOptionsWidget({
    Key? key,
    required this.commentsConfig,
    required this.selectedCommentOption,
  }) : super(key: key);

  @override
  State<CommentOptionsWidget> createState() => _CommentOptionsWidgetState();
}

class _CommentOptionsWidgetState extends State<CommentOptionsWidget> {
  late CommentOption selectedCommentOption;

  @override
  void initState() {
    super.initState();
    selectedCommentOption = widget.selectedCommentOption;
  }

  @override
  Widget build(BuildContext context) {
    return Wrap(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(vertical: 30),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.commentsConfig.commentsInfoHeaderText,
                      style: const TextStyle(
                          fontWeight: FontWeight.bold, fontSize: 18),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Text(
                      widget.commentsConfig.commentsInfoBodyText,
                      style: const TextStyle(
                          fontSize: 15, color: Color(0xff7C8494)),
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 25,
              ),
              ListView.builder(
                  shrinkWrap: true,
                  physics: const BouncingScrollPhysics(),
                  itemCount:
                      widget.commentsConfig.availableCommentOptions.length,
                  itemBuilder: (context, index) {
                    return InkWell(
                      onTap: () {
                        setState(() {
                          selectedCommentOption = widget
                              .commentsConfig.availableCommentOptions[index];
                        });
                        AppAnalytics.logEvent(
                            name: "comment_option_click",
                            parameters: {
                              "option": selectedCommentOption.displayText
                            });
                        Future.delayed(const Duration(milliseconds: 300))
                            .then((value) {
                          Navigator.of(context).pop(selectedCommentOption);
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 15),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(3),
                              decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                      color: Colors.black, width: 2.5)),
                              child: Container(
                                decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: selectedCommentOption ==
                                            widget.commentsConfig
                                                .availableCommentOptions[index]
                                        ? Colors.black
                                        : Colors.transparent),
                                width: 10,
                                height: 10,
                              ),
                            ),
                            const SizedBox(
                              width: 30,
                            ),
                            Flexible(
                              child: Text(
                                widget.commentsConfig
                                    .availableCommentOptions[index].displayText,
                                style: const TextStyle(
                                    fontSize: 16, color: Colors.black),
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }),
            ],
          ),
        ),
      ],
    );
  }
}
