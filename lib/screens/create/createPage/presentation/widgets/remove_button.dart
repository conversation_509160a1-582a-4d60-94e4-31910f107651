import 'package:flutter/material.dart';

class AssetRemoveButton extends StatelessWidget {
  final Function()? onTap;
  const AssetRemoveButton({Key? key, this.onTap}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IconButton(
      splashRadius: 20,
      onPressed: onTap,
      icon: PhysicalModel(
        elevation: 7,
        shape: BoxShape.circle,
        color: Colors.white,
        child: Container(
            height: 20,
            width: 20,
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white,
            ),
            child: const Icon(
              Icons.close,
              color: Color(0xff8899A6),
              size: 15,
            )),
      ),
    );
  }
}
