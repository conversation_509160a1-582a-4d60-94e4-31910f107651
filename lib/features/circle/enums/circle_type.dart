import 'package:json_annotation/json_annotation.dart';

enum CircleType {
  location,
  interest,
  @JsonValue('my_circle')
  myCircle,
  @JsonValue('governing_body')
  governingBody,
  @JsonValue('user_created')
  userCreated,
}

extension CircleTypeFromString on String {
  CircleType toCircleType() {
    return CircleType.values.firstWhere(
      (e) => e.toString().split('.').last == this,
      orElse: () => CircleType.interest,
    );
  }
}
