import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:praja/exceptions/api_exception.dart';
import 'package:praja/features/suggestions/models/suggested_lists.dart';
import 'package:praja/network/network_constants.dart';

@injectable
class SuggestedListService {
  Dio httpClient;

  SuggestedListService(@Named(ror) this.httpClient);

  Future<SuggestedLists> getSuggestedUsers(int circleId) async {
    try {
      final resp = await httpClient.get(
        '/users/get-suggested-users-lists',
        queryParameters: {
          'circle_id': circleId.toString(),
        },
      );
      return SuggestedLists.fromJson(resp.data);
    } on DioError catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<bool> followSuggestedMembers(String feedItemId) async {
    try {
      await httpClient.post(
        '/users/follow-all-suggested-users',
        queryParameters: {
          'feed_item_id': feedItemId,
        },
      );
      return true;
    } on DioError catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<bool> trackSeenCircle(int id) async {
    try {
      await httpClient.put('/circles/$id/suggested-seen');
      return true;
    } on DioError catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<void> trackSeenList(String feedId) async {
    try {
      await httpClient.put(
        '/users/suggested-list-seen',
        data: {"list_id": feedId},
      );
    } on DioError catch (e) {
      throw ApiException.fromDioError(e);
    }
  }
}
