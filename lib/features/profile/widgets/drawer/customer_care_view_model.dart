import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/services/app_initializer.dart';

@injectable
class CustomerCareViewModel extends ViewModel {
  static const String customerCareKey = 'customer_care_user_id';
  final AppInitializer appInitializer;
  CustomerCareViewModel(this.appInitializer);

  final MutableLiveData<int?> _customerCareId = MutableLiveData(null);
  LiveData<int?> get customerCareId => _customerCareId;

  bool _isInitialized = false;
  void _init() {
    if (_isInitialized) return;

    _isInitialized = true;
    appInitializer.addListener(onAppInitialized);
  }

  void onAppInitialized(Map<String, dynamic> data) {
    if (data.containsKey(customerCareKey) &&
        data[customerCareKey] != null &&
        data[customerCareKey] is int &&
        data[customerCareKey] != 0) {
      _customerCareId.value = data[customerCareKey] as int;
    } else {
      _customerCareId.value = null;
    }
  }

  @override
  void onDispose() {
    appInitializer.removeListener(onAppInitialized);
    super.onDispose();
  }
}

extension CustomerCareViewModelExtension on BuildContext {
  CustomerCareViewModel get customerCareViewModel =>
      getViewModel<CustomerCareViewModel>().._init();
}
