import 'dart:ui';

import 'package:injectable/injectable.dart';
import 'package:praja/features/profile/models/navigation_drawer_tile_label.dart';
import 'package:praja/services/app_initializer.dart';

@lazySingleton
class NavigationDrawerConfig {
  NavigationDrawerTileLabel? navigationPremiumTileLabel;
  NavigationDrawerConfig(AppInitializer appInitializer) {
    appInitializer.addListener((data) {
      final premiumTileLabel = data['premium_tile_label'];
      if (premiumTileLabel != null) {
        navigationPremiumTileLabel =
            NavigationDrawerTileLabel.fromJson(premiumTileLabel);
      }
    });
  }
}
