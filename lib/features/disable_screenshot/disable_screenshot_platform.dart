import 'package:flutter/services.dart';
import 'package:injectable/injectable.dart';
import 'package:uuid/uuid.dart';

@lazySingleton
class DisableScreenshotPlatform {
  final MethodChannel _channel =
      const MethodChannel('disable_screenshot_platform');

  final Set<String> _activeTokens = {};

  /// returns a token that is expected to be provided on [onScreenshotDisablerInactive]
  String onScreenshotDisablerActive() {
    final token = const Uuid().v4().toString();
    if (_activeTokens.isEmpty) {
      _channel.invokeMethod('disableScreenshot');
    }
    _activeTokens.add(token);
    return token;
  }

  void onScreenshotDisablerInactive(String token) {
    _activeTokens.remove(token);
    if (_activeTokens.isEmpty) {
      _channel.invokeMethod('enableScreenshot');
    }
  }
}
