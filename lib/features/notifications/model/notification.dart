import 'package:json_annotation/json_annotation.dart';
import 'package:praja/models/user.dart';

part 'notification.g.dart';

@JsonSerializable()
class NotificationElement {
  @Json<PERSON>ey(name: 'id')
  final int id; //
  @<PERSON><PERSON><PERSON><PERSON>(name: 'description')
  final String description;
  @Json<PERSON><PERSON>(name: 'entity_type')
  final String entityType;
  @Json<PERSON><PERSON>(name: 'entity_id')
  final int entityId;
  @<PERSON>son<PERSON>ey(name: 'entity')
  final User? entity;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'notification_type')
  final String notificationType;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'notification_icon')
  final String? notificationIconUrl;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'read', defaultValue: false)
  bool read;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final DateTime createdAt;
  @Json<PERSON>ey(name: 'updated_at')
  final DateTime updatedAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_id')
  final int userId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'delivered', defaultValue: false)
  final bool delivered;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'received', defaultValue: false)
  final bool received;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'active', defaultValue: false)
  final bool active;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'deep_link')
  final String? deepLink;

  NotificationElement({
    required this.id,
    required this.description,
    required this.entityType,
    required this.entityId,
    required this.entity,
    required this.read,
    required this.notificationType,
    required this.notificationIconUrl,
    required this.createdAt,
    required this.updatedAt,
    required this.userId,
    required this.delivered,
    required this.received,
    required this.active,
    this.deepLink,
  });

  factory NotificationElement.fromJson(Map<String, dynamic> json) =>
      _$NotificationElementFromJson(json);

  Map<String, dynamic> toJson() => _$NotificationElementToJson(this);

  @override
  String toString() {
    return 'NotificationElement{id: $id, description: $description, entityType: $entityType, entityId: $entityId, entity: $entity, notificationType: $notificationType, notificationIconUrl: $notificationIconUrl, read: $read, createdAt: $createdAt}';
  }
}
