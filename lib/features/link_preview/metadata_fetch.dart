import 'dart:convert';
import 'package:universal_html/html.dart' as html;
import 'package:universal_html/parsing.dart' as parser;
import 'package:http/http.dart' as http;

class MetadataFetch {
  /// Takes an [http.Response] and returns a [html.Document]
  static html.Document? responseToDocument(http.Response response) {
    if (response.statusCode != 200) {
      return null;
    }

    html.Document? document;
    try {
      document = parser.parseHtmlDocument(utf8.decode(response.bodyBytes));
    } catch (err) {
      return document;
    }

    return document;
  }
}

String? getProperty(
  html.Document? document, {
  String tag = 'meta',
  String attribute = 'property',
  String? property,
  String key = 'content',
}) {
  return document
      ?.getElementsByTagName(tag)
      .cast<html.Element?>()
      .firstWhere((element) => element?.attributes[attribute] == property,
          orElse: () => null)
      ?.attributes
      .get(key);
}

extension GetMethod on Map {
  String? get(dynamic key) {
    var value = this[key];
    if (value is List) return value.first;
    return value.toString();
  }

  dynamic getDynamic(dynamic key) {
    return this[key];
  }
}
