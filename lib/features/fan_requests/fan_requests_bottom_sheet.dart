import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/features/fan_requests/fan_requests_bottom_sheet_view_model.dart';
import 'package:praja/features/fan_requests/widgets/fan_requests_feed_item_widget.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/models/circle_details.dart';
import 'package:praja/models/v2/circle_background.dart';
import 'package:praja/presentation/circle_display_picture.dart';
import 'package:praja/screens/circles_v2/widgets/circle_info_widgets/circle_gradients.dart';
import 'package:praja/services/app_cache_manager.dart';

extension FanRequestsBottomSheetX on BuildContext {
  ImageProvider<Object> _getBannerImage({CircleBackground? circleBackground}) {
    final photoUrl = circleBackground?.photoUrl;
    if (photoUrl != null && photoUrl.isNotEmpty) {
      return CachedNetworkImageProvider(
        photoUrl,
        cacheManager: AppCacheManager.instance,
      );
    } else {
      return const AssetImage('assets/images/banner_crowd.jpg');
    }
  }

  Widget _getCircleTitleWidget({required String name}) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.only(top: 28, left: 40),
      child: Text(
        name,
        textScaleFactor: 1.0,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
        style: const TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        textAlign: TextAlign.start,
      ),
    );
  }

  Widget _getCircleDetailsWidget({required CircleDetails circleDetails}) {
    const avatarSize = 90.0;
    return Stack(
      clipBehavior: Clip.none,
      alignment: Alignment.center,
      children: [
        Container(
          height: 120,
          width: double.infinity,
          decoration: BoxDecoration(
            image: DecorationImage(
              image: _getBannerImage(
                  circleBackground: circleDetails.circleBackground),
              fit: BoxFit.cover,
              opacity: 0.3,
            ),
            gradient: CircleGradients.getGradients(circleDetails.toCircle()),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(24),
              topRight: Radius.circular(24),
            ),
          ),
        ),
        Positioned(
          bottom: -20,
          left: 40,
          child: Container(
            height: avatarSize + 10,
            width: avatarSize + 10,
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.5),
              borderRadius: BorderRadius.circular(avatarSize / 4),
            ),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(avatarSize / 4),
                border: Border.all(color: Colors.white, width: 2),
              ),
              child: CircleDisplayPicture.fromCircle(
                circleDetails.toCircle(),
                size: avatarSize,
              ),
            ),
          ),
        ),
        Positioned(
          bottom: -20,
          right: 40,
          child: _getLeaderPhotosSection(
            leaderPhotos: circleDetails.leaderPhotos,
          ),
        ),
        Positioned(
          top: 4,
          child: Container(
            height: 5,
            width: 78,
            decoration: BoxDecoration(
              color: Colors.black,
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        )
      ],
    );
  }

  Widget _childWidget(String photoUrl) {
    return Container(
      height: 40,
      width: 40,
      decoration: photoUrl.isNotEmpty
          ? BoxDecoration(
              border: Border.all(width: 2, color: Colors.black12),
              shape: BoxShape.circle,
            )
          : BoxDecoration(
              border: Border.all(width: 2, color: Colors.white),
              color: Colors.blue,
              shape: BoxShape.circle,
            ),
      child: CachedNetworkImage(
        imageUrl: photoUrl,
        cacheManager: AppCacheManager.instance,
        imageBuilder: (context, imageProvider) {
          return Container(
              decoration: BoxDecoration(
            shape: BoxShape.circle,
            image: DecorationImage(
              image: imageProvider,
              fit: BoxFit.cover,
            ),
          ));
        },
      ),
    );
  }

  Widget _getLeaderPhotosSection({required List<String> leaderPhotos}) {
    if (leaderPhotos.isEmpty) {
      return const SizedBox();
    }
    return Row(
      children: [
        for (final photoUrl in leaderPhotos) _childWidget(photoUrl),
      ],
    );
  }

  Widget _getBody(BuildContext context, FanRequestsBottomSheetState state,
      {required String source}) {
    if (state is FanRequestsBottomSheetLoadingState) {
      return const SizedBox(
        height: 380,
        width: double.infinity,
        child: Center(
          child: CircularProgressIndicator(
            color: Colors.white,
          ),
        ),
      );
    } else if (state is FanRequestsBottomSheetErrorState) {
      AppAnalytics.onFanRequestsSheetError(source: source, params: {
        'error': state.errorMessage,
      });
      return SizedBox(
        height: 380,
        width: double.infinity,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              state.errorMessage,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                AppAnalytics.onRetryFanRequestsSheetClicked(
                    source: source, params: {'error': state.errorMessage});
                context
                    .fanRequestsBottomSheetViewModel()
                    .getFanRequestsSection();
              },
              child: const Text(
                'మళ్ళీ ప్రయత్నించండి',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                ),
              ),
            ),
          ],
        ),
      );
    } else if (state is FanRequestsBottomSheetSuccessState) {
      AppAnalytics.onFanRequestsBottomSheetShown(
          source: source, params: state.fanRequestsSection.analyticsParams);
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _getCircleDetailsWidget(
              circleDetails: state.fanRequestsSection.circleDetails),
          _getCircleTitleWidget(
              name: state.fanRequestsSection.circleDetails.name),
          const SizedBox(height: 2),
          FanRequestsFeedItemWidget(
            fanRequestsSection: state.fanRequestsSection,
            isFromBottomSheet: true,
            source: source,
          ),
        ],
      );
    } else {
      return Container();
    }
  }

  void showFanRequestsBottomSheet({required String source}) {
    if (_FanRequestsBottomSheetData.shownBottomSheet) {
      AppAnalytics.onFanRequestsBottomSheetAlreadyShown(source: source);
      return;
    }
    showModalBottomSheet(
      context: this,
      isScrollControlled: true,
      backgroundColor: Colors.black,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      builder: (context) {
        _FanRequestsBottomSheetData.shownBottomSheet = true;
        final viewModel = context.fanRequestsBottomSheetViewModel();

        return AnnotatedRegion(
          value: const SystemUiOverlayStyle(
            systemNavigationBarColor: Colors.black,
            systemNavigationBarIconBrightness: Brightness.light,
          ),
          child: WillPopScope(
            onWillPop: () async {
              final curState = viewModel.state.value;
              Map<String, dynamic> params = {};
              if (curState is FanRequestsBottomSheetSuccessState) {
                params = curState.fanRequestsSection.analyticsParams ?? {};
                params['sheet_status'] = 'success';
              } else if (curState is FanRequestsBottomSheetErrorState) {
                params = {
                  'error': curState.errorMessage,
                  'sheet_status': 'error'
                };
              } else {
                params = {'sheet_status': 'loading'};
              }
              AppAnalytics.onFanRequestsBottomSheetClosed(
                  source: source, params: params);
              return true;
            },
            child: LiveDataBuilder(
                liveData: viewModel.state,
                builder: (_, state) {
                  return _getBody(context, state, source: source);
                }),
          ),
        );
      },
    );
  }
}

class _FanRequestsBottomSheetData {
  static bool shownBottomSheet = false;
}
