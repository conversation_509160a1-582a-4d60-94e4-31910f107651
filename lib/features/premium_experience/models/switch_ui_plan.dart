import 'package:json_annotation/json_annotation.dart';

part 'switch_ui_plan.g.dart';

/// Use this model, when there is a need to show plan switching UI
/// that might be either in Upgrade or Downgrade flow
@JsonSerializable()
class SwitchUIPlan {
  @J<PERSON><PERSON><PERSON>(name: 'title', defaultValue: "")
  final String title;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'amount')
  final String amount;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'duration_text')
  final String durationText;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'per_month_text', defaultValue: "")
  final String perMonthText;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'plan_id')
  final int planId;

  SwitchUIPlan({
    required this.title,
    required this.amount,
    required this.durationText,
    required this.perMonthText,
    required this.planId,
  });

  factory SwitchUIPlan.fromJson(Map<String, dynamic> json) =>
      _$SwitchUIPlanFromJson(json);

  Map<String, dynamic> toJson() => _$SwitchUIPlanToJson(this);

  @override
  String toString() {
    return 'SwitchUIPlan{title: $title, amount: $amount, durationText: $durationText, perMonthText: $perMonthText, planId: $planId}';
  }
}
