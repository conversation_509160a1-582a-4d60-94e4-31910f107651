import 'package:json_annotation/json_annotation.dart';

part 'offer_reveal_sheet_response.g.dart';

@JsonSerializable()
class OfferRevealSheetResponse {
  @Json<PERSON>ey(name: 'image_url')
  final String? imageUrl;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'label', defaultValue: '')
  final String label;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'gift_offer_text', defaultValue: '')
  final String giftOfferText;
  @JsonKey(name: 'sub_text', defaultValue: '')
  final String subText;
  @<PERSON>son<PERSON>ey(name: 'button_text', defaultValue: '')
  final String buttonText;
  @<PERSON>son<PERSON>ey(name: 'skip_text', defaultValue: '')
  final String skipText;
  @<PERSON>son<PERSON><PERSON>(name: 'deeplink', defaultValue: '')
  final String deeplink;
  @Json<PERSON>ey(name: 'params')
  final Map<String, dynamic>? params;
  @Json<PERSON>ey(name: 'analytics_params')
  final Map<String, dynamic>? analyticsParams;

  OfferRevealSheetResponse({
    this.imageUrl,
    required this.label,
    required this.giftOfferText,
    required this.subText,
    required this.buttonText,
    required this.skipText,
    required this.deeplink,
    this.params,
    this.analyticsParams,
  });

  factory OfferRevealSheetResponse.fromJson(Map<String, dynamic> json) =>
      _$OfferRevealSheetResponseFromJson(json);

  Map<String, dynamic> toJson() => _$OfferRevealSheetResponseToJson(this);

  @override
  String toString() {
    return 'OfferRevealSheetResponse(imageUrl: $imageUrl, nameText: $label, giftOfferText: $giftOfferText, subText: $subText, buttonText: $buttonText, skipText: $skipText, deeplink: $deeplink, params: $params, analyticsParams: $analyticsParams)';
  }
}
