// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'profile_views.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProfileViews _$ProfileViewsFromJson(Map<String, dynamic> json) => ProfileViews(
      title: json['title'] as String? ?? '',
      locked: json['locked'] as bool? ?? false,
      seeMoreLocked: json['see_more_locked'] as bool? ?? false,
      viewers: (json['viewers'] as List<dynamic>?)
              ?.map((e) => UserIdentity.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      seeMoreText: json['see_more_text'] as String? ?? '',
      seeMoreTextColor:
          (json['see_more_text_color'] as num?)?.toInt() ?? 4287598479,
      deeplink: json['deeplink'] as String? ?? '',
      analyticsParams: json['analytics_params'] as Map<String, dynamic>? ?? {},
      feedType: json['feed_type'] as String,
      feedItemId: json['feed_item_id'] as String?,
    );

Map<String, dynamic> _$ProfileViewsToJson(ProfileViews instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('feed_item_id', instance.feedItemId);
  val['feed_type'] = instance.feedType;
  val['title'] = instance.title;
  val['locked'] = instance.locked;
  val['see_more_locked'] = instance.seeMoreLocked;
  val['viewers'] = instance.viewers.map((e) => e.toJson()).toList();
  val['see_more_text'] = instance.seeMoreText;
  val['see_more_text_color'] = instance.seeMoreTextColor;
  val['deeplink'] = instance.deeplink;
  val['analytics_params'] = instance.analyticsParams;
  return val;
}
