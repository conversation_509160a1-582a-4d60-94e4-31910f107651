import 'package:json_annotation/json_annotation.dart';

part 'premium_subscription_response.g.dart';

@JsonSerializable()
class PremiumSubscriptionResponse {
  @J<PERSON><PERSON><PERSON>(name: 'id')
  final String id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'checkout_url')
  final String? checkoutUrl;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'share_text', defaultValue: '')
  final String shareText;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'juspay_payload')
  final Map<String, dynamic>? juspayPayload;
  @Json<PERSON>ey(name: 'intent_url')
  final String? intentUrl;

  PremiumSubscriptionResponse({
    required this.id,
    required this.checkoutUrl,
    required this.shareText,
    required this.juspayPayload,
    required this.intentUrl,
  });

  factory PremiumSubscriptionResponse.fromJson(Map<String, dynamic> json) =>
      _$PremiumSubscriptionResponseFromJson(json);

  Map<String, dynamic> toJson() => _$PremiumSubscriptionResponseToJson(this);

  @override
  String toString() {
    return 'PremiumSubscriptionResponse{id: $id, checkoutUrl: $checkoutUrl, shareText: $shareText, juspayPayload: $juspayPayload, intentUrl: $intentUrl}';
  }
}
