import 'package:json_annotation/json_annotation.dart';
import 'package:praja/features/premium_experience/enums/payment_gateway_enum.dart';
import 'package:praja/features/premium_experience/models/existing_premium_users.dart';
import 'package:praja/features/premium_experience/models/payment_bottom_sheet_response.dart';
import 'package:praja/features/premium_experience/models/premium_experience_button_details.dart';
import 'package:praja/features/premium_experience/models/recharge_pay_wall_response.dart';
import 'package:praja/models/admin_user.dart';
import 'package:praja_posters/praja_posters.dart';

part 'annual_recharge_pay_wall_response.g.dart';

@JsonSerializable()
class AnnualRechargePaywallResponse {
  @JsonKey(name: 'title', defaultValue: '')
  final String title;
  @JsonKey(name: 'sub_title', defaultValue: '')
  final String subTitle;
  @JsonKey(name: 'plans', defaultValue: [])
  final List<PlanItem> plans;
  @JsonKey(name: 'pay_wall', defaultValue: [])
  final List<RechargePayBlock> payWall;
  @JsonKey(name: 'rm_user')
  final AdminUser? rmUser;
  @JsonKey(name: 'help')
  final PaywallHelp? help;
  @JsonKey(name: 'auto_pay_cancel_text', defaultValue: '')
  final String autoPayCancelText;
  @JsonKey(name: 'existing_premium_users')
  final ExistingPremiumUsers? existingPremiumUsers;
  @JsonKey(name: 'button_details')
  final PremiumExperienceButtonDetails buttonDetails;
  @JsonKey(name: 'terms')
  final PosterTerms? terms;
  @JsonKey(name: 'analytics_params')
  final Map<String, dynamic>? analyticsParams;
  @JsonKey(
      name: 'payment_gateway',
      defaultValue: PaymentGatewayEnum.url,
      unknownEnumValue: PaymentGatewayEnum.url)
  final PaymentGatewayEnum paymentGateway;

  AnnualRechargePaywallResponse({
    required this.title,
    required this.subTitle,
    required this.plans,
    required this.payWall,
    required this.rmUser,
    required this.help,
    required this.autoPayCancelText,
    required this.existingPremiumUsers,
    required this.buttonDetails,
    required this.terms,
    required this.analyticsParams,
    required this.paymentGateway,
  });

  factory AnnualRechargePaywallResponse.fromJson(Map<String, dynamic> json) =>
      _$AnnualRechargePaywallResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AnnualRechargePaywallResponseToJson(this);

  @override
  String toString() {
    return 'RechargePaywallResponse{title: $title, subTitle: $subTitle, plans: $plans, payWall: $payWall, rmUser: $rmUser, help: $help, autoPayCancelText: $autoPayCancelText, existingPremiumUsers: $existingPremiumUsers, buttonDetails: $buttonDetails, terms: $terms, analyticsParams: $analyticsParams, paymentGateway: $paymentGateway}';
  }
}
