import 'package:json_annotation/json_annotation.dart';
import 'package:praja/models/feed_item_abstract_class.dart';
import 'package:praja/models/user_identity.dart';

part 'profile_views.g.dart';

@JsonSerializable()
class ProfileViews extends FeedItem {
  @J<PERSON><PERSON>ey(name: 'title', defaultValue: '')
  final String title;
  @Json<PERSON>ey(name: 'locked', defaultValue: false)
  final bool locked;
  @JsonKey(name: 'see_more_locked', defaultValue: false)
  final bool seeMoreLocked;
  @<PERSON>son<PERSON><PERSON>(name: 'viewers', defaultValue: [])
  final List<UserIdentity> viewers;
  @<PERSON>son<PERSON><PERSON>(name: 'see_more_text', defaultValue: '')
  final String seeMoreText;
  @<PERSON>son<PERSON><PERSON>(name: 'see_more_text_color', defaultValue: 0xff8f8f8f)
  final int seeMoreTextColor;

  /// On Clicking Lock Icon, we will navigate to DeepLink
  @JsonKey(name: 'deeplink', defaultValue: '')
  final String deeplink;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'analytics_params', defaultValue: {})
  final Map<String, dynamic> analyticsParams;

  ProfileViews({
    required this.title,
    required this.locked,
    required this.seeMoreLocked,
    required this.viewers,
    required this.seeMoreText,
    required this.seeMoreTextColor,
    required this.deeplink,
    required this.analyticsParams,
    required super.feedType,
    super.feedItemId,
  });

  factory ProfileViews.fromJson(Map<String, dynamic> json) =>
      _$ProfileViewsFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$ProfileViewsToJson(this);

  @override
  String toString() {
    return 'ProfileViews{title: $title, locked: $locked, seeMoreLocked: $seeMoreLocked, viewers: $viewers, seeMoreText: $seeMoreText, seeMoreTextColor: $seeMoreTextColor, deeplink: $deeplink, analyticsParams: $analyticsParams}';
  }
}
