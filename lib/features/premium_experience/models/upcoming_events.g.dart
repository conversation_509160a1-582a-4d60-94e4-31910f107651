// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'upcoming_events.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UpcomingEvents _$UpcomingEventsFromJson(Map<String, dynamic> json) =>
    UpcomingEvents(
      creatives: (json['creatives'] as List<dynamic>?)
              ?.map((e) => PosterCreative.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      title: json['title'] as String? ?? '',
      locked: json['locked'] as bool? ?? false,
      deeplink: json['deeplink'] as String? ?? '',
      analyticsParams: json['analytics_params'] as Map<String, dynamic>? ?? {},
      feedType: json['feed_type'] as String,
      feedItemId: json['feed_item_id'] as String?,
    );

Map<String, dynamic> _$UpcomingEventsToJson(UpcomingEvents instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('feed_item_id', instance.feedItemId);
  val['feed_type'] = instance.feedType;
  val['creatives'] = instance.creatives.map((e) => e.toJson()).toList();
  val['title'] = instance.title;
  val['locked'] = instance.locked;
  val['deeplink'] = instance.deeplink;
  val['analytics_params'] = instance.analyticsParams;
  return val;
}
