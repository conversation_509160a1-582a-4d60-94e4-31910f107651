import 'dart:async';

import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/common/app_event_bus.dart';
import 'package:praja/errors/error_delegate.dart';
import 'package:praja/features/premium_experience/models/premium_experience_button_details.dart';
import 'package:praja/features/premium_experience/models/premium_experience_response.dart';
import 'package:praja/features/premium_experience/services/premium_experience_service.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/utils/logger.dart';
import 'package:praja/utils/utils.dart';

@injectable
class PremiumExperienceViewModel extends ViewModel {
  final PremiumExperienceService premiumExperienceService;
  final AppEventBus _appEventBus;

  PremiumExperienceViewModel(this.premiumExperienceService, this._appEventBus);

  final MutableLiveData<PremiumExperienceScreenState> _state =
      MutableLiveData(PremiumExperienceLoadingState());
  LiveData<PremiumExperienceScreenState> get state => _state;

  final MutableEventQueue<PremiumExperienceScreenEvent> _eventQueue =
      MutableEventQueue();
  EventQueue<PremiumExperienceScreenEvent> get eventQueue => _eventQueue;

  final MutableLiveData<PremiumExperienceButtonDetails?> _buttonDetails =
      MutableLiveData(null);
  LiveData<PremiumExperienceButtonDetails?> get buttonDetails => _buttonDetails;

  final MutableLiveData<bool> _buttonDetailsLoading = MutableLiveData(false);
  LiveData<bool> get buttonDetailsLoading => _buttonDetailsLoading;

  final MutableLiveData<bool> _showAppBar = MutableLiveData(false);
  LiveData<bool> get showAppBar => _showAppBar;

  final ScrollController scrollController = ScrollController();
  StreamSubscription<AppEvent>? _appEventsSubscription;

  bool _isInitialised = false;

  void init({bool openPaymentSheet = false}) {
    if (!_isInitialised) {
      if (openPaymentSheet) {
        _eventQueue.push(OpenPaymentSheetEvent());
      }
      _fetch();
      scrollController.addListener(_onScroll);
      _isInitialised = true;
    }
    _listenToAppEvents();
  }

  void _listenToAppEvents() {
    _appEventsSubscription?.cancel();
    _appEventsSubscription = _appEventBus.stream.listen((event) {
      if (event is PremiumExperienceScreenInvalidatedEvent) {
        _fetch();
      }
    });
  }

  static const _headerContainerHeight = 179.0;
  static const _contentHeight = 90.0;
  static const _scrollThreshold = _headerContainerHeight - _contentHeight;

  void _onScroll() {
    if (scrollController.offset >= _scrollThreshold) {
      _showAppBar.value = true;
    } else {
      _showAppBar.value = false;
    }
  }

  @override
  void onDispose() {
    scrollController.removeListener(_onScroll);
    scrollController.dispose();
    _appEventsSubscription?.cancel();
    _appEventsSubscription = null;
    super.onDispose();
  }

  void onRefresh() {
    _fetch();
  }

  Future<void> _fetch() async {
    try {
      _state.value = PremiumExperienceLoadingState();
      final response = await premiumExperienceService.getPremiumExperience();
      _state.value = PremiumExperienceSuccessState(response: response);
      _buttonDetails.value = response.buttonDetails;
    } catch (e, stackTrace) {
      _state.value = PremiumExperienceErrorState(localisedErrorMessage(e));
      logNonFatalIfAppError("Error fetching premium experience response", e,
          stackTrace: stackTrace);
    }
  }

  Future<void> onWaitlistButtonClick() async {
    final buttonDetails = _buttonDetails.value;
    if (buttonDetails == null) {
      return;
    }
    final type = buttonDetails.type;
    if (type == PremiumExperienceButtonType.waitlist) {
      _buttonDetailsLoading.value = true;
      try {
        final PremiumExperienceButtonDetails waitlistButtonDetails =
            await premiumExperienceService.waitlistApi();
        _buttonDetailsLoading.value = false;
        _buttonDetails.value = waitlistButtonDetails;
        AppAnalytics.onWaitlistResponseReceived(
            params: waitlistButtonDetails.analyticsParams);
        onRefresh();
      } catch (e, stackTrace) {
        _buttonDetailsLoading.value = false;
        logNonFatal("Error while fetching waitlist api", e,
            stackTrace: stackTrace);
        Utils.showToast(localisedErrorMessage(e));
      }
    }
  }

  void onScreenClosed({required String source}) {
    Map<String, dynamic>? analyticsParams;
    final currentState = state.value;
    if (currentState is PremiumExperienceSuccessState) {
      analyticsParams = currentState.response.analyticsParams;
    }

    AppAnalytics.onPremiumExperienceScreenClosed(
        source: source, params: analyticsParams);
  }
}

abstract class PremiumExperienceScreenState {}

class PremiumExperienceLoadingState extends PremiumExperienceScreenState {}

class PremiumExperienceSuccessState extends PremiumExperienceScreenState {
  final PremiumExperienceResponse response;

  PremiumExperienceSuccessState({required this.response});
}

class PremiumExperienceErrorState extends PremiumExperienceScreenState {
  final String errorMessage;

  PremiumExperienceErrorState(this.errorMessage);
}

abstract class PremiumExperienceScreenEvent {}

class OpenPaymentSheetEvent extends PremiumExperienceScreenEvent {}

extension PremiumExperienceViewModelX on BuildContext {
  PremiumExperienceViewModel premiumExperienceViewModel(
      {required String key, bool openPaymentSheet = false}) {
    final viewModel = getViewModel<PremiumExperienceViewModel>(key: key)
      ..init(openPaymentSheet: openPaymentSheet);
    return viewModel;
  }
}
