import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:praja/screens/external_web_view.dart';
import 'package:praja_posters/praja_posters.dart';

class PaymentTermsUI extends StatelessWidget {
  final PosterTerms terms;

  const PaymentTermsUI({super.key, required this.terms});

  @override
  Widget build(BuildContext context) {
    final beforeLinkText = terms.beforeLinkText;
    final linkText = terms.linkText;
    final afterLinkText = terms.afterLinkText;
    final linkUrl = terms.linkUrl;

    return RichText(
      textAlign: TextAlign.center,
      text: TextSpan(
        children: [
          if (beforeLinkText != null && beforeLinkText.isNotEmpty)
            TextSpan(
              text: beforeLinkText,
              style: const TextStyle(
                fontSize: 12,
                color: Color(0xff8f8f8f),
                fontWeight: FontWeight.w600,
              ),
            ),
          if (beforeLinkText != null && beforeLinkText.isNotEmpty)
            const TextSpan(text: ' '),
          TextSpan(
              text: linkText,
              style: const TextStyle(
                fontSize: 12,
                decoration: TextDecoration.underline,
                fontWeight: FontWeight.w600,
                color: Color(0xffB5811E),
                decorationColor: Color(0xffB5811E),
              ),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (BuildContext context) => ExternalWebView(
                        title: linkText,
                        url: linkUrl,
                      ),
                    ),
                  );
                }),
          if (afterLinkText != null && afterLinkText.isNotEmpty) ...[
            const TextSpan(text: ' '),
            TextSpan(
              text: afterLinkText,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Color(0xff8f8f8f),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
