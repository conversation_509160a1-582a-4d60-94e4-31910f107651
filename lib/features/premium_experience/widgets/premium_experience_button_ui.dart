import 'package:flutter/material.dart';
import 'package:praja/features/premium_experience/models/premium_experience_button_details.dart';
import 'package:praja/styles.dart';

class PremiumExperienceButtonUI extends StatelessWidget {
  final PremiumExperienceButtonDetails buttonDetails;
  final String? text;
  final VoidCallback? onPressed;
  final bool loading;
  final bool disabled;

  const PremiumExperienceButtonUI({
    super.key,
    required this.buttonDetails,
    this.text,
    this.onPressed,
    this.loading = false,
    this.disabled = false,
  });

  factory PremiumExperienceButtonUI.fromText({
    required String text,
    required VoidCallback onPressed,
    bool loading = false,
    bool disabled = false,
  }) {
    return PremiumExperienceButtonUI(
      buttonDetails: PremiumExperienceButtonDetails(
        type: PremiumExperienceButtonType.deeplink,
        deeplink: '',
        text: '',
        textSuffix: '',
        discountText: '',
        discountTextColor: 0,
        autoPerformAction: false,
        autoPerformActionTime: 0,
        disabled: false,
        apiUrl: '',
        analyticsParams: {},
      ),
      text: text,
      onPressed: onPressed,
      loading: loading,
      disabled: disabled,
    );
  }

  @override
  Widget build(BuildContext context) {
    // Use either the passed disabled parameter or the buttonDetails.disabled
    final bool isDisabled = disabled || buttonDetails.disabled;

    return InkWell(
      onTap: isDisabled ? null : onPressed,
      child: Container(
        width: double.infinity,
        height: 48,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          gradient:
              isDisabled ? null : Styles.premiumExperienceButtonGoldGradient(),
          color: isDisabled ? const Color(0xFFE0E0E0) : null,
        ),
        child: loading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    text != null ? text! : buttonDetails.text,
                    textScaler: const TextScaler.linear(1.0),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      color: isDisabled
                          ? const Color(0xff696969)
                          : const Color(0xff5A3D1B),
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (buttonDetails.discountText.isNotEmpty) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 10,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.8),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        buttonDetails.discountText,
                        textScaler: const TextScaler.linear(1.0),
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Color(buttonDetails.discountTextColor),
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ]
                ],
              ),
      ),
    );
  }
}
