import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/features/localization/vertical_adjustment_padding.dart';
import 'package:praja/features/premium_experience/models/relation_manager.dart';
import 'package:praja/features/premium_experience/widgets/premium_feed_widgets_header_ui.dart';
import 'package:praja/features/support_flow/support_sheet.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/presentation/praja_icons.dart';
import 'package:praja/presentation/view_detector.dart';
import 'package:praja/services/app_cache_manager.dart';
import 'package:praja/utils/utils.dart';
import 'package:url_launcher/url_launcher.dart';

class RelationManagerWidget extends StatelessWidget {
  final RelationManager relationManager;
  final String source;
  final int index;

  const RelationManagerWidget({
    super.key,
    required this.relationManager,
    required this.source,
    required this.index,
  });

  Widget _outlinedButton({
    required int colorCode,
    required IconData icon,
    void Function()? onPressed,
  }) {
    return InkWell(
      onTap: onPressed,
      child: Container(
        height: 40,
        padding: const EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          border: Border.all(
            width: 1,
            color: Color(colorCode).withOpacity(0.5),
          ),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Icon(
          icon,
          size: 20,
          color: Color(colorCode),
        ),
      ),
    );
  }

  Widget _supportButton({
    required BuildContext context,
  }) {
    return InkWell(
      onTap: () async {
        AppAnalytics.onSupportClicked(source: "relation_manager_$source");
        await context.showSupportSheet(source: "relation_manager_$source");
      },
      child: Container(
        height: 40,
        padding: const EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          color: const Color(0xFFF4E5B7),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.support_agent,
              size: 24,
              color: Color(0xffAA6F1C),
              weight: 600,
            ),
            const SizedBox(width: 8),
            Padding(
              padding: context.verticalAdjustmentPadding(fontSize: 16),
              child: Text(
                context.getString(StringKey.supportLabel),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(0xffAA6F1C),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final rmPhotoUrl = relationManager.rmUser.photoUrl;
    return ViewDetector(
      uniqueId: 'relation_manager_${relationManager.feedItemId}',
      threshold: 0.5,
      onView: (_) {
        AppAnalytics.onRelationManagerFeedItemViewed(
          index: index,
          source: source,
          params: relationManager.analyticsParams,
        );
      },
      builder: (context, __) {
        return Container(
          width: double.infinity,
          margin: const EdgeInsets.symmetric(horizontal: 14),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (relationManager.title.isNotEmpty) ...[
                PremiumFeedWidgetsHeaderUI(title: relationManager.title),
              ],
              Row(
                children: [
                  if (rmPhotoUrl != null) ...[
                    CachedNetworkImage(
                      height: 80,
                      width: 80,
                      cacheManager: AppCacheManager.instance,
                      imageUrl: rmPhotoUrl,
                      placeholder: (context, url) => const SizedBox(
                        height: 30,
                        width: 30,
                        child: Center(
                          child: CircularProgressIndicator(),
                        ),
                      ),
                      errorWidget: (context, url, error) =>
                          const Icon(Icons.error),
                      imageBuilder: (context, imageProvider) => Container(
                        height: 74,
                        width: 74,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          image: DecorationImage(
                            image: imageProvider,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                  ],
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          relationManager.rmUser.name,
                          style: GoogleFonts.anekTelugu(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: Color(relationManager.rmNameTextColor),
                          ),
                        ),
                        const SizedBox(height: 8),
                        if (relationManager.showActionButtons)
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: _outlinedButton(
                                  colorCode: 0xff00A650,
                                  icon: FontAwesomeIcons.whatsapp,
                                  onPressed: () {
                                    AppAnalytics.onInitiatedWhatsappMessageToRm(
                                        source: source,
                                        params:
                                            relationManager.analyticsParams);
                                    launchUrl(
                                      Uri.parse(
                                          relationManager.rmUser.whatsappLink),
                                    );
                                  },
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: _outlinedButton(
                                  colorCode: 0xff696969,
                                  icon: PrajaIcons.call,
                                  onPressed: () async {
                                    final unableToOpenPhoneText = context.getString(
                                        StringKey
                                            .premiumUnableToOpenPhoneErrorMessage,
                                        listen: false);
                                    //open dialer
                                    final Uri phoneLaunchUri = Uri(
                                      scheme: 'tel',
                                      path: relationManager.rmUser.phone
                                          .toString(),
                                    );
                                    if (await canLaunchUrl(phoneLaunchUri)) {
                                      AppAnalytics.onInitiatedCallToRm(
                                          source: source,
                                          params:
                                              relationManager.analyticsParams);
                                      await launchUrl(phoneLaunchUri);
                                    } else {
                                      AppAnalytics.onFailedCallToRm(
                                          source: source,
                                          reason: 'failed to open dialer',
                                          params:
                                              relationManager.analyticsParams);

                                      Utils.showToast(unableToOpenPhoneText);
                                    }
                                  },
                                ),
                              ),
                            ],
                          )
                        else
                          _supportButton(context: context),
                      ],
                    ),
                  )
                ],
              )
            ],
          ),
        );
      },
    );
  }
}
