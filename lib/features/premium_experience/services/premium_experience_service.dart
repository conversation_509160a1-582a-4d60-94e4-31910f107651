import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart' hide Order;
import 'package:praja/exceptions/api_exception.dart';
import 'package:praja/features/payments/models/upi_app.dart';
import 'package:praja/features/payments/service/juspay/juspay_exception.dart';
import 'package:praja/features/premium_experience/models/annual_recharge_pay_wall_response.dart';
import 'package:praja/features/premium_experience/models/cancel_flow_downgrade_sheet_response.dart';
import 'package:praja/features/premium_experience/models/cancel_membership_response.dart';
import 'package:praja/features/premium_experience/models/cancellation_confirmation_sheet_response.dart';
import 'package:praja/features/premium_experience/models/downgrade_bottom_sheet_response.dart';
import 'package:praja/features/premium_experience/models/offer_reveal_sheet_response.dart';
import 'package:praja/features/premium_experience/models/payment_bottom_sheet_response.dart';
import 'package:praja/features/premium_experience/models/premium_benefits_loss_screen_response.dart';
import 'package:praja/features/premium_experience/models/premium_experience_button_details.dart';
import 'package:praja/features/premium_experience/models/premium_experience_response.dart';
import 'package:praja/features/premium_experience/models/premium_subscription_response.dart';
import 'package:praja/features/premium_experience/models/premium_success_screen_response.dart';
import 'package:praja/features/premium_experience/models/profile_viewers_response.dart';
import 'package:praja/features/premium_experience/models/recharge_pay_wall_response.dart';
import 'package:praja/features/premium_experience/models/subscription.dart';
import 'package:praja/features/premium_experience/models/upgrade_page_response.dart';
import 'package:praja/network/network_constants.dart';

@injectable
class PremiumExperienceService {
  Dio httpClient;

  DateTime? lastPaymentEventTime;

  PremiumExperienceService(@Named(ror) this.httpClient);

  Future<PremiumExperienceResponse> getPremiumExperience() async {
    // return premiumExperienceScreenResponseMock;
    try {
      final resp = await httpClient.get('/premium-experience');
      return PremiumExperienceResponse.fromJson(resp.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<ProfileViewersResponse> getProfileViews(
      {required List<int> loadedUserIds,
      required CancelToken cancelToken}) async {
    try {
      final resp = await httpClient.post('/profile-views',
          cancelToken: cancelToken, data: {'loaded_user_ids': loadedUserIds});
      return ProfileViewersResponse.fromJson(resp.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<PremiumSuccessScreenResponse> getPremiumSuccessScreen() async {
    // return premiumSuccessScreenResponseMock;
    try {
      final resp = await httpClient.get('/premium-success-v2');
      return PremiumSuccessScreenResponse.fromJson(resp.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<PremiumSuccessScreenResponse> getPremiumSuccessScreenForTrial() async {
    // return premiumSuccessScreenResponseMock;
    try {
      final resp = await httpClient.get('/start-trial-popup');
      return PremiumSuccessScreenResponse.fromJson(resp.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  //post request
  Future<void> onStartTrial() async {
    try {
      await httpClient.post('/start-trial');
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<PremiumExperienceButtonDetails> waitlistApi() async {
    // return waitlistResponse;
    try {
      final resp = await httpClient.post('/wait-list');
      return PremiumExperienceButtonDetails.fromJson(resp.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<PaymentBottomSheetResponse> getPremiumBottomSheet() async {
    // return bottomSheetResponseMock;
    try {
      final resp = await httpClient.get('/premium-bottom-sheet');
      return PaymentBottomSheetResponse.fromJson(resp.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<PremiumSubscriptionResponse> getSubscriptionResponse(int id) async {
    try {
      final response = await httpClient.post('/plans/$id/subscribe');
      return PremiumSubscriptionResponse.fromJson(response.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<PremiumSubscriptionResponse> getJuspaySubscriptionResponse(int id,
      {UpiApp? upiApp}) async {
    try {
      final response = await httpClient.post('/plans/$id/juspay-subscribe',
          data: upiApp == null
              ? null
              : {
                  'app_name': upiApp.name,
                  'package_name': upiApp.packageName,
                });
      return PremiumSubscriptionResponse.fromJson(response.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<PremiumSubscriptionResponse> getIntentSubscriptionResponse(int id,
      {UpiApp? upiApp}) async {
    try {
      final response =
          await httpClient.post('/plans/$id/intent-checkout-subscribe',
              data: upiApp == null
                  ? null
                  : {
                      'app_name': upiApp.name,
                      'package_name': upiApp.packageName,
                    });
      return PremiumSubscriptionResponse.fromJson(response.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<void> reportJuspayFailure(
      {required String subscriptionId,
      required JuspayException exception,
      required Map<String, dynamic> payload}) async {
    try {
      await httpClient
          .post('/subscriptions/$subscriptionId/failure-callback', data: {
        'payload': payload,
        'error_code': exception.errorCode,
        'error_message': exception.message,
      });
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<Subscription> getSubscriptionStatus(String id) async {
    try {
      final response = await httpClient.get('/subscriptions/$id');
      return Subscription.fromJson(response.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<void> cancelMembership(
      {required String subscriptionId, required String selectedReason}) async {
    try {
      await httpClient.post(
        '/cancel-subscription',
        data: {
          'id': subscriptionId,
          'reason': selectedReason,
        },
      );
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<RechargePaywallResponse> getRechargePaywall() async {
    try {
      final response = await httpClient.get('/recharge-paywall-v2');
      return RechargePaywallResponse.fromJson(response.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<AnnualRechargePaywallResponse> getAnnualRechargePaywall() async {
    // return annualRechargePaywallResponseMock;
    try {
      final response = await httpClient.get('/annual-recharge-paywall');
      return AnnualRechargePaywallResponse.fromJson(response.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<DowngradeBottomSheetResponse> getDowngradeBottomSheet() async {
    // return downgradeSheetResponseMock;
    try {
      final response = await httpClient.get('/downgrade-sheet');
      return DowngradeBottomSheetResponse.fromJson(response.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<void> onDowngradeConsentReceived({
    required Map<String, dynamic> bodyData,
  }) {
    try {
      return httpClient.post('/downgrade', data: bodyData);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<OfferRevealSheetResponse> getOfferRevealSheet() async {
    // return offerRevealSheetResponseMock;
    try {
      final response = await httpClient.get('/offer-reveal-sheet');
      return OfferRevealSheetResponse.fromJson(response.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<void> offerRevealStatus(
      {required String status, Map<String, dynamic>? otherParams}) {
    try {
      return httpClient.post(
        '/offer-reveal-status',
        data: {
          'status': status,
          if (otherParams != null) ...otherParams,
        },
      );
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<UpgradePageResponse> getUpgradeToYearlyPageResponse(
      Map<String, String> params) async {
    try {
      final response =
          await httpClient.get('/upgrade-plan', queryParameters: params);
      return UpgradePageResponse.fromJson(response.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  void updateLastPaymentEventTime() {
    lastPaymentEventTime = DateTime.now();
  }

  Future<void> extendOrSwitchPlan(String apiUrl) async {
    try {
      await httpClient.post(apiUrl);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<CancelMembershipResponse> getCancelMembershipSheetResponse() async {
    // return cancelMembershipResponseMock;
    try {
      final response = await httpClient.get('/get-cancel-data-v2');
      return CancelMembershipResponse.fromJson(response.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<CancellationConfirmationSheetResponse>
      getCancellationConfirmationSheet() async {
    // return cancellationConfirmationSheetResponseMock;
    try {
      final response = await httpClient.get('/cancellation-confirmation-sheet');
      return CancellationConfirmationSheetResponse.fromJson(response.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<CancelFlowDowngradeSheetResponse> getCancelFlowDowngradeSheet() async {
    // return cancelFlowDowngradeSheetResponseMock;
    try {
      final response = await httpClient.get('/cancel-flow-downgrade-sheet');
      return CancelFlowDowngradeSheetResponse.fromJson(response.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<PremiumBenefitsLossScreenResponse>
      getPremiumBenefitsLossScreen() async {
    // return premiumBenefitsLossScreenResponseMock;
    try {
      final response = await httpClient.get('/premium-benefits-loss-screen');
      return PremiumBenefitsLossScreenResponse.fromJson(response.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }
}
