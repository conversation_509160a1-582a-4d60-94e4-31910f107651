import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/common/deeplink_params.dart';
import 'package:praja/core/ui/page.dart';
import 'package:praja/core/ui/page_view_reporter.dart';
import 'package:praja/features/deeplinks/destination.dart';
import 'package:praja/features/premium_experience/offer_reveal_sheet/offer_reveal_sheet_view_model.dart';
import 'package:praja/features/premium_experience/premium_utils.dart';
import 'package:praja/features/premium_experience/widgets/premium_experience_button_ui.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/utils/widgets/page_transition_widget.dart';
import 'package:provider/provider.dart';

extension OfferRevealSheetX on BuildContext {
  Future<void> showOfferRevealSheet(
      {required String source, String? returnUrl}) async {
    await showModalBottomSheet(
      context: this,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      enableDrag: false,
      builder: (context) {
        return Wrap(
          children: [_OfferRevealSheetBody(source: source)],
        );
      },
    );
  }
}

class _OfferRevealSheetBody extends BasePage {
  final String source;

  const _OfferRevealSheetBody({super.key, required this.source});

  @override
  String get pageName => "offer_reveal_sheet";

  @override
  bool get pageLoadRequiredForTracking => true;

  void _navigateToDeeplink(BuildContext context, String deeplink) {
    if (deeplink.isEmpty) return;
    final destination = DeeplinkDestination.fromRoute(deeplink);
    if (destination is PageDeeplink) {
      Navigator.push(
        context,
        BottomSheetToPageTransition(
          builder:
              destination.getWidgetBuilder(DeeplinkSource.internalDeeplink),
        ),
      );
    } else {
      destination?.go(context, DeeplinkSource.internalDeeplink);
    }
  }

  Widget _eventImageChild({String? imageUrl}) {
    final image = imageUrl;
    return Positioned.fill(
      child: Container(
        width: double.infinity,
        height: double.infinity,
        clipBehavior: Clip.hardEdge,
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
          gradient: PremiumUtils.offerRevealSheetBgGradient,
        ),
        child: Column(
          children: [
            if (image != null)
              ShaderMask(
                shaderCallback: (bounds) => LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Colors.white.withOpacity(0.3), Colors.transparent],
                ).createShader(bounds),
                blendMode: BlendMode.dstIn,
                child: Container(
                  width: double.infinity,
                  height: 200.0,
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      fit: BoxFit.cover,
                      image: CachedNetworkImageProvider(
                        image,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _getBody({
    required OfferRevealSheetState state,
    required BuildContext context,
    required OfferRevealSheetViewModel viewModel,
  }) {
    if (state is LoadingState) {
      return const Center(
        child: CircularProgressIndicator(
          color: Colors.white,
        ),
      );
    } else if (state is SuccessState) {
      final response = state.response;
      final pageParams = {
        'source': source,
        ...response.analyticsParams ?? {},
      };
      Provider.of<PageViewTracker>(context, listen: false)
          .onPageLoaded(pageParams);
      return Stack(
        children: [
          _eventImageChild(imageUrl: response.imageUrl),
          Padding(
            padding: const EdgeInsets.only(
              left: 20,
              right: 20,
              top: 20,
              bottom: 40,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Container(
                  width: double.infinity,
                  alignment: Alignment.centerLeft,
                  child: InkWell(
                    onTap: () {
                      viewModel.onOfferSkipped(source: 'close_button');
                      Navigator.of(context).pop();
                    },
                    child: const Icon(
                      Icons.close,
                      size: 24,
                      applyTextScaling: false,
                      color: Color(0xFFD3AF4A),
                    ),
                  ),
                ),
                const SizedBox(height: 32),
                InkWell(
                  onTap: () {
                    if (response.deeplink.isNotEmpty) {
                      viewModel.onOfferOpened(source: 'gift_image');
                      Navigator.of(context).pop();
                      _navigateToDeeplink(context, response.deeplink);
                    } else {
                      Navigator.of(context).pop();
                    }
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 32.0),
                    child: Image.asset(
                      "assets/images/premium-offer-gift.png",
                      height: 280,
                      fit: BoxFit.cover,
                      width: double.infinity,
                    ),
                  ),
                ),
                if (response.label.isNotEmpty) ...[
                  Container(
                    padding:
                        const EdgeInsets.symmetric(vertical: 6, horizontal: 16),
                    decoration: BoxDecoration(
                      color: const Color(0xFFD3AF4A),
                      borderRadius: BorderRadius.circular(24),
                      border: Border.all(
                        color: const Color(0xFF5A3D1B),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      response.label,
                      textScaler: TextScaler.noScaling,
                      style: const TextStyle(
                        color: Color(0xFF5A3D1B),
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
                const SizedBox(height: 32),
                Text(
                  response.giftOfferText,
                  textScaler: TextScaler.noScaling,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    color: Color(0xFFD3AF4A),
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  response.subText,
                  textScaler: TextScaler.noScaling,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    color: Color(0xFFFFFFFF),
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 48),
                PremiumExperienceButtonUI.fromText(
                  text: response.buttonText,
                  onPressed: () {
                    if (response.deeplink.isNotEmpty) {
                      viewModel.onOfferOpened(source: 'button');
                      Navigator.of(context).pop();
                      _navigateToDeeplink(context, response.deeplink);
                    } else {
                      Navigator.of(context).pop();
                    }
                  },
                ),
                const SizedBox(height: 16),
                InkWell(
                  onTap: () {
                    viewModel.onOfferSkipped(source: 'skip_button');
                    Navigator.of(context).pop();
                  },
                  child: Text(
                    response.skipText,
                    textScaler: TextScaler.noScaling,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      color: Color(0xFF8F8F8F),
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                )
              ],
            ),
          ),
        ],
      );
    } else {
      return const Center(
        child: Text("Error"),
      );
    }
  }

  @override
  Widget buildContent(BuildContext context) {
    final viewModel = context.offerRevealSheetViewModel;
    return PopScope(
      canPop: true,
      onPopInvoked: (bool isPop) {
        if (isPop && viewModel.state.value is SuccessState) {
          viewModel.onOfferSkipped(source: 'back');
        }
      },
      child: AnnotatedRegion(
        value: const SystemUiOverlayStyle(
          systemNavigationBarColor: PremiumUtils.offerRevealSheetSystemNavColor,
          systemNavigationBarIconBrightness: Brightness.light,
        ),
        child: Container(
          decoration: const BoxDecoration(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(24),
              topRight: Radius.circular(24),
            ),
          ),
          child: LiveDataBuilder(
            liveData: viewModel.state,
            builder: (context, state) {
              if (state is ErrorState) {
                AppAnalytics.onOfferRevealSheetError(
                  source: source,
                  errorMessage: state.error.toString(),
                );
                Navigator.of(context).pop();
              }
              return _getBody(
                state: state,
                context: context,
                viewModel: viewModel,
              );
            },
          ),
        ),
      ),
    );
  }
}
