import 'package:flutter/material.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/core/ui/page.dart';
import 'package:praja/core/ui/page_view_reporter.dart';
import 'package:praja/features/premium_experience/downgrade_plan/downgrade_bottom_sheet_view_model.dart';
import 'package:praja/features/premium_experience/models/payment_bottom_sheet_response.dart';
import 'package:praja/features/premium_experience/premium_utils.dart';
import 'package:praja/features/premium_experience/widgets/premium_experience_button_ui.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/utils/utils.dart';
import 'package:provider/provider.dart';

extension DowngradeBottomSheetX on BuildContext {
  Future<void> showDowngradeBottomSheet(
      {required String source, String? returnUrl}) async {
    await showModalBottomSheet(
      context: this,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      enableDrag: false,
      builder: (context) {
        return Wrap(
          children: [_DowngradeBottomSheetBody(source: source)],
        );
      },
    );
  }
}

class _DowngradeBottomSheetBody extends BasePage {
  final String source;

  const _DowngradeBottomSheetBody({super.key, required this.source});

  @override
  String get pageName => "downgrade_bottom_sheet";

  @override
  bool get pageLoadRequiredForTracking => true;

  Widget _getSwitchingPlanItemUI(
      {required PlanItem plan, required Color textColor}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          plan.durationText,
          textScaler: const TextScaler.linear(1.0),
          style: TextStyle(
            color: textColor,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          plan.perMonthText,
          textScaler: const TextScaler.linear(1.0),
          style: TextStyle(
            color: textColor,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  List<TextSpan> _parseRichText(String text) {
    final List<String> parts = text.split("**");
    List<TextSpan> spans = [];
    for (int i = 0; i < parts.length; i++) {
      spans.add(TextSpan(
        text: parts[i],
        style: TextStyle(
          fontWeight: i.isOdd ? FontWeight.bold : FontWeight.w500,
          color: const Color(0xFF696969),
          fontSize: 16,
        ),
      ));
    }
    return spans;
  }

  Widget _getBody({
    required DowngradeBottomSheetState state,
    required void Function() onConsentApproved,
    required void Function() onConsentRejected,
    required BuildContext context,
  }) {
    if (state is DowngradeBottomSheetLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: Color(0xFFBF951E),
          strokeWidth: 3,
        ),
      );
    } else if (state is DowngradeBottomSheetSuccess) {
      final response = state.response;
      final pageParams = {
        'source': source,
        ...response.analyticsParams ?? {},
      };
      Provider.of<PageViewTracker>(context, listen: false)
          .onPageLoaded(pageParams);
      AppAnalytics.onDowngradeSheetLoaded(
          source: source, params: response.analyticsParams);
      return Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            alignment: Alignment.centerLeft,
            child: InkWell(
              onTap: () {
                onConsentRejected();
                Navigator.of(context).pop();
              },
              child: const Icon(
                Icons.close,
                size: 20,
                color: Color(0xFFD3AF4A),
              ),
            ),
          ),
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: PremiumUtils.premiumCrownGoldIconWidget(iconSize: 32),
          ),
          const SizedBox(height: 8),
          Text(
            response.title,
            textScaler: const TextScaler.linear(1.0),
            style: const TextStyle(
              color: Color(0xFFD3AF4A),
              fontSize: 22,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 48),
          Text(
            response.nameText,
            textScaler: const TextScaler.linear(1.0),
            style: const TextStyle(
              color: Color(0xFF5A3D1B),
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 32),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12.0),
            child: RichText(
              textAlign: TextAlign.start,
              textScaler: const TextScaler.linear(1.0),
              text: TextSpan(children: _parseRichText(response.notifyText)),
            ),
          ),
          const SizedBox(height: 48),
          Container(
            padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFFFCB638).withOpacity(0.3),
                  spreadRadius: 4,
                  blurRadius: 4,
                  offset: const Offset(0, 0),
                ),
                BoxShadow(
                  color: const Color(0xFFFCB638).withOpacity(0.3),
                  spreadRadius: 4,
                  blurRadius: 4,
                  offset: const Offset(0, 0),
                ),
              ],
            ),
            child: Row(
              children: [
                _getSwitchingPlanItemUI(
                  plan: response.planSwitchDetails.previousPlan,
                  textColor: const Color(0xFF5A3D1B).withOpacity(0.5),
                ),
                const SizedBox(width: 16),
                const Expanded(
                  child: Center(
                    child: Icon(
                      Icons.arrow_forward_rounded,
                      applyTextScaling: false,
                      color: Color(0xFF4EA502),
                      size: 48,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                _getSwitchingPlanItemUI(
                  plan: response.planSwitchDetails.switchingPlan,
                  textColor: const Color(0xFF5A3D1B),
                ),
              ],
            ),
          ),
          const SizedBox(height: 48),
          PremiumExperienceButtonUI.fromText(
            text: response.buttonText,
            onPressed: () {
              onConsentApproved();
              Navigator.of(context).pop();
            },
          ),
        ],
      );
    } else {
      return const Center(
        child: Text("Error"),
      );
    }
  }

  @override
  Widget buildContent(BuildContext context) {
    final viewModel = context.downgradeBottomSheetViewModel;
    return PopScope(
      canPop: true,
      onPopInvoked: (bool isPop) {
        if (isPop) {
          viewModel.onConsentRejected(isFromBackPress: true);
        }
      },
      child: Container(
        padding:
            const EdgeInsets.only(left: 20, right: 20, top: 20, bottom: 40),
        decoration: const BoxDecoration(
          gradient: PremiumUtils.downgradeSheetBgGradient,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
        ),
        child: LiveDataBuilder(
            liveData: viewModel.state,
            builder: (context, state) {
              if (state is DowngradeBottomSheetError) {
                final message = state.errorMessage;
                AppAnalytics.onDowngradeSheetError(
                  source: source,
                  errorMessage: state.error.toString(),
                );
                if (message != null && message.isNotEmpty) {
                  Utils.showToast(message);
                }
                Navigator.of(context).pop();
              }
              return _getBody(
                state: state,
                onConsentApproved: viewModel.onConsentApproved,
                onConsentRejected: viewModel.onConsentRejected,
                context: context,
              );
            }),
      ),
    );
  }
}
