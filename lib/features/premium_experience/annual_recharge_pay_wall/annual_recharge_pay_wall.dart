import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/core/ui/page.dart';
import 'package:praja/core/ui/page_view_reporter.dart';
import 'package:praja/enums/payment_status.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/features/payments/models/upi_app.dart';
import 'package:praja/features/premium_experience/annual_recharge_pay_wall/annual_recharge_pay_wall_view_model.dart';
import 'package:praja/features/premium_experience/models/existing_premium_users.dart';
import 'package:praja/features/premium_experience/models/payment_bottom_sheet_response.dart';
import 'package:praja/features/premium_experience/models/premium_experience_button_details.dart';
import 'package:praja/features/premium_experience/premium_success_screen/premium_success_screen.dart';
import 'package:praja/features/premium_experience/premium_utils.dart';
import 'package:praja/features/premium_experience/subscription_handler.dart';
import 'package:praja/features/premium_experience/widgets/annual_pay_wall_widgets/annual_pay_wall_plans_ui.dart';
import 'package:praja/features/premium_experience/widgets/existing_premium_users_widget.dart';
import 'package:praja/features/premium_experience/widgets/pay_wall_widget.dart';
import 'package:praja/features/premium_experience/widgets/pay_with_upi_app_widget.dart';
import 'package:praja/features/premium_experience/widgets/payment_terms_ui.dart';
import 'package:praja/features/premium_experience/widgets/premium_experience_button_ui.dart';
import 'package:praja/features/premium_experience/widgets/upi_app_picker.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/screens/circles_v2/widgets/customScrollBehaviour.dart';
import 'package:praja/services/app_state.dart';
import 'package:praja/utils/logger.dart';
import 'package:praja/utils/utils.dart';
import 'package:praja_posters/praja_posters.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

/// Paywall Supports Both Annual & Monthly Recharge Setup
class AnnualRechargePaywallUI extends BasePage {
  final String source;
  final String? returnUrl;

  const AnnualRechargePaywallUI(
      {super.key, required this.source, this.returnUrl});

  @override
  String get pageName => 'annual_recharge_paywall';

  @override
  bool get pageLoadRequiredForTracking => true;

  @override
  Widget buildContent(BuildContext context) {
    return _AnnualRechargePaywallInner(
        key: key, source: source, returnUrl: returnUrl);
  }
}

class _AnnualRechargePaywallInner extends StatefulWidget {
  final String source;
  final String? returnUrl;

  const _AnnualRechargePaywallInner(
      {super.key, required this.source, this.returnUrl});

  @override
  State<_AnnualRechargePaywallInner> createState() =>
      _AnnualRechargePaywallInnerState();
}

class _AnnualRechargePaywallInnerState
    extends State<_AnnualRechargePaywallInner> with RouteAware {
  late AnnualRechargePaywallViewModel viewModel;
  StreamSubscription<AppLifecycleState>? _appStateSubscription;
  late PageViewTracker _pageViewTracker;

  @override
  void initState() {
    super.initState();
    viewModel =
        context.annualRechargePaywallViewModel('annual_recharge_paywall');
    _pageViewTracker = Provider.of<PageViewTracker>(context, listen: false);
  }

  @override
  dispose() {
    _appStateSubscription?.cancel();
    _appStateSubscription = null;
    super.dispose();
  }

  void _onAppStateChanged(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      viewModel.onUserReturnedToThePage();
      _appStateSubscription?.cancel();
      _appStateSubscription = null;
    }
  }

  Widget _autoPayCancelTextWidget({required String text}) {
    if (text.isEmpty) return const SizedBox();

    return Column(
      children: [
        Row(
          children: [
            IntrinsicWidth(
              child: ConstrainedBox(
                constraints: const BoxConstraints(
                  minWidth: 60,
                ),
                child: Container(
                  height: 1,
                  color: const Color(0xFFDCDCDC),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                text,
                textScaler: MediaQuery.textScalerOf(context).clamp(
                  minScaleFactor: 1.0,
                  maxScaleFactor: 1.4,
                ),
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Color(0xff8F8F8F),
                ),
              ),
            ),
            const SizedBox(width: 8),
            IntrinsicWidth(
              child: ConstrainedBox(
                constraints: const BoxConstraints(
                  minWidth: 60,
                ),
                child: Container(
                  height: 1,
                  color: const Color(0xFFDCDCDC),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _middleSection(
      {required String autoPayCancelText,
      ExistingPremiumUsers? existingPremiumUsers}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          if (existingPremiumUsers != null) ...[
            ExistingPremiumUsersWidget(
                existingPremiumUsers: existingPremiumUsers,
                source: 'annual_recharge_paywall'),
            const SizedBox(height: 48),
          ],
          _autoPayCancelTextWidget(text: autoPayCancelText),
        ],
      ),
    );
  }

  Widget _plansUI({required List<PlanItem> plans}) {
    return LiveDataBuilder(
        liveData: viewModel.selectedPlanId,
        builder: (context, id) {
          return AnnualPaywallPlansUI(
            plans: plans,
            selectedPlanId: id,
            onPlanSelected: (planId) {
              viewModel.updatedSelectedSubscriptionItemId(planId);
            },
          );
        });
  }

  Widget _endSection({
    required int planId,
    required PremiumExperienceButtonDetails buttonDetails,
    PosterTerms? terms,
  }) {
    return Container(
      height: 112,
      color: Colors.white,
      padding: const EdgeInsets.only(left: 16, right: 16, top: 8),
      child: Column(
        children: [
          LiveDataBuilder(
            liveData: viewModel.selectedPlanId,
            builder: (context, id) {
              return Row(
                children: [
                  LiveDataBuilder<UpiApp?>(
                      liveData: viewModel.preselectedUpiApp,
                      builder: (_, upiApp) {
                        if (upiApp == null) return const SizedBox();
                        return Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 12),
                          child: PayWithUpiAppWidget(
                            upiApp: upiApp,
                            onTap: () async {
                              final upiApp = await pickUpiApp(context);
                              if (upiApp != null) {
                                viewModel.onUpiAppSelected(upiApp);
                              }
                            },
                          ),
                        );
                      }),
                  Expanded(
                    child: LiveDataBuilder<PaymentStatus>(
                        liveData: viewModel.paymentStatus,
                        builder: (_, paymentStatus) {
                          return PremiumExperienceButtonUI(
                            buttonDetails: buttonDetails,
                            loading: paymentStatus == PaymentStatus.pending,
                            onPressed: viewModel.onCheckoutButtonClicked,
                          );
                        }),
                  ),
                ],
              );
            },
          ),
          if (terms != null) ...[
            const SizedBox(height: 12),
            PaymentTermsUI(terms: terms),
          ],
        ],
      ),
    );
  }

  Widget _getBody(
      {required AnnualRechargePaywallState state,
      required BuildContext context}) {
    if (state is AnnualRechargePaywallLoading) {
      return const Center(
        child: CircularProgressIndicator(color: Color(0xFFBF951E)),
      );
    } else if (state is AnnualRechargePaywallSuccess) {
      final response = state.response;
      final pageParams = {
        'source': widget.source,
        ...response.analyticsParams ?? {},
      };
      _pageViewTracker.onPageLoaded(pageParams);
      AppAnalytics.onAnnualPaywallScreenLoaded(params: pageParams);
      return Container(
        width: double.infinity,
        decoration: BoxDecoration(
          gradient: PremiumUtils.premiumNavigationDrawerBgGradients(),
        ),
        child: Stack(
          children: [
            Column(
              children: [
                Container(
                  height: 168,
                  width: double.infinity,
                  padding: const EdgeInsets.only(top: 48),
                  decoration: const BoxDecoration(
                    gradient: PremiumUtils.premiumScreenBgGradient,
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const SizedBox(height: 16),
                      Text(
                        response.title,
                        textScaler: MediaQuery.textScalerOf(context).clamp(
                          minScaleFactor: 1.0,
                          maxScaleFactor: 1.2,
                        ),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Color(0xffD3AF4A),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        response.subTitle,
                        textScaler: MediaQuery.textScalerOf(context).clamp(
                          minScaleFactor: 1.0,
                          maxScaleFactor: 1.2,
                        ),
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Color(0xff696969),
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.only(top: 4),
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(24),
                        topRight: Radius.circular(24),
                      ),
                    ),
                    child: ScrollConfiguration(
                      behavior: CustomScrollBehaviour(),
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            const SizedBox(height: 32),
                            PaywallCircularBlocksWidget(
                                payWallBlocks: response.payWall),
                            const SizedBox(height: 48 + 12),
                            _plansUI(plans: response.plans),
                            const SizedBox(height: 48),
                            _middleSection(
                              autoPayCancelText: response.autoPayCancelText,
                              existingPremiumUsers:
                                  response.existingPremiumUsers,
                            ),
                            // end section height + padding [will help on scrolling]
                            const SizedBox(height: 112 + 24),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: _endSection(
                planId: viewModel.planId,
                buttonDetails: response.buttonDetails,
                terms: response.terms,
              ),
            ),
          ],
        ),
      );
    } else if (state is AnnualRechargePaywallError) {
      AppAnalytics.onAnnualPaywallScreenError(
          source: widget.source, errorMessage: state.errorMessage);
      final buttonDetails = PremiumExperienceButtonDetails(
        text: context.getString(StringKey.retryLabel),
        type: PremiumExperienceButtonType.deeplink,
        autoPerformAction: false,
        autoPerformActionTime: 0,
        deeplink: '',
        disabled: false,
        discountText: '',
        discountTextColor: 0,
        textSuffix: '',
        apiUrl: '',
        analyticsParams: {},
      );
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            PremiumUtils.premiumCrownGoldIconWidget(iconSize: 60),
            const SizedBox(height: 20),
            Text(
              state.errorMessage,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: PremiumUtils.premiumHeaderTitleColor,
              ),
            ),
            const SizedBox(height: 20),
            PremiumExperienceButtonUI(
              buttonDetails: buttonDetails,
              onPressed: () {
                viewModel.onRetry();
              },
            ),
          ],
        ),
      );
    } else {
      return const SizedBox();
    }
  }

  Future<void> onEvent(
      BuildContext context, SubscriptionHandlerEvent event) async {
    final paymentFailedText =
        context.getString(StringKey.yourPaymentFailedText, listen: false);
    if (event is SubscriptionHandlerUrlCheckoutInitiated) {
      printDebug("Annual Recharge Paywall: Checkout started");
      AppAnalytics.onPremiumPaymentInitiated(
        source: 'annual_recharge_paywall',
        planId: viewModel.planId,
        params: viewModel.getAnalyticsParams(),
      );
      _appStateSubscription =
          AppState.lifecycleStateStream.listen(_onAppStateChanged);
      await launchUrl(Uri.parse(event.url),
          mode: LaunchMode.externalApplication);
    } else if (event is SubscriptionHandlerIntentCheckoutInitiated) {
      printDebug("Annual Recharge Paywall: Intent Checkout started");
      AppAnalytics.onPremiumPaymentInitiated(
        source: 'annual_recharge_paywall',
        planId: viewModel.planId,
        params: viewModel.getAnalyticsParams(),
      );
      _appStateSubscription =
          AppState.lifecycleStateStream.listen(_onAppStateChanged);
      try {
        final uri = Uri.parse(event.intentUrl);
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } catch (e, st) {
        logNonFatalIfAppError(
            "Error launching intent URL in annual recharge paywall", e,
            stackTrace: st);
        AppAnalytics.onPremiumPaymentFailed(
            source: 'annual_recharge_paywall',
            planId: viewModel.planId,
            params: {...viewModel.getAnalyticsParams(), 'error': e.toString()});
        Utils.showToast(paymentFailedText);
      }
    } else if (event is SubscriptionHandlerFailure) {
      AppAnalytics.onPremiumPaymentFailed(
        source: 'annual_recharge_paywall',
        planId: viewModel.planId,
        params: viewModel.getAnalyticsParams(),
      );
      Utils.showToast(event.message ?? paymentFailedText);
    } else if (event is SubscriptionHandlerCheckoutSuccess) {
      navigateToPremiumSuccessScreen(
        context,
        source: 'annual_recharge_paywall',
        returnNavigationUrl: widget.returnUrl,
      );
    } else if (event is SubscriptionHandlerNotifyError) {
      Utils.showToast(event.message);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion(
      value: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
      child: LiveDataBuilder<bool>(
        liveData: viewModel.shouldInterceptBackPresses,
        builder: (ctx, shouldIntercept) => PopScope(
          canPop: !shouldIntercept,
          onPopInvoked: (bool popped) async {
            if (popped) {
              viewModel.onPaywallScreenClosed(source: widget.source);
            }

            if (shouldIntercept) {
              await viewModel.onBackPressed();
            }
          },
          child: EventListener(
            eventQueue: viewModel.eventQueue,
            onEvent: onEvent,
            child: Material(
              child: Container(
                width: double.infinity,
                color: Colors.white,
                child: Stack(
                  children: [
                    LiveDataBuilder(
                        liveData: viewModel.state,
                        builder: (context, state) {
                          return _getBody(state: state, context: context);
                        }),
                    Positioned(
                      top: 42,
                      left: 12,
                      child: InkWell(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        child: const Padding(
                          padding: EdgeInsets.all(4.0),
                          child: Icon(
                            Icons.close,
                            color: PremiumUtils.premiumHeaderTitleColor,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
