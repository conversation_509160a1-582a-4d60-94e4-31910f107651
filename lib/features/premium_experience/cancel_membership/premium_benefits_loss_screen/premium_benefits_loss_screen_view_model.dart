import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/errors/error_delegate.dart';
import 'package:praja/features/premium_experience/models/premium_benefits_loss_screen_response.dart';
import 'package:praja/features/premium_experience/services/premium_experience_service.dart';
import 'package:praja/mixins/analytics.dart';

@injectable
class PremiumBenefitsLossScreenViewModel extends ViewModel {
  final PremiumExperienceService _premiumExperienceService;

  PremiumBenefitsLossScreenViewModel(this._premiumExperienceService);

  final MutableLiveData<PremiumBenefitsLossScreenState> _state =
      MutableLiveData(PremiumBenefitsLossScreenLoading());
  LiveData<PremiumBenefitsLossScreenState> get state => _state;

  final MutableLiveData<bool> _isExtendingPlan = MutableLiveData(false);
  LiveData<bool> get isExtendingPlan => _isExtendingPlan;

  final MutableEventQueue<PremiumBenefitsLossScreenEvent> _eventQueue =
      MutableEventQueue();
  EventQueue<PremiumBenefitsLossScreenEvent> get eventQueue => _eventQueue;

  late String source;

  bool _isInitialized = false;
  void init({required String source}) {
    if (_isInitialized) return;
    this.source = source;
    _fetchPremiumBenefitsLossScreen();
    _isInitialized = true;
  }

  void _fetchPremiumBenefitsLossScreen() async {
    _state.value = PremiumBenefitsLossScreenLoading();
    try {
      final response =
          await _premiumExperienceService.getPremiumBenefitsLossScreen();
      _state.value = PremiumBenefitsLossScreenSuccess(response);
    } catch (e) {
      AppAnalytics.onPremiumBenefitsLossScreenFailed(
        source: source,
        errorMessage: e.toString(),
      );
      _state.value = PremiumBenefitsLossScreenError(localisedErrorMessage(e));
    }
  }

  void retry() {
    _fetchPremiumBenefitsLossScreen();
  }

  Map<String, dynamic>? _getExtendButtonAnalyticsParams() {
    Map<String, dynamic>? params;
    final currentState = state.value;
    if (currentState is PremiumBenefitsLossScreenSuccess) {
      params = {
        ...currentState.response.analyticsParams ?? {},
        ...currentState.response.extendPlanButton?.analyticsParams ?? {},
      };
    }
    return params;
  }

  void onExtendPlan({required String apiUrl}) async {
    if (_isExtendingPlan.value) return;

    _isExtendingPlan.value = true;
    try {
      await _premiumExperienceService.extendOrSwitchPlan(apiUrl);
      AppAnalytics.onMembershipExtended(
        source: 'premium_benefits_loss_screen',
        params: _getExtendButtonAnalyticsParams(),
      );
      _eventQueue.push(PlanExtendedSuccessEvent());
    } catch (e) {
      AppAnalytics.onExtendingMembershipFailed(
        source: 'premium_benefits_loss_screen',
        errorMessage: e.toString(),
        params: _getExtendButtonAnalyticsParams(),
      );
      _eventQueue.push(FailedToExtendPlanEvent(localisedErrorMessage(e)));
    } finally {
      _isExtendingPlan.value = false;
    }
  }
}

abstract class PremiumBenefitsLossScreenState {}

class PremiumBenefitsLossScreenLoading extends PremiumBenefitsLossScreenState {}

class PremiumBenefitsLossScreenSuccess extends PremiumBenefitsLossScreenState {
  final PremiumBenefitsLossScreenResponse response;

  PremiumBenefitsLossScreenSuccess(this.response);
}

class PremiumBenefitsLossScreenError extends PremiumBenefitsLossScreenState {
  final String message;

  PremiumBenefitsLossScreenError(this.message);
}

// Events
abstract class PremiumBenefitsLossScreenEvent {}

class PlanExtendedSuccessEvent extends PremiumBenefitsLossScreenEvent {}

class FailedToExtendPlanEvent extends PremiumBenefitsLossScreenEvent {
  final String message;

  FailedToExtendPlanEvent(this.message);
}

extension PremiumBenefitsLossScreenViewModelX on BuildContext {
  PremiumBenefitsLossScreenViewModel premiumBenefitsLossScreenViewModel(
      {required String source}) {
    final viewModel = getViewModel<PremiumBenefitsLossScreenViewModel>(
        key: 'premium_benefits_loss_screen');
    viewModel.init(source: source);
    return viewModel;
  }
}
