import 'package:flutter/material.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/common/deeplink_params.dart';
import 'package:praja/features/deeplinks/destination.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/features/premium_experience/models/cancel_membership_response.dart';
import 'package:praja/features/premium_experience/models/premium_experience_button_details.dart';
import 'package:praja/features/premium_experience/premium_success_screen/premium_success_screen.dart';
import 'package:praja/features/premium_experience/premium_utils.dart';
import 'package:praja/features/premium_experience/widgets/premium_experience_button_ui.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/models/admin_user.dart';
import 'package:praja/styles.dart';
import 'package:praja/utils/utils.dart';

import 'cancel_membership_helper_widgets.dart';
import 'cancel_membership_sheet_view_model.dart';

/// we are having cancel membership and cancel reasons sheet in this same file
/// because, the cancel membership responses contains cancel reasons,
/// where will show on clicking cancel membership button

extension CancelMembershipX on BuildContext {
  Future<void> showCancelMembershipBottomSheet({required String source}) async {
    await showModalBottomSheet(
      context: this,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      enableDrag: false,
      builder: (context) {
        return Wrap(
          children: [_CancelMembershipBottomSheetBody(source: source)],
        );
      },
    );
  }

  Future<void> showCancelReasonsSheet(
      {required String source,
      required AdminUser? rmUser,
      required CancelReasons cancelReasons,
      required String subscriptionId,
      required CancelMembershipSheetViewModel viewModel,
      Map<String, dynamic>? analyticsParams}) async {
    await showModalBottomSheet(
      context: this,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      enableDrag: false,
      builder: (context) {
        return Wrap(
          children: [
            _CancelReasonsSheetBody(
              source: source,
              rmUser: rmUser,
              cancelReasons: cancelReasons,
              subscriptionId: subscriptionId,
              analyticsParams: analyticsParams,
              viewModel: viewModel,
            )
          ],
        );
      },
    );
  }
}

class _CancelMembershipBottomSheetBody extends StatelessWidget {
  final String source;

  const _CancelMembershipBottomSheetBody({required this.source});

  Widget _extendFreeMembershipButtonUI({
    required PremiumExperienceButtonDetails? extendButton,
    required bool isLoading,
    void Function()? onTap,
  }) {
    if (extendButton == null) return const SizedBox.shrink();
    return Column(
      children: [
        CancelMembershipHelperWidgets.extendOrSwitchPlanButtonUI(
          text: extendButton.text,
          isSwitchingOrExtending: isLoading,
          onTap: onTap,
        ),
        const SizedBox(height: 10),
      ],
    );
  }

  Widget _continueMembershipButtonWidget({
    required PremiumExperienceButtonDetails? continueMembershipButton,
    required bool isDisabled,
    void Function()? onTap,
  }) {
    if (continueMembershipButton == null) return const SizedBox.shrink();

    return Column(
      children: [
        PremiumExperienceButtonUI(
          buttonDetails: continueMembershipButton,
          onPressed: onTap,
          disabled: isDisabled,
        ),
        const SizedBox(height: 10),
      ],
    );
  }

  Widget _getBody(
      {required CancelMembershipSheetViewModel viewModel,
      required BuildContext context}) {
    final state = viewModel.state.value;
    if (state is CancelMembershipSheetLoading) {
      return const SizedBox(
        height: 270,
        child: Center(
          child: SizedBox(
              height: 30,
              width: 30,
              child: CircularProgressIndicator(color: Colors.black)),
        ),
      );
    } else if (state is CancelMembershipSheetSuccess) {
      final cancelMembershipResponse = state.cancelMembershipResponse;
      final rmUser = cancelMembershipResponse.rmUser;

      AppAnalytics.onCancelMembershipSheetLoaded(
          source: source, params: cancelMembershipResponse.analyticsParams);

      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Premium gradient header with title, close and call buttons
          Container(
            height: 160,
            width: double.infinity,
            decoration: const BoxDecoration(
              gradient: PremiumUtils.premiumScreenBgGradient,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(24),
                topRight: Radius.circular(24),
              ),
            ),
            child: Stack(
              children: [
                // Close button on left
                Positioned(
                  left: 16,
                  top: 16,
                  child: InkWell(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: const Icon(
                      Icons.close,
                      color: Color(0xFFD3AF4A),
                      size: 24,
                    ),
                  ),
                ),
                // Premium crown and title in center
                Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      PremiumUtils.premiumCrownGoldIconWidget(iconSize: 30),
                      const SizedBox(height: 12),
                      Text(
                        cancelMembershipResponse.title,
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w700,
                          color: PremiumUtils.premiumHeaderTitleColor,
                        ),
                      ),
                    ],
                  ),
                ),
                // Call button on right
                if (rmUser != null)
                  Positioned(
                    right: 16,
                    top: 16,
                    child: CancelMembershipHelperWidgets.rmUserWidget(
                      rmUser: rmUser,
                      context: context,
                      source: 'cancel_membership_sheet',
                      analyticsParams: cancelMembershipResponse.analyticsParams,
                    ),
                  ),
              ],
            ),
          ),
          // Content area
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
            decoration: const BoxDecoration(
              color: Colors.white,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Main text
                if (cancelMembershipResponse.text.isNotEmpty) ...[
                  Text(
                    cancelMembershipResponse.text,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFFAA6F1C),
                    ),
                  ),
                  const SizedBox(height: 4),
                ],

                // Sub text (conditional)
                if (cancelMembershipResponse.subText.isNotEmpty) ...[
                  const Divider(color: Color(0xFFEBEBEB), thickness: 1),
                  const SizedBox(height: 12),
                  Text(
                    cancelMembershipResponse.subText,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      letterSpacing: -0.011, // -1.1%
                      color: Color(0xFFAA6F1C),
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
              ],
            ),
          ),
          // Buttons container
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            decoration: const BoxDecoration(color: Colors.white),
            child: LiveDataBuilder(
              liveData: viewModel.isExtendingPlan,
              builder: (context, isExtendingPlan) {
                return Column(
                  children: [
                    // Extend plan button
                    _extendFreeMembershipButtonUI(
                      extendButton: cancelMembershipResponse.extendPlanButton,
                      isLoading: isExtendingPlan,
                      onTap: () {
                        AppAnalytics.onExtendMembershipClicked(
                            source: 'cancel_membership_sheet',
                            params: {
                              ...cancelMembershipResponse.analyticsParams ?? {},
                              ...cancelMembershipResponse
                                      .extendPlanButton?.analyticsParams ??
                                  {},
                            });
                        final extendButton =
                            cancelMembershipResponse.extendPlanButton;
                        if (extendButton != null &&
                            extendButton.type ==
                                PremiumExperienceButtonType.api &&
                            extendButton.apiUrl.isNotEmpty) {
                          viewModel.onExtendPlan(
                            apiUrl: extendButton.apiUrl,
                            source: source,
                          );
                        }
                      },
                    ),
                    // Continue membership button (using PremiumExperienceButtonUI)
                    _continueMembershipButtonWidget(
                      continueMembershipButton:
                          cancelMembershipResponse.continueMembership,
                      isDisabled: isExtendingPlan,
                      onTap: () {
                        AppAnalytics.onContinueMembershipClicked(
                          source: 'cancel_membership_sheet',
                          params: {
                            ...cancelMembershipResponse.analyticsParams ?? {},
                            ...cancelMembershipResponse
                                    .continueMembership?.analyticsParams ??
                                {},
                          },
                        );
                        Navigator.of(context).pop();
                      },
                    ),
                    // Cancel membership button (same as before)
                    CancelMembershipHelperWidgets.cancelMembershipButtonUI(
                      text:
                          cancelMembershipResponse.cancelMembershipButton.text,
                      isDisabled: isExtendingPlan,
                      onTap: () async {
                        AppAnalytics.onCancelMembershipClicked(
                          source: 'cancel_membership_sheet',
                          params: {
                            ...cancelMembershipResponse.analyticsParams ?? {},
                            ...cancelMembershipResponse
                                .cancelMembershipButton.analyticsParams,
                          },
                        );
                        Navigator.of(context).pop();
                        await context.showCancelReasonsSheet(
                          source: source,
                          rmUser: rmUser,
                          viewModel: viewModel,
                          cancelReasons: cancelMembershipResponse.cancelReasons,
                          subscriptionId:
                              cancelMembershipResponse.subscriptionId,
                          analyticsParams:
                              cancelMembershipResponse.analyticsParams,
                        );
                      },
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      );
    } else if (state is CancelMembershipSheetError) {
      AppAnalytics.onCancelMembershipSheetError(
          source: source, errorMessage: state.message);
      return SizedBox(
          height: 270,
          width: double.infinity,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 20),
              Text(
                state.message,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.black,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 20),
              Container(
                height: 48,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Styles.circleIndigo, width: 1),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      context.getString(StringKey.retryLabel),
                      style: const TextStyle(
                        color: Styles.circleIndigo,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(width: 4),
                    const Icon(Icons.refresh, color: Styles.circleIndigo),
                  ],
                ),
              ),
            ],
          ));
    } else {
      return const SizedBox.shrink();
    }
  }

  @override
  Widget build(BuildContext context) {
    final viewModel =
        context.cancelMembershipSheetViewModel('cancel_membership');
    return PopScope(
      canPop: true,
      onPopInvoked: (bool popped) {
        if (popped) {
          viewModel.onCancelMembershipSheetClosed(source: source);
        }
      },
      child: EventListener(
        eventQueue: viewModel.eventQueue,
        onEvent: (context, event) async {
          if (event is PlanExtendedSuccessEvent) {
            // Navigate to success screen & No need to pop the sheet, as there is no back button in success screen
            navigateToPremiumSuccessScreen(
              context,
              source: 'cancel_membership_sheet',
            );
          } else if (event is FailedToExtendPlanEvent) {
            Utils.showToast(event.message);
          }
        },
        child: LiveDataBuilder(
          liveData: viewModel.state,
          builder: (context, state) {
            return _getBody(viewModel: viewModel, context: context);
          },
        ),
      ),
    );
  }
}

class _CancelReasonsSheetBody extends StatelessWidget {
  final String source;
  final AdminUser? rmUser;
  final CancelReasons cancelReasons;
  final String subscriptionId;
  final CancelMembershipSheetViewModel viewModel;
  final Map<String, dynamic>? analyticsParams;

  const _CancelReasonsSheetBody({
    required this.source,
    required this.rmUser,
    required this.cancelReasons,
    required this.subscriptionId,
    required this.viewModel,
    this.analyticsParams,
  });

  Widget _cancelReasonUi({
    required String reason,
    bool isSelected = false,
    void Function()? onTap,
  }) {
    return Column(
      children: [
        InkWell(
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    reason,
                    style: TextStyle(
                      fontSize: 14,
                      color: isSelected
                          ? const Color(0xFFD3AF4A)
                          : const Color(0xFF8F8F8F),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  height: 18,
                  width: 18,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isSelected
                        ? const Color(0xFFD3AF4A)
                        : const Color(0xFFEBEBEB),
                  ),
                  child: Icon(
                    Icons.check_sharp,
                    color: isSelected
                        ? const Color(0xFF222222)
                        : const Color(0xFFB8B8B8),
                    size: 14,
                  ),
                )
              ],
            ),
          ),
        ),
        Divider(
          color: isSelected ? const Color(0xFFD3AF4A) : const Color(0xFFEBEBEB),
          thickness: 1,
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _cancelMembershipButtonUI(
      {required String text, void Function()? onTap}) {
    return LiveDataBuilder(
      liveData: viewModel.isCancelling,
      builder: (context, cancelling) {
        return LiveDataBuilder(
          liveData: viewModel.selectedReason,
          builder: (context, reason) {
            final bool showDisableUI = reason.isEmpty;
            final String warnText =
                context.getString(StringKey.selectAtLeastOneCancelReasonText);
            return InkWell(
              onTap: showDisableUI
                  ? () {
                      Utils.showToast(warnText);
                    }
                  : cancelling
                      ? () {}
                      : onTap,
              child: CancelMembershipHelperWidgets.cancelMembershipButtonUI(
                text: text,
                onTap: null,
                isDisabled: showDisableUI,
                cancelling: cancelling,
              ),
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final rmUser = this.rmUser;
    return PopScope(
      canPop: true,
      onPopInvoked: (bool popped) {
        if (popped) {
          viewModel.onCancelReasonsSheetClosed(
              source: source, cancelReasons: cancelReasons.reasons);
        }
      },
      child: EventListener(
        eventQueue: viewModel.eventQueue,
        onEvent: (context, event) async {
          if (event is MembershipCancelledEvent) {
            AppAnalytics.onMembershipCancelled(
                source: source, params: analyticsParams);

            // Navigate to the deeplink if present
            final deeplink = cancelReasons.button.deeplink;
            if (deeplink.isNotEmpty) {
              Navigator.of(context).pop();
              DeeplinkDestination.fromRoute(deeplink)
                  ?.go(context, DeeplinkSource.internalDeeplink);
            } else {
              Navigator.of(context).pop();
            }
          } else if (event is FailedToCancelMembershipEvent) {
            AppAnalytics.onCancellingMembershipFailed(
                source: source,
                errorMessage: event.message,
                params: analyticsParams);
            Utils.showToast(event.message);
          }
        },
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Premium gradient header with title, close and call buttons
            Container(
              height: 140,
              width: double.infinity,
              decoration: const BoxDecoration(
                gradient: PremiumUtils.premiumScreenBgGradient,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24),
                ),
              ),
              child: Stack(
                children: [
                  // Close button on left
                  Positioned(
                    left: 16,
                    top: 16,
                    child: InkWell(
                      onTap: () {
                        Navigator.of(context).pop();
                      },
                      child: const Icon(
                        Icons.close,
                        color: Color(0xFFD3AF4A),
                        size: 24,
                      ),
                    ),
                  ),
                  // Premium crown and title in center
                  Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        PremiumUtils.premiumCrownGoldIconWidget(iconSize: 28),
                        const SizedBox(height: 12),
                        Text(
                          cancelReasons.title,
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w700,
                            color: PremiumUtils.premiumHeaderTitleColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Call button on right
                  if (rmUser != null)
                    Positioned(
                      right: 16,
                      top: 16,
                      child: CancelMembershipHelperWidgets.rmUserWidget(
                        rmUser: rmUser,
                        context: context,
                        source: 'cancel_reasons_sheet',
                        analyticsParams: analyticsParams,
                      ),
                    ),
                ],
              ),
            ),
            // Text below header
            if (cancelReasons.subTitle.isNotEmpty ||
                cancelReasons.text.isNotEmpty)
              Container(
                width: double.infinity,
                margin: const EdgeInsets.only(top: 32),
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (cancelReasons.subTitle.isNotEmpty) ...[
                      Text(
                        cancelReasons.subTitle,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFFAA6F1C),
                        ),
                      ),
                      const Divider(color: Color(0xFFEBEBEB), thickness: 1),
                      const SizedBox(height: 8),
                    ],
                    Text(
                      cancelReasons.text,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFFAA6F1C),
                      ),
                    ),
                  ],
                ),
              ),
            // Content area
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
              child: Column(
                children: [
                  LiveDataBuilder(
                      liveData: viewModel.selectedReason,
                      builder: (context, reason) {
                        return Column(
                          children: cancelReasons.reasons
                              .map(
                                (reason) => _cancelReasonUi(
                                  reason: reason,
                                  isSelected:
                                      reason == viewModel.selectedReason.value,
                                  onTap: () {
                                    AppAnalytics.onCancelReasonSelected(
                                        source: source,
                                        reason: reason,
                                        params: analyticsParams);
                                    viewModel.onReasonSelected(reason);
                                  },
                                ),
                              )
                              .toList(),
                        );
                      }),
                  const SizedBox(height: 28),
                  _cancelMembershipButtonUI(
                    text: cancelReasons.button.text,
                    onTap: () {
                      viewModel.onCancelMembership(
                          subscriptionId: subscriptionId);
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
