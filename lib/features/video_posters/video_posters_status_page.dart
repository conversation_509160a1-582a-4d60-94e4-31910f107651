import 'package:flutter/material.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/common/widgets/adaptive_back_arrow_icon.dart';
import 'package:praja/common/widgets/animated_progress_bar.dart';
import 'package:praja/core/ui/page.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/features/video_posters/video_posters_status_view_model.dart';
import 'package:praja/presentation/praja_icons.dart';
import 'package:praja/styles.dart';

import 'models/video_poster_with_status.dart';

class VideoPostersStatusPage extends BasePage {
  final int videoPosterId;
  final String source;
  final ShareMethod? initiateShareMethod;
  const VideoPostersStatusPage({
    super.key,
    required this.videoPosterId,
    required this.source,
    this.initiateShareMethod,
  });

  @override
  String get pageName => "video_poster_status";

  String _formatDuration(int seconds) {
    final hours = seconds ~/ 3600;
    final minutes = (seconds % 3600) ~/ 60;
    final remainingSeconds = seconds % 60;

    return [
      if (hours > 0) hours.toString().padLeft(2, '0'),
      minutes.toString().padLeft(2, '0'),
      remainingSeconds.toString().padLeft(2, '0')
      // ignore: avoid_hardcoded_strings_in_ui
    ].join(':');
  }

  String _getStatusText(
    BuildContext context,
    VideoPosterStatus videoPosterStatus,
  ) {
    switch (videoPosterStatus) {
      case VideoPosterStatus.generationPending:
        return context.getString(StringKey.waitingLabel);
      case VideoPosterStatus.generationProcessing:
        return context.getString(StringKey.processingLabel);
      case VideoPosterStatus.generationFailed:
        return context.getString(StringKey.failedLabel);
      case VideoPosterStatus.downloadFailed:
        return context.getString(StringKey.failedLabel);
      case VideoPosterStatus.downloadCancelled:
        return context.getString(StringKey.somethingWentWrongText);
      case VideoPosterStatus.downloadCompleted:
        return context.getString(StringKey.downloadCompletedLabel);
      case VideoPosterStatus.downloadUndefined:
        return context.getString(StringKey.downloadingLabel);
      case VideoPosterStatus.downloadEnqueued:
        return context.getString(StringKey.downloadingLabel);
      case VideoPosterStatus.downloadRunning:
        return context.getString(StringKey.downloadingLabel);
      case VideoPosterStatus.downloadPaused:
        return context.getString(StringKey.downloadingLabel);
    }
  }

  Widget _getBody(BuildContext context, VideoPostersStatusState state,
      VideoPostersStatusViewModel viewModel) {
    {
      if (state is VideoPostersStatusLoading) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      } else if (state is VideoPostersStatusError) {
        return Center(
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  state.message,
                  textAlign: TextAlign.center,
                  style: const TextStyle(color: Colors.white),
                ),
                const SizedBox(height: 12),
                ElevatedButton.icon(
                  onPressed: () => viewModel.onRetryClicked(videoPosterId),
                  label: const Text('Retry'),
                  icon: const Icon(Icons.refresh),
                ),
              ],
            ),
          ),
        );
      } else if (state is VideoPostersStatusSuccess) {
        // final videoFrame = state.videoFrame;
        const videoDuration = 10; // removed source video
        final videoPosterStatus = state.videoPosterStatus;
        final clampedTextScaler = MediaQuery.of(context)
            .textScaler
            .clamp(minScaleFactor: 1, maxScaleFactor: 1.5);
        return Stack(
          children: [
            Column(
              children: [
                Expanded(
                  flex: 8,
                  child: Padding(
                    //To make the thumbnail appear in the center of the screen added a padding of 100 which is the height of the other child
                    padding: const EdgeInsets.only(top: 100.0),
                    child: Center(
                      child: Stack(
                        children: [
                          // ClipRRect(
                          //   borderRadius: BorderRadius.circular(10),
                          //   child: VideoPosterWidget(
                          //     sourceVideo: AspectRatio(
                          //       aspectRatio:
                          //           state.videoWidth / state.videoHeight,
                          //       child: CachedNetworkImage(
                          //         imageUrl:
                          //             state.response.sourceVideo.thumbnailUrl,
                          //         cacheManager: AppCacheManager.instance,
                          //       ),
                          //     ),
                          //     videoFrame: videoFrame,
                          //   ),
                          // ),
                          Positioned(
                            bottom: 0,
                            child: Container(
                              height: 50,
                              width: 50,
                              decoration: BoxDecoration(
                                gradient: RadialGradient(
                                  colors: [
                                    Colors.black.withOpacity(0.5),
                                    Colors.transparent,
                                  ],
                                  center: Alignment.bottomLeft,
                                  stops: const [0.8, 1.0],
                                  radius: 1,
                                ),
                              ),
                              child: const Padding(
                                padding: EdgeInsets.only(top: 10, right: 16),
                                child: Icon(
                                  Icons.play_arrow_rounded,
                                  size: 30,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                          Positioned(
                            bottom: 0,
                            right: 0,
                            child: Container(
                              height: 50,
                              width: 50,
                              decoration: BoxDecoration(
                                gradient: RadialGradient(
                                  colors: [
                                    Colors.black.withOpacity(0.5),
                                    Colors.transparent,
                                  ],
                                  center: Alignment.bottomRight,
                                  stops: const [0.8, 1],
                                  radius: 1,
                                ),
                              ),
                              child: Padding(
                                padding:
                                    const EdgeInsets.only(top: 24, left: 10),
                                child: Text(
                                  // videoDuration == null
                                  //     ? " "
                                  //     :
                                  _formatDuration(videoDuration),
                                  style: const TextStyle(
                                    fontSize: 14,
                                    color: Colors.white,
                                  ),
                                  textScaler: const TextScaler.linear(1.0),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                Expanded(
                  flex: 1,
                  child: Stack(
                    children: [
                      Container(
                        height: 100,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.1),
                        ),
                      ),
                      Positioned(
                        top: 8,
                        left: 0,
                        right: 0,
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              _getStatusText(
                                context,
                                videoPosterStatus,
                              ),
                              textScaler: clampedTextScaler,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 18,
                              ),
                            ),
                            const SizedBox(
                              width: 6,
                            ),
                            if (videoPosterStatus ==
                                    VideoPosterStatus.generationPending ||
                                videoPosterStatus ==
                                    VideoPosterStatus.generationProcessing)
                              const SizedBox(
                                width: 18,
                                height: 18,
                                child: CircularProgressIndicator(),
                              ),
                          ],
                        ),
                      ),
                      Positioned(
                        top: 30,
                        left: 0,
                        right: 0,
                        child: Container(
                          child: (videoPosterStatus ==
                                  VideoPosterStatus.downloadRunning)
                              ? LiveDataBuilder(
                                  liveData: viewModel.progress,
                                  builder: (context, progress) {
                                    return Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: AnimatedProgressBar(
                                        value: progress / 100,
                                        borderRadius: BorderRadius.circular(10),
                                        minHeight: 8,
                                      ),
                                    );
                                  },
                                )
                              : const SizedBox(),
                        ),
                      ),
                      Positioned(
                        top: 40,
                        left: 0,
                        right: 0,
                        child: Container(
                          margin: const EdgeInsets.symmetric(
                            horizontal: 10,
                          ),
                          child: (state.videoPosterStatus ==
                                      VideoPosterStatus.downloadCompleted &&
                                  initiateShareMethod == ShareMethod.none)
                              ? Row(
                                  children: [
                                    Expanded(
                                      flex: 1,
                                      child: ElevatedButton.icon(
                                        label: Padding(
                                          padding: const EdgeInsets.symmetric(
                                              vertical: 10),
                                          child: Text(
                                            context.getString(
                                                StringKey.shareLabel),
                                            textScaler: clampedTextScaler,
                                          ),
                                        ),
                                        icon: const Icon(
                                          Icons.share,
                                          size: 18,
                                        ),
                                        onPressed: () {
                                          viewModel
                                              .onShareClicked(videoPosterId);
                                        },
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor:
                                              const Color(0xFF292929),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(
                                      width: 4.0,
                                    ),
                                    Expanded(
                                      flex: 1,
                                      child: ElevatedButton.icon(
                                        label: Padding(
                                          padding: const EdgeInsets.symmetric(
                                              vertical: 10.0),
                                          child: Text(
                                            context.getString(
                                                StringKey.whatsappLabel),
                                            textScaler: clampedTextScaler,
                                          ),
                                        ),
                                        icon: const Icon(
                                          PrajaIcons.whatsapp_fill,
                                          size: 18,
                                        ),
                                        onPressed: () {
                                          viewModel
                                              .onWhatsappClicked(videoPosterId);
                                        },
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor:
                                              const Color(0xff25D366),
                                        ),
                                      ),
                                    )
                                  ],
                                )
                              : const SizedBox(),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Positioned(
              child: SafeArea(
                child: Align(
                  alignment: Alignment.topLeft,
                  child: InkWell(
                    onTap: () => Navigator.pop(context),
                    child: Container(
                      margin: const EdgeInsets.only(
                        left: 10,
                        top: 20,
                      ),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.black.withOpacity(0.5),
                      ),
                      child: const AdaptiveBackArrowIcon(),
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      }
      return const SizedBox();
    }
  }

  @override
  Widget buildContent(BuildContext context) {
    final viewModel = context.viewModel(videoPosterId, initiateShareMethod);
    return Theme(
      data: ThemeData.dark(useMaterial3: false).copyWith(
        colorScheme: ColorScheme.fromSwatch(
            brightness: Brightness.dark,
            backgroundColor: Colors.black,
            primarySwatch: Styles.circleIndigo),
      ),
      child: LiveDataBuilder(
        liveData: viewModel.state,
        builder: (context, state) => Scaffold(
          backgroundColor: Colors.black,
          body: _getBody(context, state, viewModel),
        ),
      ),
    );
  }
}

extension on BuildContext {
  VideoPostersStatusViewModel viewModel(
          int id, ShareMethod? initiateShareMethod) =>
      getViewModel<VideoPostersStatusViewModel>()
        ..init(id, initiateShareMethod);
}

enum ShareMethod {
  whatsapp,
  other,
  none,
}
