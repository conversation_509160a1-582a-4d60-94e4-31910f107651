import 'dart:async';

import 'package:injectable/injectable.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/features/posters/widgets/poster_layout_share_destination.dart';
import 'package:praja/features/video_posters/models/video_frame.dart';
import 'package:praja/features/video_posters/models/video_poster.dart';
import 'package:praja/features/video_posters/models/video_poster_with_status.dart';
import 'package:praja/features/video_posters/video_posters_service.dart';
import 'package:praja/features/video_posters/video_posters_status_page.dart';
import 'package:praja/features/whatsapp_share/whatsapp_share.dart';
import 'package:praja/utils/logger.dart';
import 'package:share_plus/share_plus.dart';

@injectable
class VideoPostersStatusViewModel extends ViewModel {
  final VideoPostersService _videoPostersService;

  VideoPostersStatusViewModel(
    this._videoPostersService,
  );

  final MutableLiveData<VideoPostersStatusState> _state =
      MutableLiveData(VideoPostersStatusLoading());
  LiveData<VideoPostersStatusState> get state => _state;

  final MutableLiveData<int> _progress = MutableLiveData(0);
  LiveData<int> get progress => _progress;

  final MutableEventQueue<VideoPostersStatusEvent> _eventQueue =
      MutableEventQueue();
  EventQueue<VideoPostersStatusEvent> get eventQueue => _eventQueue;

  bool _isInitialized = false;
  StreamSubscription? _videoPosterStatusSubscription;
  ShareMethod? _initiateShareMethod;

  void init(int videoPosterId, ShareMethod? initiateShareMethod) {
    if (_isInitialized) return;
    _isInitialized = true;
    if (initiateShareMethod != null) {
      _initiateShareMethod = initiateShareMethod;
    }
    fetchVideoDetails(videoPosterId);
  }

  void onRetryClicked(int videoPosterId) {
    fetchVideoDetails(videoPosterId);
  }

  Future<void> onShareClicked(int videoPosterId) async {
    try {
      final downloadFilePath =
          await _videoPostersService.getDownloadedFilePath(videoPosterId);
      await Share.shareXFiles(
        [XFile(downloadFilePath)],
        text: "Praja Video Poster",
      );

      // Record share and emit event with deeplink
      final deeplinkUrl = await _videoPostersService.recordShare(
        shareDestination: PosterLayoutShareDestination.externalShare,
        videoPosterId: videoPosterId,
      );

      _eventQueue.push(VideoPostersStatusShareEvent(deeplinkUrl: deeplinkUrl));
    } catch (e) {
      logNonFatalIfAppError("Error sharing video poster", e);
    }
  }

  Future<void> onWhatsappClicked(int videoPosterId) async {
    try {
      final downloadFilePath =
          await _videoPostersService.getDownloadedFilePath(videoPosterId);
      await WhatsappShareAndroid.shareFiles(
        [XFile(downloadFilePath)],
        text: "Praja Video Poster",
      );

      // Record share and emit event with deeplink
      final deeplinkUrl = await _videoPostersService.recordShare(
        shareDestination: PosterLayoutShareDestination.whatsapp,
        videoPosterId: videoPosterId,
      );

      _eventQueue.push(VideoPostersStatusShareEvent(deeplinkUrl: deeplinkUrl));
    } catch (e) {
      logNonFatalIfAppError("Error sharing video poster to WhatsApp", e);
    }
  }

  Future<void> fetchVideoDetails(int videoPosterId) async {
    _state.value = VideoPostersStatusLoading();
    try {
      final response =
          await _videoPostersService.getVideoPosterWithStatus(videoPosterId);
      // final videoPoster = response.videoPoster;
      final videoPosterStatus = response.videoPosterStatus;
      // int? videoWidth = videoPoster.video?.videoWidth ?? 1080;
      // int? videoHeight = videoPoster.video?.videoHeight ?? 1920;

      // if (videoWidth == null || videoHeight == null) {
      //   final thumbnail = await AppCacheManager.instance
      //       .getSingleFile(videoPoster.video?.thumbnailUrl);
      //   final size = await ImageUtils.getImageSize(thumbnail);
      //   videoWidth = size.width.toInt();
      //   videoHeight = size.height.toInt();
      // }
      // final videoFrame = await _videoPostersService.getVideoFrameById(
      //   videoPoster.videoFrameId,
      //   videoWidth,
      //   videoHeight,
      // );
      // _state.value = VideoPostersStatusSuccess(
      //   videoFrame: videoFrame,
      //   response: videoPoster,
      //   videoWidth: videoWidth,
      //   videoHeight: videoHeight,
      //   videoPosterStatus: videoPosterStatus,
      // );
      if (videoPosterStatus == VideoPosterStatus.downloadCompleted) {
        _triggerShareMethod(
          _initiateShareMethod,
          videoPosterId,
        );
      } else {
        listenToVideoPosterStatusEvent(videoPosterId);
      }
    } catch (e) {
      _state.value = VideoPostersStatusError(e.toString());
    }
  }

  void listenToVideoPosterStatusEvent(int videoPosterId) {
    _videoPosterStatusSubscription?.cancel();
    _videoPosterStatusSubscription = _videoPostersService.stream
        .where((event) => event.videoPosterId == videoPosterId)
        .listen(
      (event) {
        if (event is VideoPosterStatusUpdate) {
          final currentState = _state.value;
          if (currentState is VideoPostersStatusSuccess) {
            if (currentState.videoPosterStatus != event.videoPosterStatus) {
              _state.value = currentState.copyWith(
                  videoPosterStatus: event.videoPosterStatus);
            }
            if (event.videoPosterStatus ==
                VideoPosterStatus.downloadCompleted) {
              _triggerShareMethod(
                _initiateShareMethod,
                videoPosterId,
              );
            }
          }
        } else if (event is VideoPosterDownloadProgressUpdate) {
          _progress.value = event.progress;
        }
      },
    );
  }

  void _triggerShareMethod(
      ShareMethod? initiateShareMethod, int videoPosterId) {
    if (initiateShareMethod == ShareMethod.whatsapp) {
      onWhatsappClicked(videoPosterId);
    } else if (initiateShareMethod == ShareMethod.other) {
      onShareClicked(videoPosterId);
    } else if (initiateShareMethod == ShareMethod.none) {
      // For download completion without immediate sharing
      _recordShareAndNavigate(
        videoPosterId,
        PosterLayoutShareDestination.download,
      );
    }
  }

  Future<void> _recordShareAndNavigate(
    int videoPosterId,
    PosterLayoutShareDestination shareDestination,
  ) async {
    final deeplinkUrl = await _videoPostersService.recordShare(
      shareDestination: shareDestination,
      videoPosterId: videoPosterId,
    );

    // Navigate to deeplink if provided
    if (deeplinkUrl != null && deeplinkUrl.isNotEmpty) {
      await _navigationService.navigateTo(deeplinkUrl);
    }
  }

  @override
  void onDispose() {
    _videoPosterStatusSubscription?.cancel();
    super.onDispose();
  }
}

sealed class VideoPostersStatusState {}

class VideoPostersStatusLoading extends VideoPostersStatusState {}

class VideoPostersStatusError extends VideoPostersStatusState {
  final String message;
  VideoPostersStatusError(this.message);
}

class VideoPostersStatusSuccess extends VideoPostersStatusState {
  final VideoFrame videoFrame;
  final VideoPoster response;
  final int videoWidth;
  final int videoHeight;
  final VideoPosterStatus videoPosterStatus;
  VideoPostersStatusSuccess({
    required this.videoFrame,
    required this.response,
    required this.videoWidth,
    required this.videoHeight,
    required this.videoPosterStatus,
  });

  VideoPostersStatusSuccess copyWith({
    VideoFrame? videoFrame,
    VideoPoster? response,
    int? videoWidth,
    int? videoHeight,
    VideoPosterStatus? videoPosterStatus,
  }) {
    return VideoPostersStatusSuccess(
      videoFrame: videoFrame ?? this.videoFrame,
      response: response ?? this.response,
      videoWidth: videoWidth ?? this.videoWidth,
      videoHeight: videoHeight ?? this.videoHeight,
      videoPosterStatus: videoPosterStatus ?? this.videoPosterStatus,
    );
  }
}

sealed class VideoPostersStatusEvent {}
