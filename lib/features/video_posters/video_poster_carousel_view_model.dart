import 'dart:async';

import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/errors/error_delegate.dart';
import 'package:praja/features/posters/widgets/poster_layout_share_destination.dart';
import 'package:praja/features/video_posters/enums/video_poster_action_method.dart';
import 'package:praja/features/video_posters/models/video_poster_with_status.dart';
import 'package:praja/features/video_posters/video_posters_service.dart';
import 'package:praja/utils/logger.dart';

@injectable
class VideoPosterCarouselViewModel extends ViewModel {
  final VideoPostersService _videoPostersService;

  VideoPosterCarouselViewModel(
    this._videoPostersService,
  );

  final MutableLiveData<VideoPosterCarouselState> _state =
      MutableLiveData(VideoPosterCarouselIdle());
  LiveData<VideoPosterCarouselState> get state => _state;

  final MutableLiveData<int> _progress = MutableLiveData(0);
  LiveData<int> get progress => _progress;

  final MutableEventQueue<VideoPosterCarouselEvent> _eventQueue =
      MutableEventQueue();
  EventQueue<VideoPosterCarouselEvent> get eventQueue => _eventQueue;

  late int _frameId;
  late int _videoCreativeId;
  StreamSubscription? _videoPosterStatusSubscription;
  VideoPosterActionMethod? _pendingActionMethod;
  int? _currentVideoPosterId;
  Timer? _successTimer;
  bool _isInitialized = false;
  String _shareText = "";
  VoidCallback? _onOperationStarted;
  VoidCallback? _onOperationEnded;

  /// Getter to expose pending action method for UI
  VideoPosterActionMethod? get pendingActionMethod => _pendingActionMethod;

  void _init({
    required int frameId,
    required int videoCreativeId,
    String? shareText,
    VoidCallback? onOperationStarted,
    VoidCallback? onOperationEnded,
  }) {
    if (_isInitialized) return;
    _frameId = frameId;
    _videoCreativeId = videoCreativeId;
    _shareText = shareText ?? _shareText;
    _onOperationStarted = onOperationStarted;
    _onOperationEnded = onOperationEnded;
    _isInitialized = true;
  }

  Future<void> onDownloadClicked() async {
    await _generateAndDownloadVideoPosters(VideoPosterActionMethod.download);
  }

  Future<void> onShareClicked() async {
    await _generateAndDownloadVideoPosters(VideoPosterActionMethod.other);
  }

  Future<void> onWhatsappClicked() async {
    await _generateAndDownloadVideoPosters(VideoPosterActionMethod.whatsapp);
  }

  void onRetryClicked() {
    if (_currentVideoPosterId != null) {
      fetchVideoDetails(_currentVideoPosterId!);
    } else {
      _generateAndDownloadVideoPosters(_pendingActionMethod);
    }
  }

  void onCloseErrorClicked() {
    _state.value = VideoPosterCarouselIdle();
    _pendingActionMethod = null;
    _currentVideoPosterId = null;
    _successTimer?.cancel();

    // Notify parent that operation ended
    _onOperationEnded?.call();
  }

  Future<void> _generateAndDownloadVideoPosters(
      VideoPosterActionMethod? actionMethod) async {
    final currentState = _state.value;
    if (currentState is VideoPosterCarouselGenerating ||
        currentState is VideoPosterCarouselDownloading) return;

    // Store action method for retry functionality
    _pendingActionMethod = actionMethod;

    // Reset progress
    _progress.value = 0;

    // Start with generating state
    _state.value = VideoPosterCarouselGenerating();

    // Notify parent that operation started
    _onOperationStarted?.call();

    try {
      // Call the service to generate and download video posters
      final videoPosterId =
          await _videoPostersService.generateAndDownloadVideoPosters(
        _frameId,
        _videoCreativeId,
      );

      _currentVideoPosterId = videoPosterId;

      // Listen to the service stream for status updates
      // The service handles internal socket listening and timer fallbacks
      listenToVideoPosterStatusEvent(videoPosterId);

      printDebug('Video poster generation started for ID: $videoPosterId');
    } catch (e) {
      printDebug('Video poster generation failed: $e');
      _state.value = VideoPosterCarouselError(localisedErrorMessage(e));
      _resetState();

      // Notify parent that operation ended
      _onOperationEnded?.call();
    }
  }

  // Add fetchVideoDetails method for retry functionality
  Future<void> fetchVideoDetails(int videoPosterId) async {
    _state.value = VideoPosterCarouselGenerating();
    try {
      final response =
          await _videoPostersService.getVideoPosterWithStatus(videoPosterId);
      final videoPosterStatus = response.videoPosterStatus;

      printDebug('Fetched video poster status: $videoPosterStatus');

      if (videoPosterStatus == VideoPosterStatus.downloadCompleted) {
        _state.value = VideoPosterCarouselSuccess();
        _triggerActionMethod(_pendingActionMethod, videoPosterId);
        _autoHideSuccess();
        // Don't reset state here - it will be reset when success UI is hidden

        // Notify parent that operation ended (success)
        _onOperationEnded?.call();
      } else {
        // Continue listening for status updates
        listenToVideoPosterStatusEvent(videoPosterId);
        _updateStateFromStatus(videoPosterStatus);
      }
    } catch (e) {
      printDebug('Failed to fetch video details: $e');
      _state.value = VideoPosterCarouselError(localisedErrorMessage(e));

      // Notify parent that operation ended (error)
      _onOperationEnded?.call();
    }
  }

  void listenToVideoPosterStatusEvent(int videoPosterId) {
    _videoPosterStatusSubscription?.cancel();
    _videoPosterStatusSubscription = _videoPostersService.stream
        .where((event) => event.videoPosterId == videoPosterId)
        .listen(
      (event) {
        if (event is VideoPosterStatusUpdate) {
          _handleStatusUpdate(event.videoPosterStatus, videoPosterId);
        } else if (event is VideoPosterDownloadProgressUpdate) {
          _progress.value = event.progress;
        }
      },
      onError: (e) {
        printDebug('Video poster status stream error: $e');
        _state.value = VideoPosterCarouselError(localisedErrorMessage(e));
      },
    );
  }

  void _handleStatusUpdate(VideoPosterStatus status, int videoPosterId) {
    printDebug('Video poster status update: $status');
    _updateStateFromStatus(status);

    if (status == VideoPosterStatus.downloadCompleted) {
      _state.value = VideoPosterCarouselSuccess();
      _triggerActionMethod(_pendingActionMethod, videoPosterId);
      _autoHideSuccess();
      // Don't reset state here - it will be reset when success UI is hidden

      // Notify parent that operation ended (success)
      _onOperationEnded?.call();
    } else if (status == VideoPosterStatus.generationFailed ||
        status == VideoPosterStatus.downloadFailed ||
        status == VideoPosterStatus.downloadCancelled) {
      final errorMessage = _getErrorMessage(status);
      _state.value = VideoPosterCarouselError(errorMessage);
      // Don't reset state here to allow retry functionality

      // Notify parent that operation ended (error)
      _onOperationEnded?.call();
    }
  }

  void _updateStateFromStatus(VideoPosterStatus status) {
    switch (status) {
      case VideoPosterStatus.generationPending:
      case VideoPosterStatus.generationProcessing:
        _state.value = VideoPosterCarouselGenerating();
        break;

      case VideoPosterStatus.downloadEnqueued:
      case VideoPosterStatus.downloadRunning:
        _state.value = VideoPosterCarouselDownloading();
        break;

      case VideoPosterStatus.downloadUndefined:
      case VideoPosterStatus.downloadPaused:
        // Keep current state for these intermediate states
        break;

      default:
        // Handle other states in _handleStatusUpdate
        break;
    }
  }

  String _getErrorMessage(VideoPosterStatus status) {
    switch (status) {
      case VideoPosterStatus.generationFailed:
        return 'Video poster generation failed';
      case VideoPosterStatus.downloadFailed:
        return 'Video poster download failed';
      case VideoPosterStatus.downloadCancelled:
        return 'Video poster download was cancelled';
      default:
        return 'An error occurred';
    }
  }

  void _autoHideSuccess() {
    _successTimer?.cancel();
    _successTimer = Timer(const Duration(seconds: 2), () {
      if (_state.value is VideoPosterCarouselSuccess) {
        _state.value = VideoPosterCarouselIdle();
        _resetState(); // Reset state after hiding success UI

        // Note: We don't call _onOperationEnded here because it was already called
        // when the success state was first set
      }
    });
  }

  void _resetState() {
    _pendingActionMethod = null;
    _currentVideoPosterId = null;
  }

  Future<void> _triggerActionMethod(
      VideoPosterActionMethod? actionMethod, int videoPosterId) async {
    try {
      printDebug(
          'Triggering action method: $actionMethod for video poster: $videoPosterId');

      // Record share to get deeplink
      final deeplinkUrl = await _recordShare(actionMethod, videoPosterId);

      // For sharing actions, emit share event with deeplink
      if (actionMethod != null &&
          actionMethod != VideoPosterActionMethod.download) {
        final downloadFilePath =
            await _videoPostersService.getDownloadedFilePath(videoPosterId);

        // Emit share event with destination and deeplink
        switch (actionMethod) {
          case VideoPosterActionMethod.whatsapp:
          case VideoPosterActionMethod.other:
            _eventQueue.push(
              VideoPosterShareEvent(
                filePath: downloadFilePath,
                shareText: _shareText,
                destination: actionMethod,
                deeplinkUrl: deeplinkUrl,
              ),
            );
            printDebug('Share event emitted for: $actionMethod');
            break;
          case VideoPosterActionMethod.download:
            // Already handled above
            break;
        }
      } else {
        // For download-only, emit event with deeplink for navigation
        _eventQueue.push(
          VideoPosterDownloadCompleteEvent(deeplinkUrl: deeplinkUrl),
        );
      }
    } catch (e) {
      printDebug('Failed to execute action method: $e');
      _state.value = VideoPosterCarouselError(localisedErrorMessage(e));
    }
  }

  Future<String?> _recordShare(
    VideoPosterActionMethod? actionMethod,
    int videoPosterId,
  ) async {
    // Map action method to share destination
    final shareDestination = _mapActionMethodToShareDestination(actionMethod);

    return await _videoPostersService.recordShare(
      shareDestination: shareDestination,
      videoPosterId: videoPosterId,
    );
  }

  PosterLayoutShareDestination _mapActionMethodToShareDestination(
    VideoPosterActionMethod? actionMethod,
  ) {
    switch (actionMethod) {
      case VideoPosterActionMethod.whatsapp:
        return PosterLayoutShareDestination.whatsapp;
      case VideoPosterActionMethod.other:
        return PosterLayoutShareDestination.externalShare;
      case VideoPosterActionMethod.download:
      case null:
        return PosterLayoutShareDestination.download;
    }
  }

  @override
  void onDispose() {
    _videoPosterStatusSubscription?.cancel();
    _successTimer?.cancel();
    super.onDispose();
  }
}

/// Represents the different states of video poster carousel during generation and download process
sealed class VideoPosterCarouselState {}

/// Initial state when no operation is in progress
class VideoPosterCarouselIdle extends VideoPosterCarouselState {}

/// State when video poster is being generated on the server
class VideoPosterCarouselGenerating extends VideoPosterCarouselState {}

/// State when video poster is being downloaded with progress tracking
class VideoPosterCarouselDownloading extends VideoPosterCarouselState {}

/// State when video poster generation and download completed successfully
class VideoPosterCarouselSuccess extends VideoPosterCarouselState {}

/// State when an error occurred during generation or download process
class VideoPosterCarouselError extends VideoPosterCarouselState {
  final String message;
  VideoPosterCarouselError(this.message);
}

/// Events for video poster carousel actions
sealed class VideoPosterCarouselEvent {}

/// Event to trigger sharing with specified destination
class VideoPosterShareEvent extends VideoPosterCarouselEvent {
  final String filePath;
  final String shareText;
  final VideoPosterActionMethod destination;

  ///This Deeplink is to redirect user to premium experience screen [only for users who came via Ad]
  final String? deeplinkUrl;

  VideoPosterShareEvent({
    required this.filePath,
    required this.shareText,
    required this.destination,
    this.deeplinkUrl,
  });
}

/// Event for download completion with deeplink navigation
class VideoPosterDownloadCompleteEvent extends VideoPosterCarouselEvent {
  ///This Deeplink is to redirect user to premium experience screen [only for users who came via Ad]
  final String? deeplinkUrl;

  VideoPosterDownloadCompleteEvent({this.deeplinkUrl});
}

extension VideoPosterCarouselViewModelX on BuildContext {
  VideoPosterCarouselViewModel videoPosterCarouselViewModel({
    required int frameId,
    required int videoCreativeId,
    required String key,
    String? shareText,
    VoidCallback? onOperationStarted,
    VoidCallback? onOperationEnded,
  }) {
    return getViewModel<VideoPosterCarouselViewModel>(key: key)
      .._init(
        frameId: frameId,
        videoCreativeId: videoCreativeId,
        shareText: shareText,
        onOperationStarted: onOperationStarted,
        onOperationEnded: onOperationEnded,
      );
  }
}
