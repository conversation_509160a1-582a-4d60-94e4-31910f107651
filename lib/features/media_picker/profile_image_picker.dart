import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get_it/get_it.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:praja/features/image_compressor.dart';
import 'package:praja/features/posters/widgets/poster_constants.dart';
import 'package:praja/models/v2/local_image.dart';
import 'package:praja/styles.dart';
import 'package:praja/utils/logger.dart';

import 'media_picker.dart';

class ProfilePicResult {
  final LocalImage image;
  final MediaPickerType type;

  ProfilePicResult({
    required this.image,
    required this.type,
  });
}

Future<ProfilePicResult?> pickProfilePicture(BuildContext context,
    {MediaPickerType? type}) async {
  MediaPickerType? pickerType =
      type ?? await pickMediaType(context: context, allowVideo: false);

  if (pickerType == null) {
    return null;
  }

  final imagePicker = ImagePicker();
  final source = pickerType == MediaPickerType.camera
      ? ImageSource.camera
      : ImageSource.gallery;
  XFile? pickedFile;
  try {
    pickedFile =
        await imagePicker.pickImage(source: source, requestFullMetadata: false);
  } catch (e, st) {
    pickedFile = null;
    if (e is PlatformException && e.code == 'camera_access_denied') {
      // nothing to do
    } else {
      logNonFatal("Error picking profile picture using source: $source", e,
          stackTrace: st);
    }
  }

  if (pickedFile == null) {
    return null;
  }

  final compressor = GetIt.I.get<ImageCompressor>();
  final compressionResult = await compressor.compress(File(pickedFile.path));

  final LocalImage compressedPhoto = LocalImage(path: compressionResult.path);
  final croppedFile = await ImageCropper().cropImage(
      aspectRatio: const CropAspectRatio(
        ratioX: 1,
        ratioY: 1,
      ),
      sourcePath: compressedPhoto.path,
      uiSettings: [
        AndroidUiSettings(
          toolbarColor: Styles.circleIndigo,
          toolbarWidgetColor: Colors.white,
          statusBarColor: Styles.circleIndigo,
          activeControlsWidgetColor: Styles.circleIndigo,
          cropStyle: CropStyle.circle,
        ),
        IOSUiSettings(
          cropStyle: CropStyle.circle,
          aspectRatioLockEnabled: true,
        )
      ]);

  if (croppedFile != null) {
    return ProfilePicResult(
        image: compressedPhoto.copyWith(path: croppedFile.path),
        type: pickerType);
  } else {
    return null;
  }
}

Future<ProfilePicResult?> pickPictureForPosterPhoto(BuildContext context,
    {MediaPickerType? type}) async {
  MediaPickerType? pickerType =
      type ?? await pickMediaType(context: context, allowVideo: false);

  if (pickerType == null) {
    return null;
  }

  final imagePicker = ImagePicker();
  final source = pickerType == MediaPickerType.camera
      ? ImageSource.camera
      : ImageSource.gallery;
  XFile? pickedFile;
  try {
    pickedFile =
        await imagePicker.pickImage(source: source, requestFullMetadata: false);
  } catch (e, st) {
    pickedFile = null;
    if (e is PlatformException && e.code == 'camera_access_denied') {
      // nothing to do
    } else {
      logNonFatal("Error picking profile picture using source: $source", e,
          stackTrace: st);
    }
  }

  if (pickedFile == null) {
    return null;
  }

  final compressor = GetIt.I.get<ImageCompressor>();
  final compressionResult = await compressor.compress(File(pickedFile.path));

  final LocalImage compressedPhoto = LocalImage(path: compressionResult.path);
  final croppedFile = await ImageCropper().cropImage(
      aspectRatio: const CropAspectRatio(
        ratioX: posterUserPhotoWidth,
        ratioY: posterUserPhotoHeight,
      ),
      sourcePath: compressedPhoto.path,
      uiSettings: [
        AndroidUiSettings(
          toolbarColor: Colors.black,
          toolbarWidgetColor: Colors.white,
          statusBarColor: Colors.black,
          activeControlsWidgetColor: Styles.circleIndigo,
          cropStyle: CropStyle.rectangle,
        ),
        IOSUiSettings(
          cropStyle: CropStyle.rectangle,
          aspectRatioLockEnabled: true,
          // aspectRatioLockDimensionSwapEnabled:
        ),
      ]);

  if (croppedFile != null) {
    return ProfilePicResult(
        image: compressedPhoto.copyWith(path: croppedFile.path),
        type: pickerType);
  } else {
    return null;
  }
}
