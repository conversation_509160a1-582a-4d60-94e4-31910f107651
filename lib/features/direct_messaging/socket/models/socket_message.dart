import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';
import 'package:praja/features/direct_messaging/database/entities/db_message.dart';
import 'package:praja/features/direct_messaging/models/message_data.dart';
import 'package:praja/utils/utc_date_time_converter.dart';

part 'socket_message.g.dart';

@JsonSerializable()
class SocketMessage {
  final String id;
  final String conversationId;
  final NormalMessageData messageData;

  /// The time at which the message was sent in ISO 8601 format
  @UtcDateTimeConverter()
  final DateTime sentAt;

  SocketMessage({
    required this.id,
    required this.conversationId,
    required this.messageData,
    required this.sentAt,
  });

  factory SocketMessage.fromDbMessage(DbMessage dbMessage) {
    return SocketMessage(
      id: dbMessage.id,
      conversationId: dbMessage.conversationId,
      messageData:
          NormalMessageData.fromJson(json.decode(dbMessage.messageData)),
      sentAt: dbMessage.sentAt,
    );
  }

  factory SocketMessage.fromJson(Map<String, dynamic> json) =>
      _$SocketMessageFromJson(json);

  Map<String, dynamic> toJson() => _$SocketMessageToJson(this);

  @override
  String toString() {
    return 'SocketMessage{id: $id, conversationId: $conversationId, messageData: $messageData, sentAt: $sentAt}';
  }
}
