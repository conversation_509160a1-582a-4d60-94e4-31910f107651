import 'dart:io';
import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:praja/features/direct_messaging/models/attachment.dart';
import 'package:praja/features/direct_messaging/ui/chat_ui_constants.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/services/app_cache_manager.dart';

typedef OnAttachmentClick = void Function(Attachment attachment);

class ImageAttachmentsUI extends StatelessWidget {
  final List<Attachment> attachments;
  final OnAttachmentClick? onAttachmentClick;
  final VoidCallback? onClick;
  final bool toBeSent;
  final double? progress;
  final double borderRadius;
  final bool elevated;

  const ImageAttachmentsUI(
      {super.key,
      required this.attachments,
      required this.toBeSent,
      this.elevated = false,
      this.borderRadius = ChatUiConstants.bubbleBorderRadius,
      this.progress,
      this.onClick,
      this.onAttachmentClick});

  // default cache with high stale duration
  static final CacheManager _cacheManager = AppCacheManager.instance;

  Widget _image(Attachment attachment) {
    final data = attachment.data;
    if (data is ImageAttachmentData) {
      return CachedNetworkImage(
        imageUrl: data.url,
        cacheManager: _cacheManager,
        fit: BoxFit.cover,
        width: double.infinity,
        height: double.infinity,
      );
    } else if (data is AssetImageAttachmentData) {
      return Image.file(
        File(data.path),
        width: double.infinity,
        height: double.infinity,
        fit: BoxFit.cover,
      );
    } else {
      return const SizedBox();
    }
  }

  Widget _buildImage(Attachment attachment) {
    return GestureDetector(
      onTap: () {
        if (onAttachmentClick != null) {
          onAttachmentClick!(attachment);
        }
      },
      child: _image(attachment),
    );
  }

  Widget _buildImageGrid(BuildContext context) {
    if (attachments.isEmpty) {
      return const SizedBox();
    }

    if (attachments.length == 1) {
      return AspectRatio(
          aspectRatio: max(attachments.first.aspectRatio, 0.75),
          child: _buildImage(attachments.first));
    } else if (attachments.length == 2) {
      return AspectRatio(
          aspectRatio: 2,
          child: Row(
            mainAxisSize: MainAxisSize.max,
            children: [
              Expanded(
                child: _buildImage(attachments.first),
              ),
              Expanded(
                child: _buildImage(attachments.last),
              ),
            ],
          ));
    } else if (attachments.length == 3) {
      return AspectRatio(
          aspectRatio: 2,
          child: Row(
            mainAxisSize: MainAxisSize.max,
            children: [
              Expanded(
                child: _buildImage(attachments.first),
              ),
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Expanded(
                      child: _buildImage(attachments[1]),
                    ),
                    Expanded(
                      child: _buildImage(attachments[2]),
                    ),
                  ],
                ),
              ),
            ],
          ));
    } else if (attachments.length == 4) {
      return AspectRatio(
          aspectRatio: 2,
          child: Row(
            mainAxisSize: MainAxisSize.max,
            children: [
              Expanded(
                child: _buildImage(attachments.first),
              ),
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Expanded(
                      child: _buildImage(attachments[1]),
                    ),
                    Expanded(
                        child: Row(mainAxisSize: MainAxisSize.max, children: [
                      Expanded(
                        child: _buildImage(attachments[2]),
                      ),
                      Expanded(
                        child: _buildImage(attachments[3]),
                      ),
                    ]))
                  ],
                ),
              ),
            ],
          ));
    } else {
      return AspectRatio(
          aspectRatio: 2,
          child: Row(
            mainAxisSize: MainAxisSize.max,
            children: [
              Expanded(
                child: _buildImage(attachments.first),
              ),
              Expanded(
                child: Row(children: [
                  Expanded(
                      child: Column(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Expanded(
                        child: _buildImage(attachments[1]),
                      ),
                      Expanded(
                        child: _buildImage(attachments[2]),
                      ),
                    ],
                  )),
                  Expanded(
                      child: Column(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Expanded(
                        child: _buildImage(attachments[3]),
                      ),
                      Expanded(
                        child: _buildImage(attachments[4]),
                      ),
                    ],
                  )),
                ]),
              ),
            ],
          ));
    }
  }

  @override
  Widget build(BuildContext context) {
    final bool toBeUploaded = toBeSent &&
        (attachments
                .where((e) => e.type == AttachmentType.assetImage)
                .isNotEmpty ||
            progress != null);
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFE0E0E0),
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: elevated
            ? [
                BoxShadow(
                  color: Colors.black.withOpacity(0.25),
                  blurRadius: 1,
                  offset: const Offset(0, 1),
                )
              ]
            : null,
      ),
      child: ClipRRect(
          borderRadius: BorderRadius.circular(borderRadius),
          child: Stack(alignment: Alignment.bottomCenter, children: [
            _buildImageGrid(context),
            if (attachments.length > 1 && !toBeUploaded)
              Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.5),
                  ),
                  child: Material(
                      color: Colors.black.withOpacity(0.5),
                      child: InkWell(
                          onTap: () {
                            onClick?.call();
                          },
                          child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 8),
                              child: Row(children: [
                                Text(
                                  context.getString(StringKey.seePhotosLabel),
                                  style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500),
                                ),
                                const SizedBox(width: 8),
                                const Icon(Icons.arrow_forward,
                                    color: Colors.white, size: 20),
                              ]))))),
            if (toBeUploaded)
              Positioned.fill(
                  child: Container(
                decoration: BoxDecoration(color: Colors.black.withOpacity(0.5)),
                child: Center(
                    child: SizedBox(
                        width: 36,
                        height: 36,
                        child: CircularProgressIndicator(
                            value: progress, color: Colors.white))),
              ))
          ])),
    );
  }
}

extension on Attachment {
  double get aspectRatio {
    final d = data;
    if (d is AssetImageAttachmentData) {
      return d.height != 0 ? d.width / d.height : 1;
    } else if (d is ImageAttachmentData) {
      return d.height != 0 ? d.width / d.height : 1;
    } else {
      return 1;
    }
  }
}
