import 'package:flutter/material.dart';
import 'package:praja/features/direct_messaging/models/attachment.dart';
import 'package:praja/features/direct_messaging/ui/reply_attachment_ui.dart';

class ReplyDraftAttachmentUI extends StatelessWidget {
  final MessageAttachmentData attachmentData;
  final VoidCallback? onRemove;

  const ReplyDraftAttachmentUI({
    super.key,
    required this.attachmentData,
    this.onRemove,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedSize(
        duration: const Duration(milliseconds: 200),
        child: Container(
            padding: const EdgeInsets.all(8),
            margin: const EdgeInsets.only(bottom: 4),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: Colors.black.withOpacity(0.05)),
            child: Row(children: [
              Expanded(
                  child: ReplyAttachmentUI(attachmentData: attachmentData)),
              GestureDetector(
                  onTap: onRemove,
                  child: Container(
                      width: 24,
                      height: 24,
                      margin: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.black,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Center(
                          child: Icon(Icons.close,
                              color: Colors.white, size: 16))))
            ])));
  }
}
