import 'package:flutter/widgets.dart';
import 'package:injectable/injectable.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/errors/error_delegate.dart';
import 'package:praja/exceptions/api_exception.dart';
import 'package:praja/models/link_preview.dart';
import 'package:praja/services/link/link_service.dart';
import 'package:praja/utils/logger.dart';

@injectable
class LinkPreviewViewModel extends ViewModel {
  final LinkService service;

  LinkPreviewViewModel(this.service);

  final MutableLiveData<LinkPreviewState> _state =
      MutableLiveData(LinkPreviewState.loading);

  LiveData<LinkPreviewState> get state => _state;

  bool _isInitialized = false;

  Future<void> _init(String url) async {
    if (_isInitialized) return;

    _isInitialized = true;
    try {
      final linkPreview = await service.getLinkPreview(url);
      _state.value = LinkPreviewState.loaded(linkPreview);
    } catch (e, stackTrace) {
      printDebug('Failure while fetching link preview for $url in DM',
          error: e, level: LogLevel.error, stackTrace: stackTrace);
      _state.value = LinkPreviewState.failed(e);
    }
  }
}

extension LinkPreviewViewModelX on BuildContext {
  LinkPreviewViewModel linkPreviewViewModel(String url) =>
      getViewModel<LinkPreviewViewModel>(key: url).._init(url);
}

abstract class LinkPreviewState {
  const LinkPreviewState();

  static const loading = LinkPreviewLoading();

  factory LinkPreviewState.loaded(LinkPreview linkPreview) = LinkPreviewLoaded;

  factory LinkPreviewState.failed(Object error) = LinkPreviewFailed;
}

class LinkPreviewLoading extends LinkPreviewState {
  const LinkPreviewLoading();
}

class LinkPreviewLoaded extends LinkPreviewState {
  final LinkPreview linkPreview;

  const LinkPreviewLoaded(this.linkPreview);
}

class LinkPreviewFailed extends LinkPreviewState {
  final Object error;

  const LinkPreviewFailed(this.error);

  String get message {
    final e = error;
    if (e is ApiException) {
      if (e.failureType == ApiFailureType.notFound) {
        return 'Link does not exist';
      }
    }

    return localisedErrorMessage(error);
  }
}
