import 'dart:io';
import 'dart:ui';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:injectable/injectable.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/common/deeplink_params.dart';
import 'package:praja/errors/error_delegate.dart';
import 'package:praja/features/deeplinks/destination.dart';
import 'package:praja/features/direct_messaging/models/attachment.dart';
import 'package:praja/features/direct_messaging/models/ui_conversation.dart';
import 'package:praja/features/direct_messaging/ui/chat_ui_constants.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/features/posters/services/poster_service.dart';
import 'package:praja/features/posters/widgets/category_preview_widget.dart';
import 'package:praja/features/whatsapp_share/whatsapp_share.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/models/create_poster_preview.dart';
import 'package:praja/presentation/circle_display_picture.dart';
import 'package:praja/presentation/praja_icons.dart';
import 'package:praja/services/app_cache_manager.dart';
import 'package:praja/utils/utils.dart';
import 'package:share_plus/share_plus.dart';

class CreatePosterAttachmentUI extends StatelessWidget {
  final CreatePosterAttachmentData attachmentData;
  final double borderRadius;
  final double width;

  /// Conversation in which the attachment is being viewed.
  /// used to omit the circle image again if the attachment is already being
  /// viewed in the same circle's conversation
  final UIConversation conversation;

  const CreatePosterAttachmentUI(this.attachmentData,
      {super.key,
      required this.width,
      required this.conversation,
      this.borderRadius = ChatUiConstants.bubbleBorderRadius});

  void onClick(BuildContext context, CreatePosterPreviewState state) {
    if (state is! CreatePosterPreviewSuccess) {
      return;
    }

    AppAnalytics.onPosterCategoryClicked(
      index: 0,
      source: 'chat',
      parameters: {
        ...state.previewData.analyticsParams,
        "disabled": state.previewData.disableCtaMessage.isNotEmpty
      },
    );
    if (state.previewData.disableCtaMessage.isNotEmpty) {
      Utils.showToast(state.previewData.disableCtaMessage,
          toastLength: Toast.LENGTH_LONG);
    } else {
      final deeplink = state.previewData.deeplink;
      if (deeplink.isEmpty) return;
      DeeplinkDestination.fromRoute(deeplink)
          ?.go(context, DeeplinkSource.internalDeeplink);
    }
  }

  void onLongPress(BuildContext context, CreatePosterPreviewState state) async {
    if (state is! CreatePosterPreviewSuccess) {
      return;
    }

    if (state.previewData.disableCtaMessage.isNotEmpty) {
      Utils.showToast(state.previewData.disableCtaMessage,
          toastLength: Toast.LENGTH_LONG);
    } else if (state.previewData.shareText.isEmpty) {
      onClick(context, state);
    } else {
      AppAnalytics.logShare(
        contentType: 'poster_creative',
        itemId:
            state.previewData.params['creative_id']?.toString() ?? 'unknown',
        method: 'whatsapp',
        otherParameters: {
          ...state.previewData.analyticsParams,
          "source": 'chat',
        },
      );

      if (Platform.isAndroid) {
        await WhatsappShareAndroid.shareText(state.previewData.shareText);
      } else {
        await Share.share(state.previewData.shareText);
      }
    }
  }

  bool _shouldShowCircleImage(CreatePosterPreviewSuccess state) {
    final conversation = this.conversation;
    if (conversation == null) return true;
    if (state.previewData.circle?.id == 0) return false;

    if (conversation is UIChannelConversation) {
      return conversation.circleIdentity.id != state.previewData.circle?.id;
    } else if (conversation is UIPrivateGroupConversation) {
      return conversation.circleIdentity.id != state.previewData.circle?.id;
    } else {
      return true;
    }
  }

  @override
  Widget build(BuildContext context) {
    final viewModel = context.createPosterPreviewViewModel(attachmentData);

    final previewWidth = width - 32;

    return LiveDataBuilder<CreatePosterPreviewState>(
        liveData: viewModel.previewData,
        builder: (_, state) {
          return GestureDetector(
              onTap: () => onClick(context, state),
              child: ClipRRect(
                  borderRadius: BorderRadius.circular(borderRadius),
                  child: AspectRatio(
                      aspectRatio: 1,
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 200),
                        width: double.infinity,
                        color: Colors.grey.shade100,
                        child: Stack(
                          children: [
                            Positioned.fill(
                              child: AnimatedSwitcher(
                                duration: const Duration(milliseconds: 200),
                                child: state is CreatePosterPreviewSuccess
                                    ? ImageFiltered(
                                        key: const ValueKey('blurred-image'),
                                        imageFilter: ImageFilter.blur(
                                            sigmaX: 10,
                                            sigmaY: 10,
                                            tileMode: TileMode.mirror),
                                        child: Transform.scale(
                                          scale: 2,
                                          child: CachedNetworkImage(
                                            imageUrl: state
                                                .previewData.imageUrls.first,
                                            fit: BoxFit.cover,
                                            cacheManager:
                                                AppCacheManager.instance,
                                          ),
                                        ),
                                      )
                                    : const SizedBox(key: ValueKey('loading')),
                              ),
                            ),
                            Positioned.fill(
                                top: 8,
                                bottom: 52,
                                child: AnimatedSwitcher(
                                    duration: const Duration(milliseconds: 200),
                                    child: state is CreatePosterPreviewSuccess
                                        ? Opacity(
                                            opacity: state.previewData
                                                    .disableCtaMessage.isEmpty
                                                ? 1
                                                : 0.9,
                                            child: CategoryPreview(
                                                key: const ValueKey(
                                                    'category_preview'),
                                                width: previewWidth,
                                                images: state
                                                    .previewData.imageUrls))
                                        : state is CreatePosterPreviewLoading
                                            ? const SizedBox(
                                                key: ValueKey('loading'),
                                                width: 24,
                                                height: 24,
                                              )
                                            : Padding(
                                                key: const ValueKey('error'),
                                                padding:
                                                    const EdgeInsets.all(8.0),
                                                child: Column(
                                                    mainAxisSize:
                                                        MainAxisSize.min,
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .center,
                                                    children: [
                                                      const Icon(
                                                          Icons.error_outline,
                                                          color: Colors.red),
                                                      const SizedBox(height: 8),
                                                      Text(
                                                          (state as CreatePosterPreviewError)
                                                              .localizedMessage,
                                                          textAlign:
                                                              TextAlign.center,
                                                          style:
                                                              const TextStyle(
                                                            fontSize: 12,
                                                          )),
                                                    ]),
                                              ))),
                            if (state is CreatePosterPreviewSuccess &&
                                state.previewData.circle != null &&
                                _shouldShowCircleImage(state))
                              Positioned(
                                  top: 0,
                                  left: 0,
                                  child: Container(
                                      padding: const EdgeInsets.all(8),
                                      child: CircleDisplayPicture
                                          .fromCircleIdentity(
                                              state.previewData.circle!,
                                              size: 24))),
                            Positioned(
                              left: 0,
                              right: 0,
                              bottom: 0,
                              child: AnimatedContainer(
                                  duration: const Duration(milliseconds: 200),
                                  width: double.infinity,
                                  color: state is CreatePosterPreviewSuccess &&
                                          state.previewData.disableCtaMessage
                                              .isEmpty
                                      ? Colors.black.withOpacity(0.5)
                                      : Colors.grey.withOpacity(0.5),
                                  child: state is CreatePosterPreviewSuccess
                                      ? TextButton.icon(
                                          onPressed: () {
                                            onClick(context, state);
                                          },
                                          onLongPress: () {
                                            onLongPress(context, state);
                                          },
                                          style: TextButton.styleFrom(
                                              foregroundColor: state.previewData
                                                      .disableCtaMessage.isEmpty
                                                  ? Colors.white
                                                  : Colors.white
                                                      .withOpacity(0.5),
                                              textStyle: const TextStyle(
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.w600,
                                                  height: 1.1,
                                                  overflow:
                                                      TextOverflow.ellipsis),
                                              tapTargetSize: MaterialTapTargetSize
                                                  .shrinkWrap,
                                              minimumSize:
                                                  const Size.fromHeight(48)),
                                          icon: const Icon(PrajaIcons.posters, size: 20),
                                          label: Text(context.getPluralizedString(StringKey.posterTitle, 1), maxLines: 1))
                                      : const SizedBox(height: 48)),
                            ),
                          ],
                        ),
                      ))));
        });
  }
}

@injectable
class CreatePosterPreviewViewModel extends ViewModel {
  final PosterService _posterService;

  CreatePosterPreviewViewModel(this._posterService) : super();

  final MutableLiveData<CreatePosterPreviewState> _previewData =
      MutableLiveData(CreatePosterPreviewState.loading());
  LiveData<CreatePosterPreviewState> get previewData => _previewData;

  bool _isInitialized = false;
  Future<void> _init(CreatePosterAttachmentData attachmentData) async {
    if (_isInitialized) return;

    _isInitialized = true;
    try {
      // support for mock
      if (attachmentData.params['id'] == "mock") {
        await Future.delayed(const Duration(milliseconds: 500));
        _previewData.value = CreatePosterPreviewState.success(mockPreview);
        return;
      }

      _previewData.value = CreatePosterPreviewState.success(
          await _posterService.getPosterPreview(attachmentData));
    } catch (e) {
      _previewData.value =
          CreatePosterPreviewState.error(localisedErrorMessage(e));
    }
  }
}

extension CreatePosterPreviewViewModelX on BuildContext {
  CreatePosterPreviewViewModel createPosterPreviewViewModel(
      CreatePosterAttachmentData attachmentData) {
    return getViewModel<CreatePosterPreviewViewModel>(key: attachmentData.key)
      .._init(attachmentData);
  }
}

sealed class CreatePosterPreviewState {
  const CreatePosterPreviewState();

  factory CreatePosterPreviewState.loading() = CreatePosterPreviewLoading;
  factory CreatePosterPreviewState.error(String localizedMessage) =
      CreatePosterPreviewError;
  factory CreatePosterPreviewState.success(CreatePosterPreview previewData) =
      CreatePosterPreviewSuccess;
}

class CreatePosterPreviewLoading extends CreatePosterPreviewState {
  const CreatePosterPreviewLoading();
}

class CreatePosterPreviewError extends CreatePosterPreviewState {
  final String localizedMessage;
  const CreatePosterPreviewError(this.localizedMessage);
}

class CreatePosterPreviewSuccess extends CreatePosterPreviewState {
  final CreatePosterPreview previewData;
  const CreatePosterPreviewSuccess(this.previewData);

  @override
  String toString() => '_Success{previewData: $previewData}';
}

final CreatePosterPreview mockPreview = CreatePosterPreview.fromJson({
  "title": "బాలల దినోత్సవ శుభాకాంక్షలు",
  "params": {"id": "446"},
  "image_urls": [
    "https://cdn.thecircleapp.in/production/admin-media/33/47896625ff82e87181c8e8b9fbceec0c.jpg",
    "https://cdn.thecircleapp.in/production/admin-media/33/3958802c011ad42fa093b66165c59e23.jpg",
    "https://cdn.thecircleapp.in/production/admin-media/33/8ea87a2bf88bec267ebde420fad8fa22.jpg"
  ],
  "total_creatives_count": 4,
  "analytics_params": {
    "event_name": "బాలల దినోత్సవ శుభాకాంక్షలు",
    "event_id": 446
  },
  "disable_cta_message":
      "ప్రతిపక్ష పార్టీ నాయకులకు ఈ పోస్టర్లు అందుబాటులో లేవు",
  "circle": {
    "id": 31403,
    "name": "వైఎస్ఆర్‌సీపీ",
    "name_en": "YSRCP",
    "level": "political_party",
    "hashid": "jVSQZ0",
    "circle_type": "interest",
    "members_count": 221607,
    "photo": {
      "id": 174529,
      "url":
          "https://g-cdn.thecircleapp.in/fit-in/1024x1024/filters:quality(80)/production/photos/41/b0149ff55f4649c16b4b33f7893055dd.png",
      "width": 328,
      "height": 328,
      "dominant_dark_color": "#000000",
      "user_id": 41,
      "active": true,
      "explicit": false,
      "created_at": "2022-02-03T18:22:11.000+05:30",
      "updated_at": "2022-02-03T18:22:24.000+05:30",
      "base64": null,
      "path": null,
      "service": "aws",
      "aspect_ratio": 1.0,
      "bg_color": "#000000",
      "data": null
    },
  }
});
