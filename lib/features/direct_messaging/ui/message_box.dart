import 'dart:io';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:image_picker/image_picker.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/features/direct_messaging/models/attachment.dart';
import 'package:praja/features/direct_messaging/service/messaging_config.dart';
import 'package:praja/features/direct_messaging/ui/draft_attachments_ui.dart';
import 'package:praja/features/media_picker/media_picker.dart';
import 'package:praja/presentation/praja_icons.dart';
import 'package:praja/utils/image_utils.dart';
import 'package:praja/utils/utils.dart';
import 'package:praja/utils/video_utils.dart';

typedef OnAttachmentAdditionCallback = void Function(
    {required AttachmentType type, required int count});

typedef OnAttachmentRemovedCallback = void Function(
    {required AttachmentType type});

/// Message editing box at the bottom of a chat screen
class MessageBox extends StatelessWidget {
  final MessageBoxController controller;
  final VoidCallback? onSubmit;
  final OnAttachmentAdditionCallback? onAttachmentAdditionCallback;
  final OnAttachmentRemovedCallback? onAttachmentRemovedCallback;
  final bool enabled;

  final bool autoFocus;
  final FocusNode? focusNode;
  final Widget? trailing;

  const MessageBox({
    super.key,
    required this.controller,
    this.trailing,
    this.enabled = true,
    this.focusNode,
    this.autoFocus = false,
    this.onSubmit,
    this.onAttachmentAdditionCallback,
    this.onAttachmentRemovedCallback,
  });

  void _onAttachTap(BuildContext context) async {
    controller.onAttachTapped();
    final result = await pickMedia(context: context);
    if (result == null) {
      controller.onAttachCancelled();
      return;
    }

    if (result.type == MediaPickerType.video) {
      await _onVideoPickerTap(result.files.first);
    } else {
      await _onImagePickerTap(result.files);
    }
  }

  Future<void> _onImagePickerTap(List<XFile> imageList) async {
    if (imageList.isEmpty) {
      // did not pick any image
      return;
    }
    await controller.onImagesPicked(imageList);
    onAttachmentAdditionCallback?.call(
        type: AttachmentType.image,
        count: controller.draftState.value.attachments.length);
  }

  Future<void> _onVideoPickerTap(XFile videoFile) async {
    await controller.onSendVideo(videoFile.path);
    onAttachmentAdditionCallback?.call(type: AttachmentType.video, count: 1);
  }

  static const _animDuration = Duration(milliseconds: 200);

  @override
  Widget build(BuildContext context) {
    return LiveDataBuilder(
        liveData: controller.draftState,
        builder: (_, state) {
          return Padding(
              padding: const EdgeInsets.all(8),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AnimatedSize(
                        duration: _animDuration,
                        child: DraftAttachmentsUI(
                            attachments: state.attachments,
                            onAttachmentRemove: (attachment) {
                              onAttachmentRemovedCallback?.call(
                                  type: attachment.type);
                              controller.onAttachmentRemove(attachment);
                            })),
                    Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.end,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(
                            child: Container(
                                decoration: BoxDecoration(
                                  color: state.allowTextEdit
                                      ? const Color(0xFFF3F3F3)
                                      : Colors.grey.shade200,
                                  borderRadius: BorderRadius.circular(24),
                                ),
                                padding: const EdgeInsets.only(left: 16),
                                child: Row(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Expanded(
                                          child: state.allowTextEdit
                                              ? TextField(
                                                  focusNode: focusNode,
                                                  minLines: 1,
                                                  maxLines: 4,
                                                  keyboardType:
                                                      TextInputType.multiline,
                                                  textInputAction:
                                                      TextInputAction.newline,
                                                  textCapitalization:
                                                      TextCapitalization
                                                          .sentences,
                                                  autofocus: autoFocus,
                                                  controller: controller
                                                      .messageController,
                                                  onSubmitted: (_) {
                                                    if (!state.canSend ||
                                                        !enabled) {
                                                      return;
                                                    }
                                                    onSubmit?.call();
                                                  },
                                                  decoration: InputDecoration(
                                                    hintText:
                                                        'మెసేజ్ టైప్ చేయండి…',
                                                    hintStyle: TextStyle(
                                                        color: Colors
                                                            .grey.shade700,
                                                        fontSize: 14),
                                                    border: InputBorder.none,
                                                  ),
                                                )
                                              : Text(
                                                  controller
                                                      .messageController.text,
                                                  maxLines: 4,
                                                  overflow:
                                                      TextOverflow.ellipsis)),

                                      // using Material > Inkwell because buttons are adding extra margins
                                      AnimatedSize(
                                          duration: _animDuration,
                                          alignment: Alignment.bottomRight,
                                          child: SizedBox(
                                              width: !state.allowTextEdit
                                                  ? null
                                                  : 0,
                                              child: Material(
                                                  color: Colors.grey.shade200,
                                                  shape: const CircleBorder(),
                                                  clipBehavior: Clip.antiAlias,
                                                  child: InkWell(
                                                      onTap: () {
                                                        controller
                                                            .onTextClearClicked();
                                                      },
                                                      child: Padding(
                                                          padding:
                                                              const EdgeInsets
                                                                  .all(12),
                                                          child: Icon(
                                                              Icons.cancel,
                                                              size: 24,
                                                              color: Colors.grey
                                                                  .shade600)))))),
                                      AnimatedSize(
                                          duration: _animDuration,
                                          alignment: Alignment.bottomRight,
                                          child: SizedBox(
                                              width: enabled &&
                                                      state.canAttachMedia()
                                                  ? null
                                                  : 0,
                                              child: Material(
                                                  color:
                                                      const Color(0xFFF3F3F3),
                                                  shape: const CircleBorder(),
                                                  clipBehavior: Clip.antiAlias,
                                                  child: InkWell(
                                                      onTap: () {
                                                        if (!enabled) return;
                                                        _onAttachTap(context);
                                                      },
                                                      child: Padding(
                                                          padding:
                                                              const EdgeInsets
                                                                  .all(12),
                                                          child: Transform.rotate(
                                                              angle: pi / 4,
                                                              child: Icon(
                                                                  Icons
                                                                      .attach_file,
                                                                  size: 24,
                                                                  color: Colors
                                                                      .grey
                                                                      .shade600))))))),
                                      AnimatedSize(
                                          duration: _animDuration,
                                          alignment: Alignment.bottomRight,
                                          child: SizedBox(
                                              width:
                                                  state.isAttaching ? null : 0,
                                              child: const Material(
                                                  color: Color(0xFFF3F3F3),
                                                  shape: CircleBorder(),
                                                  clipBehavior: Clip.antiAlias,
                                                  child: Padding(
                                                      padding:
                                                          EdgeInsets.all(12),
                                                      child: SizedBox(
                                                          width: 24,
                                                          height: 24,
                                                          child:
                                                              CircularProgressIndicator(
                                                                  strokeWidth:
                                                                      2.0)))))),
                                      AnimatedSize(
                                          duration: _animDuration,
                                          alignment: Alignment.bottomRight,
                                          child: SizedBox(
                                              width:
                                                  trailing != null ? null : 0,
                                              child: Material(
                                                  color:
                                                      const Color(0xFFF3F3F3),
                                                  shape: const CircleBorder(),
                                                  clipBehavior: Clip.antiAlias,
                                                  child: trailing))),
                                    ]))),
                        if (state.canSend && enabled) const SizedBox(width: 8),

                        // using Material > Inkwell because buttons are adding extra margins
                        AnimatedOpacity(
                            duration: _animDuration,
                            curve: Curves.easeInOut,
                            opacity: state.canSend && enabled ? 1 : 0,
                            child: AnimatedContainer(
                                width: state.canSend && enabled ? 48 : 0,
                                height: state.canSend && enabled ? 48 : 0,
                                curve: Curves.easeInOut,
                                duration: _animDuration,
                                alignment: Alignment.centerRight,
                                child: AnimatedScale(
                                    duration: _animDuration,
                                    scale: state.canSend && enabled ? 1 : 0,
                                    child: Material(
                                        color: Theme.of(context).primaryColor,
                                        shape: const CircleBorder(),
                                        clipBehavior: Clip.antiAlias,
                                        child: !enabled
                                            ? const Padding(
                                                padding: EdgeInsets.all(10),
                                                child: SizedBox(
                                                    width: 28,
                                                    height: 28,
                                                    child:
                                                        CircularProgressIndicator(
                                                      strokeWidth: 2,
                                                      valueColor:
                                                          AlwaysStoppedAnimation<
                                                                  Color>(
                                                              Colors.grey),
                                                    )),
                                              )
                                            : InkWell(
                                                onTap: () {
                                                  if (!state.canSend ||
                                                      !enabled) return;
                                                  onSubmit?.call();
                                                },
                                                child: const Padding(
                                                    padding: EdgeInsets.all(10),
                                                    child: Icon(PrajaIcons.send,
                                                        size: 28,
                                                        color: Color(
                                                            0xFFF3F3F3)))))))),
                      ],
                    )
                  ]));
        });
  }
}

class MessageBoxController {
  final MutableLiveData<DraftState> _draftState =
      MutableLiveData<DraftState>(DraftState());

  LiveData<DraftState> get draftState => _draftState;

  String get text => messageController.text;

  List<Attachment> get attachments => _draftState.value.attachments;

  final TextEditingController messageController;

  MessageBoxController() : messageController = TextEditingController() {
    messageController.addListener(_onTextChange);
  }

  void _onTextChange() {
    final text = messageController.text;
    bool hasText = text.trim().isNotEmpty;
    if (draftState.value.hasText != hasText) {
      _draftState.value = _draftState.value.copyWith(hasText: hasText);
    }

    if (text.length > 10000) {
      messageController.text = text.substring(0, 10000);
    }
  }

  void modify(
      {String? text,
      List<Attachment>? attachments,
      bool? allowTextEdit,
      bool? allowAttachmentAddition}) {
    _draftState.value = _draftState.value.copyWith(
        hasText: text != null && text.isNotEmpty,
        attachments: attachments,
        allowTextEdit: allowTextEdit,
        allowAttachmentAddition: allowAttachmentAddition);
    if (text != null) {
      messageController.text = text;
    }
  }

  void onAttachTapped() {
    _draftState.value = _draftState.value.copyWith(isAttaching: true);
  }

  void onAttachCancelled() {
    _draftState.value = _draftState.value.copyWith(isAttaching: false);
  }

  void clear() {
    _draftState.value = _draftState.value
        .copyWith(hasText: false, attachments: [], isAttaching: false);
    messageController.clear();
  }

  void onAttachmentRemove(Attachment attachment) {
    final attachments = draftState.value.attachments;
    final newAttachments = attachments.where((a) => a != attachment).toList();
    _draftState.value = _draftState.value.copyWith(attachments: newAttachments);
  }

  void onTextClearClicked() {
    _draftState.value =
        _draftState.value.copyWith(hasText: false, allowTextEdit: true);
    messageController.clear();
  }

  Future<void> onImagesPicked(List<XFile> images) async {
    // mark as sending message while we are processing the images
    _draftState.value = _draftState.value.copyWith(isAttaching: true);

    final maxImageAttachmentsCount =
        GetIt.I.get<MessagingConfig>().maxImageAttachmentsCount;

    final List<Attachment> nonMediaAttachments =
        _draftState.value.attachments.where((a) {
      return !a.isImage && !a.isVideo;
    }).toList();

    List<Attachment> imageAttachments =
        _draftState.value.attachments.where((a) {
      return a.isImage;
    }).toList();

    Set<String> existingImagePaths = imageAttachments.map((a) {
      return (a.data as AssetImageAttachmentData).path;
    }).toSet();

    bool informUserAboutLimit = false;
    for (final image in images) {
      if (existingImagePaths.contains(image.path)) {
        continue;
      }

      if (imageAttachments.length >= maxImageAttachmentsCount) {
        informUserAboutLimit = true;
        break;
      }

      final size = await ImageUtils.getImageSize(File(image.path));
      final attachment = Attachment.assetImage(
        path: image.path,
        width: size.width.toInt(),
        height: size.height.toInt(),
      );
      imageAttachments.add(attachment);
    }

    _draftState.value = _draftState.value.copyWith(
        isAttaching: false,
        attachments: [...nonMediaAttachments, ...imageAttachments]);

    if (informUserAboutLimit) {
      Utils.showToast(
          "$maxImageAttachmentsCount కన్నా ఎక్కువ ఫోటోలు ఎంచుకోలేరు");
    }
  }

  Future<void> onSendVideo(String videoPath) async {
    // mark as sending message while we process the video
    _draftState.value = _draftState.value.copyWith(isAttaching: true);

    final List<Attachment> nonMediaAttachments =
        _draftState.value.attachments.where((a) {
      return !a.isImage && !a.isVideo;
    }).toList();

    final durationLimitInSecs = await Utils.getVideoUploadDurationLimitInSecs();
    final duration = await VideoUtils.getDurationAtPath(videoPath);
    if (duration.inSeconds > durationLimitInSecs) {
      final message = await Utils.getVideoLimitErrorMessage();
      Utils.showToast(message);
      return;
    }
    final thumbnailFile = await VideoUtils.getThumbnail(videoPath);
    Size size = Size.zero;
    if (thumbnailFile != null) {
      size = await ImageUtils.getImageSize(thumbnailFile);
    }
    final attachment = Attachment.assetVideo(
        path: videoPath,
        durationInSecs: duration.inSeconds,
        width: size.width.toInt(),
        height: size.height.toInt(),
        thumbnailPath: thumbnailFile?.path);
    _draftState.value = _draftState.value.copyWith(
        isAttaching: false, attachments: [...nonMediaAttachments, attachment]);
  }
}

class DraftState {
  final bool hasText;
  final List<Attachment> attachments;
  final bool isAttaching;
  final bool allowTextEdit;
  final bool allowAttachmentAddition;

  DraftState({
    this.hasText = false,
    this.attachments = const [],
    this.isAttaching = false,
    this.allowTextEdit = true,
    this.allowAttachmentAddition = true,
  });

  bool get canSend =>
      hasText || attachments.where((e) => e.isStandaloneMessage).isNotEmpty;

  DraftState copyWith({
    bool? hasText,
    List<Attachment>? attachments,
    bool? isAttaching,
    bool? allowTextEdit,
    bool? allowAttachmentAddition,
  }) {
    return DraftState(
      hasText: hasText ?? this.hasText,
      attachments: attachments ?? this.attachments,
      isAttaching: isAttaching ?? this.isAttaching,
      allowTextEdit: allowTextEdit ?? this.allowTextEdit,
      allowAttachmentAddition:
          allowAttachmentAddition ?? this.allowAttachmentAddition,
    );
  }

  bool canAttachMedia() {
    final hasPostAttachment = attachments.where((a) => a.isPost).isNotEmpty;

    return allowAttachmentAddition && !hasPostAttachment && !isAttaching;
  }

  bool canAttachVideo() {
    final hasPostAttachment = attachments.where((a) => a.isPost).isNotEmpty;
    final hasVideoAttachment = attachments.where((a) => a.isVideo).isNotEmpty;
    final imageAttachments =
        attachments.where((a) => a.isImage && !a.isPost).toList();

    return allowAttachmentAddition &&
        !hasPostAttachment &&
        !hasVideoAttachment &&
        imageAttachments.isEmpty;
  }

  bool canAttachImage() {
    final hasPostAttachment = attachments.where((a) => a.isPost).isNotEmpty;
    final hasVideoAttachment = attachments.where((a) => a.isVideo).isNotEmpty;
    final imageAttachments =
        attachments.where((a) => a.isImage && !a.isPost).toList();

    return allowAttachmentAddition &&
        !hasPostAttachment &&
        !hasVideoAttachment &&
        imageAttachments.length <
            GetIt.I.get<MessagingConfig>().maxImageAttachmentsCount;
  }
}
