import 'package:flutter/material.dart';
import 'package:praja/features/app_version_update/app_version_updater.dart';
import 'package:praja/features/direct_messaging/ui/chat_ui_constants.dart';

class UnknownAttachmentUI extends StatelessWidget {
  final double borderRadius;
  final bool elevated;

  const UnknownAttachmentUI({
    super.key,
    this.borderRadius = ChatUiConstants.bubbleBorderRadius,
    this.elevated = false,
  });

  void _onTap(BuildContext context) async {
    await AppUpdatePlatform().openFullScreenUpdateUI(context);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        _onTap(context);
      },
      child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(borderRadius),
            boxShadow: elevated
                ? [
                    BoxShadow(
                      color: Colors.grey.shade300,
                      blurRadius: 0.5,
                      offset: const Offset(0, 1),
                    ),
                  ]
                : null,
          ),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(12),
                child: Row(children: [
                  Icon(Icons.attach_email_outlined,
                      color: Colors.grey.shade900),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'ఈ మెసేజ్ చూడటానికి మీ ప్రజా యాప్ ని అప్డేట్ చేసుకోగలరు',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade800,
                      ),
                    ),
                  ),
                ]),
              ),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 12),
                child: Divider(height: 1),
              ),
              SizedBox(
                  height: 40,
                  child: TextButton.icon(
                      onPressed: () {
                        _onTap(context);
                      },
                      icon: const Icon(Icons.cloud_download_outlined, size: 20),
                      label: const Text("అప్డేట్ యాప్")))
            ],
          )),
    );
  }
}
