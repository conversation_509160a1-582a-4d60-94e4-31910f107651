import 'package:flutter/material.dart';
import 'package:praja/features/intl/intl.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/models/circle_identity.dart';
import 'package:praja/presentation/circle_display_picture.dart';
import 'package:shimmer/shimmer.dart';

class ChannelPreviewUI extends StatelessWidget {
  final CircleIdentity circleIdentity;
  final bool showCTA;
  final VoidCallback? onTap;

  const ChannelPreviewUI({
    super.key,
    required this.circleIdentity,
    this.showCTA = true,
    this.onTap,
  });

  Widget _cta(BuildContext context) {
    return SizedBox(
        height: 40,
        child: TextButton.icon(
          label: Text(context.getString(StringKey.channelLabel)),
          icon: const Padding(
            padding: EdgeInsets.only(bottom: 2),
            child: Icon(Icons.campaign_outlined, size: 22),
          ),
          onPressed: onTap,
        ));
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        constraints: const BoxConstraints(maxWidth: 280),
        padding: const EdgeInsets.only(top: 12, left: 12, right: 12),
        child: Column(mainAxisSize: MainAxisSize.min, children: [
          Row(
            children: [
              CircleDisplayPicture.fromCircleIdentity(
                circleIdentity,
                size: 58,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      circleIdentity.name,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      // ignore: avoid_hardcoded_strings_in_ui
                      "${(circleIdentity.membersCount).toDisplayFormat(usedAsPrefix: true)} ${context.getString(StringKey.circleMembersLabel)}",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.normal,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          if (showCTA) const Divider(height: 1),
          if (showCTA) _cta(context),
        ]),
      ),
    );
  }

  static Widget shimmer({showCTA = true}) {
    return Container(
      padding: const EdgeInsets.all(12),
      child: Column(children: [
        Row(
          children: [
            CircleDisplayPicture.shimmer(
              size: 56,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _shimmerText(
                    width: 200,
                    height: 24,
                  ),
                  const SizedBox(height: 4),
                  _shimmerText(
                    width: 100,
                    height: 12,
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        if (showCTA) const Divider(height: 1),
        if (showCTA)
          Container(
              width: double.infinity, height: 48, color: Colors.grey.shade300)
      ]),
    );
  }

  static Widget _shimmerText({required double width, required double height}) {
    return Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: SizedBox(
          width: width,
          height: height,
        ));
  }
}
