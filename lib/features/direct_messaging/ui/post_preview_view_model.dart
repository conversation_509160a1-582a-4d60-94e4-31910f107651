import 'package:flutter/widgets.dart';
import 'package:injectable/injectable.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/errors/error_delegate.dart';
import 'package:praja/exceptions/api_exception.dart';
import 'package:praja/models/post_preview.dart';
import 'package:praja/services/post/post_service.dart';
import 'package:praja/utils/logger.dart';

@injectable
class PostPreviewViewModel extends ViewModel {
  final PostService service;

  PostPreviewViewModel(this.service);

  final MutableLiveData<PostPreviewState> _state =
      MutableLiveData(PostPreviewState.loading);
  LiveData<PostPreviewState> get state => _state;

  bool _isInitialized = false;

  Future<void> _init(int postId) async {
    if (_isInitialized) return;

    _isInitialized = true;
    try {
      final postPreview = await service.getPostPreview(postId);
      _state.value = PostPreviewState.loaded(postPreview);
    } catch (e, stackTrace) {
      printDebug(
          'Failure while fetching post preview for $postId in DM attachment',
          error: e,
          level: LogLevel.error,
          stackTrace: stackTrace);
      _state.value = PostPreviewState.failed(e);
    }
  }
}

extension PostPreviewViewModelX on BuildContext {
  PostPreviewViewModel postPreviewViewModel(int postId) =>
      getViewModel<PostPreviewViewModel>(key: "$postId").._init(postId);
}

abstract class PostPreviewState {
  const PostPreviewState();

  static const loading = PostPreviewLoading();
  factory PostPreviewState.loaded(PostPreview postPreview) = PostPreviewLoaded;
  factory PostPreviewState.failed(Object error) = PostPreviewFailed;
}

class PostPreviewLoading extends PostPreviewState {
  const PostPreviewLoading();
}

class PostPreviewLoaded extends PostPreviewState {
  final PostPreview postPreview;

  const PostPreviewLoaded(this.postPreview);
}

class PostPreviewFailed extends PostPreviewState {
  final Object error;

  const PostPreviewFailed(this.error);

  String get message {
    final e = error;
    if (e is ApiException) {
      if (e.failureType == ApiFailureType.notFound) {
        return 'ఈ పోస్ట్ తొలగించబడినది'; // Post has been removed
      }
    }

    return localisedErrorMessage(error);
  }
}
