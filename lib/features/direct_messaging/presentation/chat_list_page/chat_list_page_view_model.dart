import 'dart:async';

import 'package:injectable/injectable.dart';
import 'package:jetpack/eventqueue.dart';
import 'package:jetpack/livedata.dart';
import 'package:jetpack/viewmodel.dart';
import 'package:praja/features/direct_messaging/models/ui_conversation_item.dart';
import 'package:praja/features/direct_messaging/service/messaging_service.dart';
import 'package:praja/utils/logger.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

@injectable
class ChatListViewModel extends ViewModel {
  MessagingService messageService;

  ChatListViewModel(this.messageService) : super() {
    _initialise();
  }

  final primaryConversationsItemScrollController = ItemScrollController();
  final primaryConversationsItemPositionsListener =
      ItemPositionsListener.create();

  final otherConversationsItemScrollController = ItemScrollController();
  final otherConversationsItemPositionsListener =
      ItemPositionsListener.create();
  final MutableEventQueue<MessagingEvent> _eventQueue = MutableEventQueue();

  final MutableLiveData<ConversationsState> _state =
      MutableLiveData<ConversationsState>(ConversationsState.initial());

  LiveData<ConversationsState> get state => _state;

  bool _isInitialised = false;
  StreamSubscription? _conversationViewSubscription;
  StreamSubscription? _otherConversationsSubscription;
  bool _isFetchingOlderPrimaryConversations = false;
  bool _isFetchingOlderOtherConversations = false;

  MutableEventQueue<MessagingEvent> get eventQueue => _eventQueue;

  void _onConversationScrollChanged(
    Iterable<ItemPosition> itemPositions,
    int currentLength,
    bool isPrimary,
  ) {
    int maxIndex = -1;
    for (var itemPosition in itemPositions) {
      final index = itemPosition.index;
      if (index > maxIndex) {
        maxIndex = index;
      }
    }
    if (currentLength - maxIndex < 10) {
      if (isPrimary) {
        _onPrimaryConversationsPageEndReached();
      } else {
        _onOtherConversationsPageEndReached();
      }
    }
    return;
  }

  _onPrimaryConversationsPageEndReached() async {
    final currentState = state.value;
    if (currentState.allPrimaryConversationsDownloaded ||
        _isFetchingOlderPrimaryConversations) return;
    UIConversationItem? lastConversation = currentState.conversations.isNotEmpty
        ? currentState.conversations.last
        : null;
    _isFetchingOlderPrimaryConversations = true;
    final lastPage = await messageService.downloadOlderConversations(
        before: DateTime.fromMillisecondsSinceEpoch(
            lastConversation?.conversation.lastMessageTime ??
                DateTime.now().millisecondsSinceEpoch),
        isPrimary: true);
    if (lastPage == true) {
      _state.value =
          state.value.copyWith(allPrimaryConversationsDownloaded: true);
    }
    _isFetchingOlderPrimaryConversations = false;
  }

  _onOtherConversationsPageEndReached() async {
    final currentState = state.value;
    if (currentState.allOtherConversationsDownloaded ||
        _isFetchingOlderOtherConversations) return;
    UIConversationItem? lastConversation =
        currentState.otherConversations.isNotEmpty
            ? currentState.otherConversations.last
            : null;
    _isFetchingOlderOtherConversations = true;
    final lastPage = await messageService.downloadOlderConversations(
        before: DateTime.fromMillisecondsSinceEpoch(
            lastConversation?.conversation.lastMessageTime ??
                DateTime.now().millisecondsSinceEpoch),
        isPrimary: false);
    if (lastPage == true) {
      _state.value =
          state.value.copyWith(allOtherConversationsDownloaded: true);
    }
    _isFetchingOlderOtherConversations = false;
  }

  void _onPrimaryConversationsScrollChanged() {
    final itemPositions =
        primaryConversationsItemPositionsListener.itemPositions.value;
    int currentLength = state.value.conversations.length;
    _onConversationScrollChanged(itemPositions, currentLength, true);
  }

  void _onOtherConversationsScrollChanged() {
    final itemPositions =
        otherConversationsItemPositionsListener.itemPositions.value;
    int currentLength = state.value.otherConversations.length;
    _onConversationScrollChanged(itemPositions, currentLength, false);
  }

  void onItemsReceived() async {
    _onPrimaryConversationsScrollChanged();
    _onOtherConversationsScrollChanged();
  }

  void refresh() {
    if (!_isInitialised) {
      return;
    }

    _conversationViewSubscription?.cancel();
    _otherConversationsSubscription?.cancel();
    _isInitialised = false;
    _initialise();
  }

  _initialise() async {
    if (_isInitialised) {
      return;
    }

    otherConversationsItemPositionsListener.itemPositions
        .addListener(_onOtherConversationsScrollChanged);
    primaryConversationsItemPositionsListener.itemPositions
        .addListener(_onPrimaryConversationsScrollChanged);

    _conversationViewSubscription = messageService
        .listenForConversationViews()
        .logNonFatalsIfAppError(
            "Error while listening to primary conversations in chat_list_page")
        .listen((items) {
      _state.value =
          _state.value.copyWith(conversations: items, isLoading: false);
    });

    _otherConversationsSubscription = messageService
        .listenForConversationViews(isPrimary: false)
        .logNonFatalsIfAppError(
            "Error while listening to secondary conversations in chat_list_page")
        .listen((items) {
      _state.value = _state.value.copyWith(otherConversations: items);
    });
    _isInitialised = true;
  }

  void onTabChanged(int index) {
    _state.value = _state.value.copyWith(selectedTabIndex: index);
  }

  searchClick() {
    _eventQueue.push(const NavigateToSearch());
  }

  Future<void> onUserReturnedFromConversations(
      UIConversationItem conversation) async {
    final updatedConversation =
        await messageService.getConversationById(conversation.conversation.id);
    if (updatedConversation != null) {
      final activeTab = updatedConversation.isPrimary ? 0 : 1;
      onTabChanged(activeTab);
    }
  }

  @override
  void onDispose() {
    _conversationViewSubscription?.cancel();
    _otherConversationsSubscription?.cancel();
    super.onDispose();
  }
}

class ConversationsState {
  final bool isLoading;
  final List<UIConversationItem> conversations;
  final List<UIConversationItem> otherConversations;
  final int selectedTabIndex;
  final bool allPrimaryConversationsDownloaded;
  final bool allOtherConversationsDownloaded;

  ConversationsState({
    required this.conversations,
    required this.otherConversations,
    this.selectedTabIndex = 0,
    this.isLoading = true,
    this.allPrimaryConversationsDownloaded = false,
    this.allOtherConversationsDownloaded = false,
  });

  factory ConversationsState.initial() {
    return ConversationsState(
      conversations: [],
      otherConversations: [],
    );
  }

  ConversationsState copyWith({
    bool? isLoading,
    List<UIConversationItem>? conversations,
    List<UIConversationItem>? otherConversations,
    int? selectedTabIndex,
    bool? allPrimaryConversationsDownloaded,
    bool? allOtherConversationsDownloaded,
  }) {
    return ConversationsState(
      isLoading: isLoading ?? this.isLoading,
      conversations: conversations ?? this.conversations,
      otherConversations: otherConversations ?? this.otherConversations,
      selectedTabIndex: selectedTabIndex ?? this.selectedTabIndex,
      allOtherConversationsDownloaded: allOtherConversationsDownloaded ??
          this.allOtherConversationsDownloaded,
      allPrimaryConversationsDownloaded: allPrimaryConversationsDownloaded ??
          this.allPrimaryConversationsDownloaded,
    );
  }

  int get otherUnreadCount =>
      otherConversations.where((e) => e.conversation.unreadCount > 0).length;

  int get primaryUnreadCount =>
      conversations.where((e) => e.conversation.unreadCount > 0).length;
}

abstract class MessagingEvent {
  const MessagingEvent();
}

class NavigateToSearch implements MessagingEvent {
  const NavigateToSearch();
}

class ToastEvent implements MessagingEvent {
  final String message;

  const ToastEvent(this.message);
}
