import 'dart:async';
import 'dart:convert';

import 'package:injectable/injectable.dart';
import 'package:praja/features/direct_messaging/database/entities/db_message.dart';
import 'package:praja/features/direct_messaging/database/messaging_store.dart';
import 'package:praja/features/direct_messaging/models/message_data.dart';
import 'package:praja/features/direct_messaging/socket/messaging_socket.dart';
import 'package:praja/features/direct_messaging/socket/messaging_socket_events.dart';
import 'package:praja/utils/logger.dart';

/// Listens to messages with status to be reported and reports them to the server
@injectable
class MessageStatusReporter {
  final MessagingStore store;
  final MessagingSocket socket;

  late String _loggedInUserId;

  StreamSubscription<List<DbMessage>>? _subscription;

  MessageStatusReporter(this.store, this.socket);

  void connect(String loggedInUserId) {
    _loggedInUserId = loggedInUserId;
    _subscription?.cancel();
    _subscription = store
        .getMessagesWithStatusToBeReported()
        .listen(_onMessagesWithStatusToBeReportedReceived);
  }

  void dispose() {
    _subscription?.cancel();
  }

  Future<void> _onMessagesWithStatusToBeReportedReceived(
      List<DbMessage> messages) async {
    if (messages.isEmpty) {
      return;
    }

    dispose();

    List<DbMessage> messagesToBeUpdatedInDb = [];
    for (final message in messages) {
      final messageData = MessageData.fromJson(
              json.decode(message.messageData), MessageType.normal)
          as NormalMessageData;
      if (message.status == MessageStatus.unread.value ||
          message.status == MessageStatus.delivered.value) {
        // message delivered
        dynamic data = await socket.emitWithAck(MessageDelivered(
          messageId: message.id,
          userId: _loggedInUserId,
          messageSenderId: messageData.senderId,
          deliveredAt: DateTime.fromMillisecondsSinceEpoch(message.receivedAt),
        ));
        printDebug('Message delivered ack received: $data');
        if (isSuccessAck(data)) {
          messagesToBeUpdatedInDb.add(message.copyWith(
              statusToBeReported: false,
              updatedAt: DateTime.now().millisecondsSinceEpoch));
        }
      } else if (message.status == MessageStatus.read.value) {
        // message read
        dynamic data = await socket.emitWithAck(MessageRead(
          messageId: message.id,
          userId: _loggedInUserId,
          messageSenderId: messageData.senderId,
          readAt: DateTime.fromMillisecondsSinceEpoch(message.updatedAt),
        ));
        printDebug('Message read ack received: $data');
        if (isSuccessAck(data)) {
          messagesToBeUpdatedInDb.add(message.copyWith(
              statusToBeReported: false,
              updatedAt: DateTime.now().millisecondsSinceEpoch));
        }
      } else if (message.status == MessageStatus.readHidden.value) {
        // message read hidden
        dynamic data = await socket.emitWithAck(MessageReadHidden(
          messageId: message.id,
          userId: _loggedInUserId,
          messageSenderId: messageData.senderId,
          readHiddenAt: DateTime.fromMillisecondsSinceEpoch(message.updatedAt),
        ));
        printDebug('Message read hidden ack received: $data');
        if (isSuccessAck(data)) {
          messagesToBeUpdatedInDb.add(message.copyWith(
              statusToBeReported: false,
              updatedAt: DateTime.now().millisecondsSinceEpoch));
        }
      }
    }
    await store.insertMessages(messagesToBeUpdatedInDb);

    connect(_loggedInUserId);
  }

  bool isSuccessAck(dynamic data) {
    return data is Map<String, dynamic> && data['success'] == true;
  }
}
