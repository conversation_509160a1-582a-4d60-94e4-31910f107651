import 'package:json_annotation/json_annotation.dart';
import 'package:praja/features/direct_messaging/models/message_data.dart';

part 'action_data.g.dart';

class ActionData extends MessageData {
  final ActionType type;
  ActionData(this.type);

  factory ActionData.fromJson(Map<String, dynamic> json) {
    final type = ActionType.fromString(json['type']);
    switch (type) {
      case ActionType.conversationCreated:
        return ConversationCreatedActionData.fromJson(json);
      default:
        return ActionData(type);
    }
  }

  @override
  Map<String, dynamic> toJson() {
    if (this is ConversationCreatedActionData) {
      return (this as ConversationCreatedActionData).toJson();
    } else {
      return {
        'type': type.value,
      };
    }
  }
}

enum ActionType {
  conversationCreated("CONVERSATION_CREATED"),
  userAdded("USER_ADDED"),
  userRemoved("USER_REMOVED"),
  userLeft("USER_LEFT"),
  conversationPropertyUpdated("CONVERSATION_PROPERTY_UPDATED"),
  unknown("UNKNOWN");

  final String value;
  const ActionType(this.value);

  static ActionType fromString(String value) {
    try {
      return ActionType.values.firstWhere((element) => element.value == value);
    } catch (_) {
      return ActionType.unknown;
    }
  }
}

@JsonSerializable()
class ConversationCreatedActionData extends ActionData {
  @JsonKey(name: 'performedBy')
  final String? performedBy;

  ConversationCreatedActionData({
    required this.performedBy,
  }) : super(ActionType.conversationCreated);

  factory ConversationCreatedActionData.fromJson(Map<String, dynamic> json) =>
      _$ConversationCreatedActionDataFromJson(json);

  Map<String, dynamic> toJson() => _$ConversationCreatedActionDataToJson(this);
}
