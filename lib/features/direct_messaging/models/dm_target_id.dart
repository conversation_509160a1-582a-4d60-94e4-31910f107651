import 'package:praja/features/direct_messaging/database/entities/db_conversation.dart';

sealed class DmTargetId {}

class UserDmTargetId extends DmTargetId {
  final int userId;
  UserDmTargetId({required this.userId});
}

class CircleDmTargetId extends DmTargetId {
  final int circleId;
  final ConversationType conversationType;
  CircleDmTargetId({required this.circleId, required this.conversationType});
}
