import 'dart:async';
import 'dart:io';

import 'package:injectable/injectable.dart';
import 'package:light_compressor/light_compressor.dart';
import 'package:praja/utils/logger.dart';
import 'package:praja/utils/utils.dart';
import 'package:praja/utils/video_utils.dart';
import 'package:path/path.dart' as p;

@lazySingleton
class VideoCompressor {
  final _lightCompressor = LightCompressor();
  bool isCompressionInProgress = false;

  Future<void> waitForCurrentCompressionToFinish(
      {Duration timeout = const Duration(minutes: 1)}) async {
    final startTime = DateTime.now();
    while (isCompressionInProgress) {
      if (DateTime.now().difference(startTime) > timeout) {
        throw TimeoutException(
          "Current Compression did not finish in $timeout",
        );
      }
      await Future.delayed(const Duration(milliseconds: 100));
    }
  }

  Future<VideoCompressionResult> compressVideo(String path) async {
    if (isCompressionInProgress) {
      _lightCompressor.cancelCompression();
    }

    isCompressionInProgress = true;
    final duration = await VideoUtils.getDuration(File(path));
    final bitrate = VideoUtils.getBitrateInMbps(
      await File(path).length(),
      duration,
    );
    final targetVideoQuality = await _getTargetVideoQuality(bitrate);
    try {
      if (targetVideoQuality == null) {
        return VideoCompressionResult(
          path: path,
          originalPath: path,
          duration: duration,
          bitrate: bitrate,
        );
      }
      final compressionResponse = await _lightCompressor.compressVideo(
        path: path,
        video: Video(
          videoName: "${p.basenameWithoutExtension(path)}-compressed",
        ),
        android: AndroidConfig(isSharedStorage: false),
        ios: IOSConfig(saveInGallery: false),
        videoQuality: targetVideoQuality,
        isMinBitrateCheckEnabled: false,
      );

      if (compressionResponse is OnSuccess) {
        final compressedVideoSize =
            await File(compressionResponse.destinationPath).length();
        printDebug("Video Size after compression: $compressedVideoSize");
        final compressedVideoBitrate = VideoUtils.getBitrateInMbps(
          compressedVideoSize,
          duration,
        );
        return VideoCompressionResult(
          path: compressionResponse.destinationPath,
          originalPath: path,
          duration: duration,
          bitrate: compressedVideoBitrate,
        );
        // return compressionResponse.destinationPath;
      } else if (compressionResponse is OnFailure) {
        throw VideoCompressionFailureException(compressionResponse.message);
      } else if (compressionResponse is OnCancelled) {
        throw VideoCompressionCancelledException();
      } else {
        throw VideoCompressionFailureException(
            "Unknown compression response: $compressionResponse");
      }
    } finally {
      isCompressionInProgress = false;
    }
  }

  /// Our maximum bitrate ladder is 3Mbps
  Future<double> getTargetBitrate() async {
    final targetBitrate = await Utils.getVideoTargetBitrate();
    return targetBitrate;
  }

  Future<double> getMinBitrate() async {
    final minBitrate = await Utils.getVideoMinBitrate();
    return minBitrate;
  }

  /// Selects the target video quality based on the current bitrate of the video
  ///
  /// Refer [Bitrate selection of light_compressor](https://github.com/AbedElazizShe/LightCompressor/blob/6d7ed6bab2644fa5acaf3567911ef89741935522/lightcompressor/src/main/java/com/abedelazizshe/lightcompressorlibrary/utils/CompressorUtils.kt#L178)
  Future<VideoQuality?> _getTargetVideoQuality(double currentBitrate) async {
    final double targetBitrate = await getTargetBitrate();
    final double minBitrate = await getMinBitrate();

    if (currentBitrate <= minBitrate) {
      return null;
    }

    if (0.6 * currentBitrate <= targetBitrate) {
      return VideoQuality.very_high;
    } else if (0.4 * currentBitrate <= targetBitrate) {
      return VideoQuality.high;
    } else if (0.3 * currentBitrate <= targetBitrate) {
      return VideoQuality.medium;
    } else if (0.2 * currentBitrate <= targetBitrate) {
      return VideoQuality.low;
    } else {
      return VideoQuality.very_low;
    }
  }

  Stream<double> get compressionProgress => _lightCompressor.onProgressUpdated;
}

class VideoCompressionFailureException implements Exception {
  final String message;
  VideoCompressionFailureException(this.message);

  @override
  String toString() {
    return "VideoCompressionException: $message";
  }
}

class VideoCompressionCancelledException implements Exception {
  @override
  String toString() {
    return "Video Compression Cancelled";
  }
}

class VideoCompressionResult {
  final String path;
  final String originalPath;
  final Duration duration;
  final double bitrate;
  bool get isCompressed => path != originalPath;

  VideoCompressionResult(
      {required this.originalPath,
      required this.path,
      required this.duration,
      required this.bitrate});

  @override
  String toString() {
    return "VideoCompressionResult(path: $path, originalPath: $originalPath, duration: $duration, bitrate: $bitrate)";
  }
}
