import 'package:flutter/material.dart';
import 'package:jetpack/jetpack.dart';
import 'package:lottie/lottie.dart';
import 'package:praja/common/deeplink_params.dart';
import 'package:praja/core/ui/page.dart';
import 'package:praja/core/ui/page_view_reporter.dart';
import 'package:praja/features/deeplinks/destination.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/features/lottie/lottie_custom_decoder.dart';
import 'package:praja/features/premium_experience/premium_utils.dart';
import 'package:praja/features/support_flow/models/support_sheet_option.dart';
import 'package:praja/features/support_flow/support_sheet_view_model.dart';
import 'package:praja/features/support_flow/widgets/support_sheet_option_ui.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:provider/provider.dart';

extension SupportSheetX on BuildContext {
  /// We will send this source to backend, to differentiate the logic
  /// So, Please add source carefully
  Future<void> showSupportSheet({
    required String source,

    /// Right now, this only triggers if the option is calling an API
    /// It won't trigger for deeplink contained options
    Function(SupportSheetOption?)? onOptionSelected,
  }) async {
    await showModalBottomSheet(
      context: this,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      enableDrag: false,
      builder: (context) {
        return ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(24),
              topRight: Radius.circular(24),
            ),
            child: _SupportSheetInner(
              source: source,
              onOptionSelected: onOptionSelected,
            ));
      },
    );
  }
}

class _SupportSheetInner extends BasePage {
  final String source;
  final Function(SupportSheetOption?)? onOptionSelected;

  const _SupportSheetInner({
    required this.source,
    this.onOptionSelected,
  });

  @override
  String get pageName => 'support_sheet';

  @override
  bool get pageLoadRequiredForTracking => true;

  Widget _getBody({
    required SupportSheetState state,
    required SupportSheetViewModel viewModel,
    required BuildContext context,
  }) {
    if (state is SupportSheetLoadingState) {
      return const Padding(
        padding: EdgeInsets.symmetric(vertical: 24.0),
        child: Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation(Colors.black),
          ),
        ),
      );
    } else if (state is SupportSheetSuccessState) {
      final pageParams = {
        'source': source,
        ...state.response.analyticsParams ?? {},
      };
      Provider.of<PageViewTracker>(context, listen: false)
          .onPageLoaded(pageParams);
      return SingleChildScrollView(
        child: Stack(
          children: [
            AnimatedSize(
              duration: const Duration(milliseconds: 200),
              curve: Curves.easeInOut,
              child: LiveDataBuilder(
                  liveData: viewModel.supportRequested,
                  builder: (context, supportRequested) {
                    return LiveDataBuilder(
                        liveData: viewModel.requestSuccessMessage,
                        builder: (context, message) {
                          if (supportRequested && message.isEmpty) {
                            return const Padding(
                              padding: EdgeInsets.symmetric(vertical: 24.0),
                              child: Center(
                                child: CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation(
                                    Colors.black,
                                  ),
                                ),
                              ),
                            );
                          }
                          if (message.isNotEmpty) {
                            return Container(
                              width: double.infinity,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 24,
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  const SizedBox(height: 70),
                                  SizedBox(
                                    height: 120,
                                    child: Lottie.asset(
                                      'assets/Lottie/customer_support.lottie',
                                      decoder: lottieCustomDecoder,
                                      fit: BoxFit.contain,
                                      width: double.infinity,
                                      repeat: true,
                                    ),
                                  ),
                                  const SizedBox(height: 24),
                                  Text(
                                    message,
                                    textAlign: TextAlign.center,
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.black,
                                    ),
                                  ),
                                  const SizedBox(height: 70),
                                ],
                              ),
                            );
                          }
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (state.response.title.isNotEmpty) ...[
                                Container(
                                  height:
                                      state.response.isPremiumUser ? 140 : 100,
                                  width: double.infinity,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    gradient: state.response.isPremiumUser
                                        ? PremiumUtils.premiumScreenBgGradient
                                        : const LinearGradient(
                                            colors: [
                                              Colors.white,
                                              Colors.white,
                                            ],
                                          ),
                                  ),
                                  child: Text(
                                    state.response.title,
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      fontSize: 20,
                                      color: state.response.isPremiumUser
                                          ? PremiumUtils.premiumHeaderTitleColor
                                          : Colors.black,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                              if (state.response.subTitle.isNotEmpty) ...[
                                Padding(
                                  padding: const EdgeInsets.all(16.0),
                                  child: Text(
                                    state.response.subTitle,
                                    textAlign: TextAlign.left,
                                    style: const TextStyle(
                                      fontSize: 14,
                                      color: Color(0xFF8F8F8F),
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 16),
                              ],
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 16.0),
                                child: LiveDataBuilder(
                                  liveData: viewModel.selectedOption,
                                  builder: (context, selectedOption) {
                                    return ListView.separated(
                                      key: ValueKey(selectedOption),
                                      shrinkWrap: true,
                                      physics:
                                          const NeverScrollableScrollPhysics(),
                                      itemCount: (selectedOption != null &&
                                              selectedOption
                                                  .subOptions.isNotEmpty)
                                          ? selectedOption.subOptions.length
                                          : state.response.options.length,
                                      itemBuilder: (context, index) {
                                        return SupportSheetOptionUI(
                                          premiumUser:
                                              state.response.isPremiumUser,
                                          option: (selectedOption != null &&
                                                  selectedOption
                                                      .subOptions.isNotEmpty)
                                              ? selectedOption.subOptions[index]
                                              : state.response.options[index],
                                          onTap: () {
                                            AppAnalytics.onSupportOptionClicked(
                                              params: (selectedOption != null &&
                                                      selectedOption.subOptions
                                                          .isNotEmpty)
                                                  ? selectedOption
                                                      .subOptions[index]
                                                      .analyticsParams
                                                  : state
                                                      .response
                                                      .options[index]
                                                      .analyticsParams,
                                              source: 'support_sheet',
                                            );
                                            viewModel.onOptionSelected(
                                              (selectedOption != null &&
                                                      selectedOption.subOptions
                                                          .isNotEmpty)
                                                  ? selectedOption
                                                      .subOptions[index]
                                                  : state
                                                      .response.options[index],
                                            );
                                          },
                                        );
                                      },
                                      separatorBuilder: (context, index) {
                                        return const Divider(
                                          color: Color(0xFFE0E0E0),
                                        );
                                      },
                                    );
                                  },
                                ),
                              ),
                              const SizedBox(height: 48),
                            ],
                          );
                        });
                  }),
            ),
            LiveDataBuilder(
                liveData: viewModel.selectedOption,
                builder: (context, option) {
                  return option != null && option.subOptions.isNotEmpty
                      ? Positioned(
                          top: 4,
                          left: 4,
                          child: IconButton(
                            icon: const Icon(
                              Icons.arrow_back_rounded,
                              color: Colors.black,
                              size: 24,
                            ),
                            onPressed: () {
                              viewModel.onBackClicked();
                            },
                          ),
                        )
                      : const SizedBox();
                }),
            LiveDataBuilder(
                liveData: viewModel.requestSuccessMessage,
                builder: (context, message) {
                  return message.isNotEmpty
                      ? Positioned(
                          top: 4,
                          right: 4,
                          child: IconButton(
                            icon: const Icon(
                              Icons.close,
                              color: Colors.black,
                            ),
                            onPressed: () {
                              viewModel.onBackClicked();
                              Navigator.of(context).pop();
                            },
                          ),
                        )
                      : const SizedBox();
                })
          ],
        ),
      );
    } else if (state is SupportSheetErrorState) {
      AppAnalytics.onSupportSheetFailed(
          source: 'support_sheet', errorMessage: state.message);
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const SizedBox(height: 24),
            Text(
              state.message,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 18,
                color: Colors.black,
              ),
            ),
            const SizedBox(height: 24),
            InkWell(
              onTap: () {
                viewModel.onRetryClicked();
              },
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.refresh,
                      color: Colors.black,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      context.getString(StringKey.retryLabel),
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        color: Colors.black,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 48),
          ],
        ),
      );
    } else {
      return const SizedBox();
    }
  }

  @override
  Widget buildContent(BuildContext context) {
    final viewModel = context.supportSheetViewModel(
        source: source, onOptionSelected: onOptionSelected);
    return PopScope(
      canPop: true,
      onPopInvoked: (bool isPop) {
        if (isPop) {
          viewModel.onBackClicked();
        }
      },
      child: Material(
        color: Colors.white,
        child: EventListener(
          eventQueue: viewModel.eventQueue,
          onEvent: (context, event) async {
            if (event is NavigateToDeeplinkEvent) {
              if (event.deeplink.isNotEmpty &&
                  event.identifier == 'cancel_subscription') {
                Navigator.of(context).pop();
              }
              DeeplinkDestination.fromRoute(event.deeplink)
                  ?.go(context, DeeplinkSource.internalDeeplink);
            }
          },
          child: LiveDataBuilder(
              liveData: viewModel.state,
              builder: (context, state) {
                return Wrap(
                  children: [
                    _getBody(
                      state: state,
                      viewModel: viewModel,
                      context: context,
                    ),
                  ],
                );
              }),
        ),
      ),
    );
  }
}
