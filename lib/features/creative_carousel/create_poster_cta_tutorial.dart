import 'package:injectable/injectable.dart';
import 'package:praja/services/prefs/hive_prefs.dart';
import 'package:praja/utils/logger.dart';

typedef OnChangedListener = void Function();

@lazySingleton
class CreatePosterCtaTutorial {
  final _prefs = HivePrefs.app();
  final Set<OnChangedListener> _listeners = {};

  int _nudgesShownInSession = 0;

  bool _initialized = false;
  init() async {
    if (_initialized) {
      return;
    }
    _update();
    _initialized = true;
  }

  bool get userLearntCreatePosterCta => _userLearntCreatePosterCta;

  bool _userLearntCreatePosterCta = false;

  Future<void> _update() async {
    final hasInteracted = await _hasInteractedWithCreatePosterCta();
    if (hasInteracted) {
      _userLearntCreatePosterCta = true;
      _notifyListeners();
      return;
    }

    if (_nudgesShownInSession >= 2) {
      _userLearntCreatePosterCta = true;
      _notifyListeners();
      return;
    }

    final numberOfNudgesShown = await _getNumberOfCreatePosterCtaNudgesShown();
    _userLearntCreatePosterCta = numberOfNudgesShown >= 5;
    _notifyListeners();
  }

  void addListener(OnChangedListener listener) {
    _listeners.add(listener);
  }

  void removeListener(OnChangedListener listener) {
    _listeners.remove(listener);
  }

  void _notifyListeners() {
    for (final listener in _listeners) {
      listener();
    }
  }

  Future<void> onInteractedWithCreatePosterCta() async {
    logDebug("onInteractedWithCreatePosterCta");
    await _prefs.putBool("interacted_with_create_poster_cta", true);
    await _update();
  }

  Future<void> onCreatePosterCtaNudgeShown() async {
    logDebug("onCreatePosterCtaNudgeShown");
    await _prefs.putInt("number_of_create_poster_cta_nudges_shown",
        (await _getNumberOfCreatePosterCtaNudgesShown()) + 1);
    _nudgesShownInSession++;
    await _update();
  }

  Future<bool> _hasInteractedWithCreatePosterCta() async {
    return await _prefs.getBool("interacted_with_create_poster_cta",
        defaultValue: false);
  }

  Future<int> _getNumberOfCreatePosterCtaNudgesShown() async {
    return await _prefs.getInt("number_of_create_poster_cta_nudges_shown",
        defaultValue: 0);
  }
}
