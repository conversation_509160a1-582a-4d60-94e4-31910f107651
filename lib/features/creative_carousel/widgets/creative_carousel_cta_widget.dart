import 'dart:ui';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:praja/features/creative_carousel/models/creative_carousel.dart';
import 'package:praja/services/app_cache_manager.dart';

class CreativeCarouselCTAWidget extends StatelessWidget {
  final CreativeCarousel creativeCarousel;
  final VoidCallback onPressed;
  final double width;
  final double height;

  const CreativeCarouselCTAWidget(
      {super.key,
      required this.width,
      required this.height,
      required this.creativeCarousel,
      required this.onPressed});

  @override
  Widget build(BuildContext context) {
    final firstPhoto = creativeCarousel.items.first.photo;
    return Container(
      width: width,
      height: height,
      clipBehavior: Clip.antiAlias,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.5),
            offset: const Offset(1, 1),
            blurRadius: 4,
          ),
        ],
      ),
      child: GestureDetector(
        onTap: onPressed,
        child: DefaultTextStyle.merge(
          style:
              const TextStyle(color: Colors.white, fontSize: 14, height: 1.4),
          child: Stack(
            children: [
              ImageFiltered(
                imageFilter: ImageFilter.blur(
                    sigmaX: 10, sigmaY: 10, tileMode: TileMode.mirror),
                child: CachedNetworkImage(
                  cacheManager: AppCacheManager.instance,
                  imageUrl: firstPhoto.placeholderUrl ?? firstPhoto.url,
                  width: width,
                  height: height,
                  fit: BoxFit.cover,
                ),
              ),
              Container(
                width: width,
                height: height,
                color: Colors.black.withOpacity(0.55),
              ),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Center(
                  child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(24.0),
                          child: Text(creativeCarousel.ctaDescription,
                              style: const TextStyle(height: 1.4),
                              textAlign: TextAlign.center),
                        ),
                        const SizedBox(height: 8),
                        TextButton.icon(
                            onPressed: onPressed,
                            style: TextButton.styleFrom(
                              foregroundColor: Colors.white,
                              backgroundColor: Colors.black.withOpacity(0.3),
                              textStyle: const TextStyle(
                                  fontSize: 15, fontWeight: FontWeight.w600),
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(24)),
                            ),
                            label: const Padding(
                              padding: EdgeInsets.only(bottom: 2.0),
                              child: Icon(Icons.chevron_right, size: 32),
                            ),
                            icon: Padding(
                              padding: const EdgeInsets.only(left: 12.0),
                              child: Text(creativeCarousel.ctaText),
                            )),
                      ]),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
