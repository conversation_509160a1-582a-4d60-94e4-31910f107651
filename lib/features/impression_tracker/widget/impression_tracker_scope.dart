import 'package:flutter/material.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/features/impression_tracker/view_model/impression_tracker.dart';
import 'package:provider/provider.dart';

class ImpressionTrackerScope extends StatefulWidget {
  final Widget child;

  const ImpressionTrackerScope({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  State<ImpressionTrackerScope> createState() => _ImpressionTrackerScopeState();
}

class _ImpressionTrackerScopeState extends State<ImpressionTrackerScope>
    with WidgetsBindingObserver {
  late ImpressionTracker _impressionTracker;
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _impressionTracker = context.getViewModel<ImpressionTracker>();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.inactive:
        break;
      case AppLifecycleState.paused:
        _impressionTracker.onInvisible();
        break;
      case AppLifecycleState.resumed:
        break;
      case AppLifecycleState.detached:
        break;
      case AppLifecycleState.hidden:
        break;
    }
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () {
        _impressionTracker.onInvisible();
        return Future.value(true);
      },
      child: Provider.value(value: _impressionTracker, child: widget.child),
    );
  }
}
