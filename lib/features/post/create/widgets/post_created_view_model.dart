import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/errors/error_delegate.dart';
import 'package:praja/features/user/models/app_user.dart';
import 'package:praja/models/post.dart';
import 'package:praja/services/post/post_service.dart';
import 'package:praja/services/user_store.dart';
import 'package:praja/utils/logger.dart';

@injectable
class PostCreatedViewModel extends ViewModel {
  PostService postService;
  late int postId;

  PostCreatedViewModel(this.postService);

  final MutableLiveData<PostCreatedState> _state =
      MutableLiveData(PostCreatedLoading());
  LiveData<PostCreatedState> get state => _state;

  bool _isInitialized = false;
  void _init(int postId) {
    if (_isInitialized) return;

    _isInitialized = true;
    this.postId = postId;
    _fetchPost();
  }

  void _fetchPost() async {
    try {
      final post = await postService.fetchByID(postId);
      _state.value = PostCreatedSuccess(post);
    } catch (e, st) {
      logNonFatalIfAppError(
          "Error while fetching post in PostCreatedViewModel", e,
          stackTrace: st);
      _state.value = PostCreatedError(localisedErrorMessage(e));
    }
  }
}

sealed class PostCreatedState {}

class PostCreatedLoading extends PostCreatedState {}

class PostCreatedSuccess extends PostCreatedState {
  final Post post;
  PostCreatedSuccess(this.post);
}

class PostCreatedError extends PostCreatedState {
  final String message;
  PostCreatedError(this.message);
}

extension CreatePostViewModelX on BuildContext {
  PostCreatedViewModel postCreatedViewModel(int postId) =>
      getViewModel<PostCreatedViewModel>(key: postId.toString()).._init(postId);
}
