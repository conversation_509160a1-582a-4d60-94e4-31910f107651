import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:praja/exceptions/api_exception.dart';
import 'package:praja/features/search/models/user_search_item.dart';
import 'package:praja/network/network_constants.dart';

@injectable
class UserSearchService {
  final Dio dio;

  UserSearchService({@Named(ror) required this.dio});

  Future<List<UserSearchItem>> search(String query) async {
    try {
      var response = await dio.get(
        '/users/search-v2',
        queryParameters: {
          'query': query,
          'count': '10',
        },
      );
      return (response.data as List)
          .map((e) => UserSearchItem.fromJson(e))
          .toList();
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }
}
