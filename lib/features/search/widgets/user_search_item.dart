import 'package:flutter/material.dart' hide Badge;
import 'package:praja/features/intl/intl.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/features/search/models/user_search_item.dart';
import 'package:praja/features/user/follow/widgets/user_follow_button.dart';
import 'package:praja/models/badge.dart';
import 'package:praja/models/circle.dart';
import 'package:praja/presentation/user_avatar.dart';
import 'package:praja/screens/users/user.dart';
import 'package:praja/utils/color_utils.dart';
import 'package:praja/utils/widgets/badge_strip_profile.dart';

class UserSearchItemWidget extends StatelessWidget {
  const UserSearchItemWidget({
    super.key,
    required this.source,
    required this.userSearchResult,
  });

  final String source;
  final UserSearchItem userSearchResult;

  void _navigateToUserProfile(BuildContext context, int userId) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (BuildContext context) => UserPage(
          id: userId,
          source: source,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        color: Colors.white,
        child: ListTile(
          contentPadding: const EdgeInsets.symmetric(horizontal: 16),
          dense: true,
          leading: UserAvatar(
            name: userSearchResult.name,
            color: HexColor.fromHex(userSearchResult.avatarColor),
            photo: userSearchResult.photo,
            badge: userSearchResult.badge,
          ),
          title: Text(
            userSearchResult.name.toString(),
            overflow: TextOverflow.ellipsis,
            softWrap: true,
            maxLines: 1,
            style: const TextStyle(fontSize: 15, fontWeight: FontWeight.bold),
          ),
          subtitle: Column(
            children: [
              if (userSearchResult.badge != null &&
                  userSearchResult.badge!.active &&
                  userSearchResult.badge!.badgeBanner != BadgeBanner.none)
                UserBadge(badge: userSearchResult.badge!),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                  Flexible(
                    child: UserFollowersCountWidget(
                      count: userSearchResult.followersCount,
                    ),
                  ),
                  if (userSearchResult.village != null)
                    Flexible(
                      child: UserVillage(
                        village: userSearchResult.village!,
                      ),
                    ),
                ],
              ),
            ],
          ),
          trailing: userSearchResult.loggedInUser == false
              ? UserFollowButton(
                  userId: userSearchResult.id,
                  isFollowing: userSearchResult.follows,
                  source: "search",
                )
              : const SizedBox(width: 0, height: 0),
          onTap: () => _navigateToUserProfile(context, userSearchResult.id),
        ));
  }
}

class UserVillage extends StatelessWidget {
  final Circle village;

  const UserVillage({super.key, required this.village});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        const SizedBox(width: 8),
        const Icon(
          Icons.location_on,
          size: 12,
          color: Colors.grey,
        ),
        const SizedBox(width: 2),
        Flexible(
          child: Text(
            village.name.toString(),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(fontSize: 11, color: Colors.grey.shade600),
          ),
        ),
      ],
    );
  }
}

class UserBadge extends StatelessWidget {
  final Badge badge;

  const UserBadge({super.key, required this.badge});

  @override
  Widget build(BuildContext context) {
    if (badge.badgeBanner == BadgeBanner.none) {
      return const SizedBox();
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(
          height: 3,
        ),
        Align(
          alignment: Alignment.centerLeft,
          child: Row(
            children: [
              const SizedBox(
                width: 10,
              ),
              BadgeStripProfile(
                badge,
                maxWidth: MediaQuery.of(context).size.width * 0.35,
                height: 14,
                fontSize: 8,
                badgeTextLetterSpacing: 0,
              ),
            ],
          ),
        ),
        const SizedBox(
          height: 4,
        )
      ],
    );
  }
}

class UserFollowersCountWidget extends StatelessWidget {
  final int count;

  const UserFollowersCountWidget({super.key, required this.count});

  @override
  Widget build(BuildContext context) {
    String countText = count.toDisplayFormat(usedAsPrefix: true);

    return Text(
      // ignore: avoid_hardcoded_strings_in_ui
      "$countText ${context.getPluralizedString(StringKey.userFollowersCountSuffixText, count)}",
      style: const TextStyle(fontSize: 11),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }
}
