import 'package:flutter/material.dart';
import 'package:praja/features/search/models/circle_search_item.dart';
import 'package:praja/features/search/models/user_search_item.dart';
import 'package:praja/features/search/widgets/search_circle_results.dart';
import 'package:praja/features/search/widgets/user_search_item.dart';

class SearchResultsWidget extends StatelessWidget {
  const SearchResultsWidget({
    super.key,
    required this.userSearchResults,
    required this.circleSearchResults,
  });

  final List<UserSearchItem> userSearchResults;
  final List<CircleSearchItem> circleSearchResults;

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: userSearchResults.length + 1,
      itemBuilder: (context, index) {
        if (index == 0) {
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: SearchCircleResultsWidget(
              source: 'search',
              circleSearchResults: circleSearchResults,
            ),
          );
        }
        return UserSearchItemWidget(
          source: 'search',
          userSearchResult: userSearchResults[index - 1],
        );
      },
    );
  }
}
