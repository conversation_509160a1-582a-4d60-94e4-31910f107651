// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'poster_preview_store.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class CreatePosterPreviewValueAdapter
    extends TypeAdapter<CreatePosterPreviewValue> {
  @override
  final int typeId = 5;

  @override
  CreatePosterPreviewValue read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CreatePosterPreviewValue(
      posterPreviewSerialized: fields[0] as String,
      updatedAt: fields[1] as int,
    );
  }

  @override
  void write(BinaryWriter writer, CreatePosterPreviewValue obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.posterPreviewSerialized)
      ..writeByte(1)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CreatePosterPreviewValueAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
