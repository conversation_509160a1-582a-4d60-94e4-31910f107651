import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';

@lazySingleton
class PosterCarouselConfig extends ChangeNotifier {
  final Set<String> _layoutFeedbackCompletedProtocols = {};

  // Layout Feedback Review Methods
  bool _isProtocolCompleted(String protocolId) {
    return _layoutFeedbackCompletedProtocols.contains(protocolId);
  }

  void onProtocolReviewCompleted(String protocolId) {
    _layoutFeedbackCompletedProtocols.add(protocolId);
    notifyListeners();
  }

  bool isAvailableForLayoutFeedback(String protocolId) {
    return !_isProtocolCompleted(protocolId);
  }

  void clearProtocolIdFromCompletedProtocols(String protocolId) {
    if (_isProtocolCompleted(protocolId)) {
      _layoutFeedbackCompletedProtocols.remove(protocolId);
      notifyListeners();
    }
  }
}
