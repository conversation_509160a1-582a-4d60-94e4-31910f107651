import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_vibrate/flutter_vibrate.dart';
import 'package:praja/utils/widgets/formatted_text_widget.dart';
import 'package:praja_posters/praja_posters.dart';

class LayoutFeedbackWidget extends StatelessWidget {
  final LayoutFeedbackRequest layoutFeedbackRequest;
  final void Function()? onAccepted;
  final void Function()? onRejected;

  const LayoutFeedbackWidget({
    super.key,
    required this.layoutFeedbackRequest,
    required this.onAccepted,
    required this.onRejected,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF292929) : const Color(0xFFEEEEEE),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          FormattedTextWidget(
            text: layoutFeedbackRequest.text,
            textAlign: TextAlign.center,
            textScaleFactor: 1,
            maxLines: 2,
            defaultTextStyle: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    onRejected?.call();
                  },
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 6),
                    backgroundColor: const Color(0xfff44336)
                        .withOpacity(isDarkMode ? 0.5 : 0.9),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  child: Text(
                    layoutFeedbackRequest.rejectText,
                    textScaler: const TextScaler.linear(1),
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    onAccepted?.call();
                  },
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 6),
                    backgroundColor: const Color(0xff4CAF50)
                        .withOpacity(isDarkMode ? 0.5 : 0.9),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  child: Text(
                    layoutFeedbackRequest.acceptText,
                    textScaler: const TextScaler.linear(1),
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}

class AnimatedLayoutFeedbackWidget extends StatefulWidget {
  final LayoutFeedbackRequest layoutFeedbackRequest;
  final void Function()? onAccepted;
  final void Function()? onRejected;

  const AnimatedLayoutFeedbackWidget({
    super.key,
    required this.layoutFeedbackRequest,
    required this.onAccepted,
    required this.onRejected,
  });

  @override
  State<AnimatedLayoutFeedbackWidget> createState() =>
      _AnimatedLayoutFeedbackWidgetState();
}

class _AnimatedLayoutFeedbackWidgetState
    extends State<AnimatedLayoutFeedbackWidget>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<double> _shakeAnimation;

  // I want to vibrate the phone only once per app session
  static bool _isVibrationCompleted = false;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    // Creates shake animation from negative to positive values
    // 0 -> -10 -> 10 -> -10 -> 10 -> 0
    _shakeAnimation = TweenSequence<double>([
      TweenSequenceItem(tween: Tween(begin: 0, end: -10), weight: 1),
      TweenSequenceItem(tween: Tween(begin: -10, end: 10), weight: 2),
      TweenSequenceItem(tween: Tween(begin: 10, end: -10), weight: 2),
      TweenSequenceItem(tween: Tween(begin: -10, end: 10), weight: 2),
      TweenSequenceItem(tween: Tween(begin: 10, end: 0), weight: 1),
    ]).animate(_controller);

    // addPostFrameCallback is used to ensure that the widget is rendered before the shake animation starts
    // That's why we can able to use async-await inside addPostFrameCallback
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (_isVibrationCompleted) return;
      await Future.delayed(const Duration(seconds: 1));
      if (!mounted) return;
      _controller.forward();
      _vibratePhone();
    });
  }

  Future<void> _vibratePhone() async {
    if (await Vibrate.canVibrate) {
      Vibrate.feedback(FeedbackType.warning);
    } else {
      // For devices that don't support vibration, we use HapticFeedback
      HapticFeedback.mediumImpact();
    }
    _isVibrationCompleted = true;
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _shakeAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(_shakeAnimation.value, 0),
          child: child,
        );
      },
      child: LayoutFeedbackWidget(
        layoutFeedbackRequest: widget.layoutFeedbackRequest,
        onAccepted: widget.onAccepted,
        onRejected: widget.onRejected,
      ),
    );
  }
}
