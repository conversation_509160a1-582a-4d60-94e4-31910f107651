import 'dart:io';

import 'package:clipboard/clipboard.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:image_picker/image_picker.dart';
import 'package:praja/features/whatsapp_share/whatsapp_share.dart';
import 'package:praja/utils/storage_permission.dart';
import 'package:praja/utils/utils.dart';
import 'package:share_plus/share_plus.dart';

import 'poster_layout_share_destination.dart';
import 'save_image_to_gallery.dart';

Future<void> sharePoster({
  required String path,
  required PosterLayoutShareDestination destination,
  required String shareText,
  required String source,
  required Map<String, dynamic> analyticsParams,
}) async {
  if (destination == PosterLayoutShareDestination.download) {
    final bool isPermissionGranted = await checkAndRequestStoragePermission(
        source: source, analyticsParameters: analyticsParams);
    if (isPermissionGranted) {
      final isImageSaved =
          await saveImageToGallery(creativeFile: File(path)); //file
      if (isImageSaved) {
        Utils.showToast('పోస్టర్ డౌన్లోడ్ చేయబడింది');
      } else {
        Utils.showToast('పోస్టర్ డౌన్లోడ్ ఫెయిల్ అయినది');
      }
    } else {
      Utils.showToast("స్టోరేజ్ పర్మిషన్ అనుమతించండి");
    }
  } else if (destination == PosterLayoutShareDestination.whatsapp) {
    if (Platform.isIOS) {
      await _shareToIos(
        creativeFile: File(path),
        shareText: shareText,
      );
    } else {
      await WhatsappShareAndroid.shareFiles(
        [XFile(path)],
        text: shareText,
      );
    }
  } else if (destination == PosterLayoutShareDestination.externalShare) {
    if (Platform.isIOS) {
      await _shareToIos(
        creativeFile: File(path),
        shareText: shareText,
      );
    } else {
      await Share.shareXFiles(
        [XFile(path)],
        text: shareText,
      );
    }
  } else {
    throw ArgumentError.value(
        destination, 'destination', 'Unknown destination type for sharing');
  }
}

_shareToIos({required File creativeFile, required String shareText}) async {
  if (shareText.trim().isNotEmpty) {
    // For Premium Posters the shareText is Just a space, so we don't want to copy that to clipboard.
    await FlutterClipboard.copy(shareText);
    Utils.showToast(
      "పోస్టర్  వివరాలు కాపీ చేయబడ్డాయి.\nదయచేసి కంటెంట్‌లో పేస్ట్ చేయండి!",
      toastLength: Toast.LENGTH_LONG,
    );
  }
  await Share.shareXFiles(
    [XFile(creativeFile.path)],
  );
}
