import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/features/posters/services/poster_photo_update_config.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/utils/logger.dart';

class PosterPhotoPickerPlaceholderUI extends StatelessWidget {
  final double height;
  final String userPhotoType;
  final VoidCallback? onTap;

  const PosterPhotoPickerPlaceholderUI({
    super.key,
    this.height = 200,
    required this.userPhotoType,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final posterPhotoUpdateConfig = GetIt.I.get<PosterPhotoUpdateConfig>();
    final imageUrl =
        posterPhotoUpdateConfig.posterPhotoPlaceholderUrls[userPhotoType];
    if (imageUrl == null) {
      AppAnalytics.logEvent(
          name: "missing_poster_photo_placeholder_url",
          parameters: {
            "userPhotoType": userPhotoType,
          });
      logNonFatal(
          "Missing Poster Photo Placeholder Url for $userPhotoType", null,
          stackTrace: StackTrace.current);
    }

    return Container(
      height: height,
      width: double.infinity,
      padding: const EdgeInsets.only(left: 16, top: 16, right: 16),
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/images/chequered_bg.png'),
          fit: BoxFit.cover,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        child: Container(
          width: double.infinity,
          alignment: Alignment.bottomCenter,
          decoration: BoxDecoration(
            image: imageUrl != null
                ? DecorationImage(
                    image: NetworkImage(posterPhotoUpdateConfig
                        .posterPhotoPlaceholderUrls[userPhotoType]),
                    fit: BoxFit.contain,
                  )
                : null,
          ),
          child: Container(
            margin: const EdgeInsets.only(bottom: 50),
            decoration: BoxDecoration(
              color: const Color(0xFF292929),
              borderRadius: BorderRadius.circular(8),
            ),
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 32),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const Icon(Icons.photo_library),
                const SizedBox(width: 8),
                Text(context.getString(StringKey.galleryLabel))
              ],
            ),
          ),
        ),
      ),
    );
  }
}
