import 'dart:async';
import 'dart:io';

import 'package:flutter/services.dart';
import 'package:injectable/injectable.dart';
import 'package:praja/features/user/models/app_user.dart';
import 'package:praja/mixins/analytics.dart';
// import 'package:praja/services/app_state.dart';
import 'package:praja/services/user_store.dart';
import 'package:praja/utils/logger.dart';

@lazySingleton
class AppIconChanger {
  final UserStore _userStore;
  static const MethodChannel _channel = MethodChannel('icon_changer');
  // StreamSubscription<AppOpenState>? _appOpenStateSubscription;

  AppIconState _userCurrentState = AppIconState.free;

  AppIconChanger(this._userStore);

  // Future<void> _onAppOpenStateChange(AppOpenState state) async {
  //   if (state == AppOpenState.closed) {
  //     if (Platform.isIOS) return;
  //     _defaultIcon();
  //     _premiumIcon();
  //   }
  // }

  Future<void> startListening() async {
    _userStore.userStream.listen(_onUserChange);
  }

  Future<void> _defaultIcon() async {
    final appCurrentState = await _currentState();
    if (_userCurrentState == AppIconState.free &&
        appCurrentState == AppIconState.premium) {
      AppAnalytics.onAppIconChanged(from: 'premium', to: 'default');
      _changeToDefaultIcon();
    }
  }

  Future<void> _premiumIcon() async {
    final appCurrentState = await _currentState();
    if (_userCurrentState == AppIconState.premium &&
        appCurrentState == AppIconState.free) {
      AppAnalytics.onAppIconChanged(from: 'default', to: 'premium');
      _changeToPremiumIcon();
    }
  }

  void _onUserChange(AppUser? user) async {
    if (user == null) {
      _userCurrentState = AppIconState.free;
      return;
    }
    if (user.isPremium) {
      _userCurrentState = AppIconState.premium;
      if (Platform.isIOS) {
        await _premiumIcon();
      }
    } else {
      _userCurrentState = AppIconState.free;
      if (Platform.isIOS) {
        await _defaultIcon();
      }
    }
  }

  static Future<void> _changeToDefaultIcon() async {
    try {
      await _channel.invokeMethod('changeToDefaultIcon');
    } on PlatformException catch (e) {
      logInfo("Failed to change icon: ${e.message}");
    } on MissingPluginException catch (e) {
      logInfo("Failed to change icon: ${e.message}");
    }
  }

  static Future<void> _changeToPremiumIcon() async {
    try {
      await _channel.invokeMethod('changeToPremiumIcon');
    } on PlatformException catch (e) {
      logInfo("Failed to change icon: ${e.message}");
    } on MissingPluginException catch (e) {
      logInfo("Failed to change icon: ${e.message}");
    }
  }

  static Future<String> _getCurrentIconState() async {
    try {
      return await _channel.invokeMethod('getCurrentIconState');
    } on PlatformException catch (e) {
      logInfo("Failed to get current icon status: ${e.message}");
      return 'default';
    } on MissingPluginException catch (e) {
      logInfo("Failed to get current icon status: ${e.message}");
      return 'default';
    }
  }

  static Future<AppIconState> _currentState() async {
    final currentState = await _getCurrentIconState();
    if (currentState == 'premium') {
      return AppIconState.premium;
    } else {
      return AppIconState.free;
    }
  }
}

enum AppIconState {
  free,
  premium,
}
