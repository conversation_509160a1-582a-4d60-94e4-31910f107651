import 'package:json_annotation/json_annotation.dart';
import 'package:praja/models/v2/photo.dart';

part 'circle_identity.g.dart';

@JsonSerializable()
class CircleIdentity {
  final int id;
  final String name;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'name_en')
  final String nameEN;
  final String level;
  @J<PERSON><PERSON><PERSON>(name: 'hashid')
  final String? hashId;
  @<PERSON>son<PERSON>ey(name: 'photo')
  final Photo? profilePhoto;
  @Json<PERSON>ey(name: 'circle_type')
  final String type;
  @<PERSON>son<PERSON>ey(name: 'description')
  final String? description;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'members_count', defaultValue: 0)
  final int membersCount;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'verified', defaultValue: false)
  final bool verified;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'biggest', defaultValue: false)
  final bool biggest;

  @<PERSON>son<PERSON>ey(name: 'analytics_params')
  final Map<String, dynamic>? analyticsParams;

  CircleIdentity({
    required this.id,
    required this.name,
    required this.nameEN,
    required this.level,
    required this.hashId,
    required this.profilePhoto,
    required this.type,
    required this.description,
    required this.membersCount,
    required this.verified,
    required this.biggest,
    required this.analyticsParams,
  });

  factory CircleIdentity.fromJson(Map<String, dynamic> json) =>
      _$CircleIdentityFromJson(json);

  Map<String, dynamic> toJson() => _$CircleIdentityToJson(this);

  @override
  toString() {
    return 'CircleIdentity(id: $id, name: $name, nameEN: $nameEN, level: $level, hashId: $hashId, profilePhoto: $profilePhoto, type: $type, description: $description, membersCount: $membersCount, verified: $verified, biggest: $biggest, analyticsParams: $analyticsParams)';
  }
}
