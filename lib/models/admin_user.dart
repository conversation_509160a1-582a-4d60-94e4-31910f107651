import 'package:json_annotation/json_annotation.dart';

part 'admin_user.g.dart';

@JsonSerializable()
class AdminUser {
  @Json<PERSON>ey(name: 'name')
  final String name;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'photo_url')
  final String? photoUrl;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'phone', defaultValue: 0)
  final int phone;
  @<PERSON>son<PERSON>ey(name: 'whatsapp_link', defaultValue: '')
  final String whatsappLink;
  @<PERSON>sonKey(name: 'email', defaultValue: '')
  final String email;

  AdminUser({
    required this.name,
    this.photoUrl,
    required this.phone,
    required this.whatsappLink,
    required this.email,
  });

  factory AdminUser.fromJson(Map<String, dynamic> json) =>
      _$AdminUserFromJson(json);

  Map<String, dynamic> toJson() => _$AdminUserToJson(this);

  @override
  String toString() {
    return 'AdminUser{name: $name, photoUrl: $photoUrl, phone: $phone, whatsappLink: $whatsappLink, email: $email}';
  }
}
