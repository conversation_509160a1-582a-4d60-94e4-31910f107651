import 'package:json_annotation/json_annotation.dart';

part 'confirm_popup_options.g.dart';

@JsonSerializable()
class PopupOptions {
  @Json<PERSON>ey(name: 'confirm_text')
  final String? confirmText;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'cancel_btn_text')
  final String cancelBtnText;
  @Json<PERSON>ey(name: 'confirm_btn_text')
  final String confirmBtnText;

  PopupOptions({
    this.confirmText,
    required this.cancelBtnText,
    required this.confirmBtnText,
  });

  factory PopupOptions.fromJson(Map<String, dynamic> json) =>
      _$PopupOptionsFromJson(json);

  Map<String, dynamic> toJson() => _$PopupOptionsToJson(this);

  @override
  String toString() {
    return 'PopupOptions{confirmText: $confirmText, cancelBtnText: $cancelBtnText, confirmBtnText: $confirmBtnText}';
  }
}
