import 'package:json_annotation/json_annotation.dart';
part 'media_upload_url.g.dart';

@JsonSerializable()
class MediaUploadUrl {
  @Json<PERSON>ey(name: 'photo')
  final String photoUrl;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'video')
  final String videoUrl;

  MediaUploadUrl({
    required this.photoUrl,
    required this.videoUrl,
  });

  factory MediaUploadUrl.fromJson(Map<String, dynamic> json) =>
      _$MediaUploadUrlFromJson(json);

  Map<String, dynamic> toJson() => _$MediaUploadUrlToJson(this);

  @override
  String toString() {
    return 'UploadURLs{photoUrl: $photoUrl, videoUrl: $videoUrl}';
  }
}
