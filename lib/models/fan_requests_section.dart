import 'package:json_annotation/json_annotation.dart';
import 'package:praja/models/circle_details.dart';
import 'package:praja/models/feed_item_abstract_class.dart';
import 'package:praja/models/user_identity.dart';

part 'fan_requests_section.g.dart';

@JsonSerializable()
class FanRequestsSection extends FeedItem {
  @JsonKey(name: 'circle_details')
  final CircleDetails circleDetails;
  @JsonKey(name: 'users', defaultValue: [])
  final List<UserIdentity> users;
  @Json<PERSON>ey(name: 'users_count', defaultValue: 0)
  final int additionalUsersCount;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'header', defaultValue: '')
  final String header;
  @Json<PERSON>ey(name: 'cta_text', defaultValue: '')
  final String ctaText;
  @JsonKey(name: 'analytics_params')
  final Map<String, dynamic>? analyticsParams;

  FanRequestsSection({
    required this.circleDetails,
    required this.users,
    required this.additionalUsersCount,
    required this.header,
    required this.ctaText,
    this.analyticsParams,
    required super.feedType,
    super.feedItemId,
  });

  factory FanRequestsSection.fromJson(Map<String, dynamic> json) =>
      _$FanRequestsSectionFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$FanRequestsSectionToJson(this);

  @override
  String toString() {
    return 'FanRequestsSection{circleDetails: $circleDetails, users: $users, additionalUsersCount: $additionalUsersCount, header: $header, ctaText: $ctaText, analyticsParams: $analyticsParams, feedType: $feedType, feedItemId: $feedItemId}';
  }
}
