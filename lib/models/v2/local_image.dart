import 'package:json_annotation/json_annotation.dart';

part 'local_image.g.dart';

@JsonSerializable()
class LocalImage {
  @Json<PERSON>ey(name: 'path')
  final String path;
  @Json<PERSON>ey(name: 'height')
  final double? height;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'width')
  final double? width;

  /// used to keep the original values for tracking post compression
  /// in bytes
  @Json<PERSON>ey(name: 'source_size')
  final int? sourceSize;

  const LocalImage({
    required this.path,
    this.height,
    this.width,
    this.sourceSize,
  });

  LocalImage copyWith({
    String? path,
    double? height,
    double? width,
    int? sourceSize,
  }) {
    return LocalImage(
      path: path ?? this.path,
      height: height ?? this.height,
      width: width ?? this.width,
      sourceSize: sourceSize ?? this.sourceSize,
    );
  }

  factory LocalImage.fromJson(Map<String, dynamic> json) =>
      _$LocalImageFromJson(json);

  Map<String, dynamic> toJson() => _$LocalImageToJson(this);

  @override
  String toString() {
    return 'LocalImage(path: $path, height: $height, width: $width, sourceSize: $sourceSize)';
  }
}
