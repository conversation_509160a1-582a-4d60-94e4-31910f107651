import 'package:json_annotation/json_annotation.dart';
import 'package:praja/models/v2/local_image.dart';

import 'dimensions.dart';

part 'local_video.g.dart';

@JsonSerializable()
class LocalVideo {
  @Json<PERSON>ey(name: 'path')
  final String path;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'localThumbnail')
  final LocalImage? localThumbnail;

  /// used to keep the original values for tracking post compression
  @JsonKey(name: 'source_bitrate')
  final double? sourceBitrate;
  @Json<PERSON>ey(name: 'source_dimensions')
  final Dimensions? sourceDimensions;
  @Json<PERSON>ey(name: 'source_size')
  final int? sourceSize;

  const LocalVideo(
      {required this.path,
      this.localThumbnail,
      this.sourceBitrate,
      this.sourceDimensions,
      this.sourceSize});

  factory LocalVideo.fromJson(Map<String, dynamic> json) =>
      _$LocalVideoFromJson(json);

  Map<String, dynamic> toJson() => _$LocalVideoToJson(this);

  @override
  String toString() {
    return 'LocalVideo(path: $path, localThumbnail: $localThumbnail, sourceBitrate: $sourceBitrate, sourceDimensions: $sourceDimensions, sourceSize: $sourceSize)';
  }
}
