import 'package:json_annotation/json_annotation.dart';
import 'package:praja/models/user.dart';

part 'comment.g.dart';

@JsonSerializable()
class Comment {
  @Json<PERSON>ey(name: 'id')
  final int id;
  @<PERSON>son<PERSON>ey(name: 'user')
  final User user;
  @<PERSON>son<PERSON>ey(name: 'comment')
  final String comment;
  @Json<PERSON>ey(name: 'created_at')
  DateTime createdAt;

  Comment({
    required this.id,
    required this.user,
    required this.comment,
    required this.createdAt,
  });

  factory Comment.fromJson(Map<String, dynamic> json) =>
      _$CommentFromJson(json);

  Map<String, dynamic> toJson() => _$CommentToJson(this);

  @override
  String toString() {
    return 'Comment{id: $id, user: $user, comment: $comment, createdAt: $createdAt}';
  }
}
