import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:praja/models/tag_alert_popup_data.dart';
import 'package:praja/models/tag_circle.dart';
import 'package:praja/models/tag_circle_level_config.dart';
import 'package:praja/models/tag_info_popup_data.dart';

part 'tag_data.g.dart';

@JsonSerializable()
class TagData extends Equatable {
  @J<PERSON><PERSON><PERSON>(name: 'pre_selected_circles', defaultValue: [])
  final List<TagCircle> preselectedCircles;
  @JsonKey(
      name: 'tag_header_text', defaultValue: 'సంబంధిత గ్రూప్‌కి షేర్ చేయండి')
  final String tagHeaderText;
  @Json<PERSON>ey(
      name: 'screen_header_text',
      defaultValue: 'పోస్ట్ కి సంబంధించిన గ్రూపులు మాత్రమే ఎంచుకోండి')
  final String screenHeaderText;
  @<PERSON>son<PERSON>ey(name: 'info_pop_up_config')
  final TagInfoPopUpData tagInfoPopUpData;
  @Json<PERSON>ey(name: 'alert_pop_up_config')
  final TagAlertPopUpData tagAlertPopUpData;
  @JsonKey(name: 'max_circles', defaultValue: 0)
  final int maxCirclesCount;
  @JsonKey(
      name: 'max_circles_toast_text',
      defaultValue: '3 గ్రూపులకు పరిమితం చేయబడింది')
  final String maxCirclesReachedToast;
  @JsonKey(name: 'circle_levels_count_config', defaultValue: [])
  final List<CircleLevelConfig> circleLevelsCountConfig;

  const TagData(
      {required this.preselectedCircles,
      required this.tagHeaderText,
      required this.maxCirclesReachedToast,
      required this.screenHeaderText,
      required this.tagInfoPopUpData,
      required this.tagAlertPopUpData,
      required this.maxCirclesCount,
      required this.circleLevelsCountConfig});

  factory TagData.fromJson(Map<String, dynamic> json) =>
      _$TagDataFromJson(json);

  Map<String, dynamic> toJson() => _$TagDataToJson(this);

  @override
  List<Object> get props => [
        circleLevelsCountConfig,
        maxCirclesCount,
        preselectedCircles,
        tagHeaderText,
        maxCirclesReachedToast,
        screenHeaderText,
        tagInfoPopUpData,
        tagAlertPopUpData
      ];

  TagData copyWith({
    List<TagCircle>? preselectedCircles,
    String? tagHeaderText,
    String? maxCirclesReachedToast,
    String? screenHeaderText,
    TagInfoPopUpData? tagInfoPopUpData,
    TagAlertPopUpData? tagAlertPopUpData,
    int? maxCirclesCount,
    List<CircleLevelConfig>? circleLevelsCountConfig,
  }) {
    return TagData(
      maxCirclesReachedToast:
          maxCirclesReachedToast ?? this.maxCirclesReachedToast,
      preselectedCircles: preselectedCircles ?? this.preselectedCircles,
      tagHeaderText: tagHeaderText ?? this.tagHeaderText,
      screenHeaderText: screenHeaderText ?? this.screenHeaderText,
      tagInfoPopUpData: tagInfoPopUpData ?? this.tagInfoPopUpData,
      tagAlertPopUpData: tagAlertPopUpData ?? this.tagAlertPopUpData,
      maxCirclesCount: maxCirclesCount ?? this.maxCirclesCount,
      circleLevelsCountConfig:
          circleLevelsCountConfig ?? this.circleLevelsCountConfig,
    );
  }

  @override
  String toString() {
    return 'TagData{preselectedCircles: $preselectedCircles, tagHeaderText: $tagHeaderText, screenHeaderText: $screenHeaderText, maxCirclesCount: $maxCirclesCount, maxCirclesReachedToast: $maxCirclesReachedToast, circleLevelsCountConfig: $circleLevelsCountConfig}';
  }
}
