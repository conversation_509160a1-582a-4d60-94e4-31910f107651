import 'dart:io';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:praja/constants/AppConstants.dart';
import 'package:praja/services/session_store.dart';
import 'package:praja/utils/device_id/device_id_platform.dart';
import 'package:praja/utils/utils.dart';

@injectable
class RequestHeadersInterceptor extends InterceptorsWrapper {
  final DeviceIdPlatform _deviceIdPlatform;
  final SessionStore _sessionStore;

  RequestHeadersInterceptor(
    this._deviceIdPlatform,
    this._sessionStore,
  );

  String? _buildNumber;
  String? _appVersion;
  String? _deviceId;
  String? _processorInfo;
  String? _deviceModel;
  String? _deviceMake;

  @override
  Future onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    await _initializeDeviceDetails();
    final headers = _generateHeaders(options.headers);

    return handler.next(options.copyWith(headers: headers));
  }

  Future<void> _initializeDeviceDetails() async {
    _appVersion ??= await Utils.getAppVersion();
    _buildNumber ??= await Utils.getBuildNumber();
    _deviceId ??= await _deviceIdPlatform.getDeviceId();
    _processorInfo ??= await Utils.getDeviceSOCDetails();
    _deviceModel ??= await Utils.getDeviceModel();
    _deviceMake ??= await Utils.getDeviceManufacturer();
  }

  Map<String, dynamic> _generateHeaders(Map<String, dynamic> existingHeaders) {
    final headers = Map<String, dynamic>.from(existingHeaders);

    headers.addAll({
      "X-Api-Key": Utils.getApiKey(),
      "X-App-Version": _appVersion,
      "X-App-BuildNumber": _buildNumber,
      "X-Internet-Connection": _getInternetConnectionType(),
      "X-App-OS": Platform.operatingSystem,
    });

    _addIfNotNull(headers, "X-Device-Model", _deviceModel);
    _addIfNotNull(headers, "X-Device-Make", _deviceMake);
    _addIfNotNull(headers, "X-Device-Id", _deviceId);
    _addIfNotNull(headers, "X-Processor-Info", _processorInfo);

    _addIfNotNull(headers, "X-Session-Referrer", _sessionStore.sessionReferrer);
    _addIfNotNull(headers, "X-Session-Source", _sessionStore.sessionSource);

    return headers;
  }

  /// Determines the current internet connection type.
  String _getInternetConnectionType() {
    final connections = GetIt.I.get<AppConstants>().appInternetConnectivityType;
    if (connections.contains(ConnectivityResult.wifi)) {
      return "wifi";
    } else if (connections.contains(ConnectivityResult.mobile)) {
      return "mobile";
    }
    return "none";
  }

  void _addIfNotNull(Map<String, dynamic> map, String key, String? value) {
    if (value != null && value.isNotEmpty) {
      map[key] = value;
    }
  }
}
