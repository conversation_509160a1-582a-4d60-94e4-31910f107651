import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:praja/common/app_event_bus.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/services/user.dart';
import 'package:praja/services/user_store.dart';

@injectable
class TokenInterceptor extends InterceptorsWrapper {
  final UserStore _userStore;
  final AppEventBus _appEventBus;
  TokenInterceptor(this._userStore, this._appEventBus);

  @override
  Future onRequest(RequestOptions options, handler) async {
    final accessToken = await _userStore.getUserToken();
    RequestOptions newOptions = options;
    if (accessToken.isNotEmpty) {
      final newHeaders = Map<String, dynamic>.from(options.headers);
      newHeaders['Authorization'] = "Bearer $accessToken";
      newOptions = options.copyWith(headers: newHeaders);
    }
    return handler.next(newOptions);
  }

  @override
  Future onResponse(Response response, handler) async {
    final String? newAccessToken = response.headers.value('X-Access-Token');
    if (newAccessToken != null) {
      _userStore.setUserToken(newAccessToken);
    }
    return handler.next(response);
  }

  @override
  Future onError(DioException err, handler) async {
    final response = err.response;
    if (response != null && response.statusCode == 401) {
      Uri requestUri = Uri.parse(err.requestOptions.path);
      // ignore 401 in logout API, otherwise it will go into infinite loop
      if (requestUri.path == '/logout') {
        return handler.next(err);
      }

      try {
        await UserService.logout();
      } catch (e) {
        // ignore
      } finally {
        _appEventBus.fire(UserUnauthenticatedEvent());

        AppAnalytics.logEvent(name: 'logout', parameters: {
          'reason': '401 UnAuthorized',
          'endpoint': err.requestOptions.path
        });
      }
    }
    return handler.next(err);
  }
}
