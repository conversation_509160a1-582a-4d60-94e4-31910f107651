import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:praja/common/app_event_bus.dart';
import 'package:praja/constants/AppConstants.dart';
import 'package:praja/errors/error_delegate.dart';
import 'package:praja/features/circle/service/circle_service.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/models/circle.dart';
import 'package:praja/presentation/circle_display_picture.dart';
import 'package:praja/shimmers/users.dart';
import 'package:praja/utils/utils.dart';

class PartySuggestion extends StatefulWidget {
  static const String tag = '/party-suggestions';
  final bool allowNextCta;
  final bool skipPartyMemberDecision;

  const PartySuggestion(
      {super.key,
      this.allowNextCta = false,
      this.skipPartyMemberDecision = true});

  @override
  State<PartySuggestion> createState() => _PartySuggestionState();
}

class _PartySuggestionState extends State<PartySuggestion> {
  bool isLoaded = false;
  bool enableNext = false;
  List<Circle> parties = [];
  Set<String> partyIds = {};
  bool showList = false;

  //If enabled, allows user to join just one circle
  bool restrictCircleJoin = GetIt.I.get<AppConstants>().exclusivePartyJoin;
  final CircleServiceV2 _circleServiceV2 = GetIt.I.get<CircleServiceV2>();
  final AppEventBus _feedEventsBus = GetIt.I.get<AppEventBus>();

  @override
  void initState() {
    super.initState();
    widget.allowNextCta;
    AppAnalytics.timeEvent(name: "party_suggestions_shown");
    fetchParties();
  }

  void updateCircleInParties(Circle updatedCircle) {
    setState(() {
      int index = parties.indexWhere((circle) => circle.id == updatedCircle.id);
      if (index >= 0) {
        parties[index] = updatedCircle;
      }
    });
  }

  getPartyList() {
    return ListView(
      physics: const BouncingScrollPhysics(),
      shrinkWrap: true,
      children: parties.map((e) {
        return partyTile(e);
      }).toList(),
    );
  }

  trackNextButtonClick() {
    AppAnalytics.logEvent(
        name: "party_suggestions_next_click",
        parameters: {"joined_parties": partyIds.length.toString()});
  }

  trackSkipButtonClick() {
    AppAnalytics.logEvent(
        name: "party_suggestions_skip_click",
        parameters: {"joined_parties": partyIds.length.toString()});
  }

  trackBackButtonClick() {
    AppAnalytics.logEvent(
        name: "party_suggestions_back_click",
        parameters: {"joined_parties": partyIds.length.toString()});
  }

  trackPartySuggestionsShown() {
    AppAnalytics.logEvent(name: "party_suggestions_shown");
  }

  Widget partyTile(Circle circle) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
            child: Row(
              children: [
                CircleDisplayPicture.fromCircle(
                  circle,
                  size: 48.0,
                ),
                const SizedBox(
                  width: 15,
                ),
                Flexible(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        circle.name,
                        style: const TextStyle(
                          fontSize: 15,
                        ),
                      ),
                      const SizedBox(
                        height: 5,
                      ),
                      Row(
                        children: [
                          const Icon(Icons.group,
                              size: 15, color: Color(0xff888888)),
                          const SizedBox(width: 5),
                          Text(
                            // ignore: avoid_hardcoded_strings_in_ui
                            "${circle.membersCount} ${context.getString(StringKey.circleMembersLabel)}",
                            style: const TextStyle(
                              color: Color(0xff888888),
                              fontSize: 11,
                            ),
                          ),
                        ],
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),
          circle.isUserJoined
              ? leaveButton(circle, updateCircleInParties)
              : joinButton(circle, updateCircleInParties)
        ],
      ),
    );
  }

  Widget joinButton(Circle circle, Function(Circle) updateCircleInParties) {
    return ElevatedButton(
        style: ButtonStyle(
          fixedSize: MaterialStateProperty.all(const Size(80, 28)),
          shape: MaterialStateProperty.all(RoundedRectangleBorder(
              side: const BorderSide(
                color: Colors.blue,
              ),
              borderRadius: BorderRadius.circular(6))),
          elevation: MaterialStateProperty.all(0),
          backgroundColor: MaterialStateProperty.all(Colors.transparent),
          minimumSize: MaterialStateProperty.all(const Size(0, 0)),
        ),
        onPressed: restrictCircleJoin
            ? () async {
                try {
                  Circle? circleToUnJoin = joinedCircle;
                  if (circleToUnJoin != null) {
                    try {
                      await _circleServiceV2.userUnjoin(circleToUnJoin);
                      AppAnalytics.logLeaveCircle(
                        circle,
                        source: "party_suggestions",
                      );
                      partyIds.remove(circleToUnJoin.id.toString());
                      setState(() {
                        circleToUnJoin.copyWith(
                            isUserJoined: false,
                            membersCount: circleToUnJoin.membersCount - 1);
                      });
                      updateCircleInParties(circleToUnJoin);
                    } catch (e) {
                      Utils.showToast(localisedErrorMessage(e));
                    }
                  }
                  setState(() {
                    circle = circle.copyWith(
                        isUserJoined: true,
                        membersCount: circle.membersCount + 1);
                    enableNext = true;
                  });
                  updateCircleInParties(circle);
                  joinedCircle = circle;
                  partyIds.add(circle.id.toString());
                  AppAnalytics.logJoinCircle(circle, "party_suggestions");
                  await _circleServiceV2.userJoin(circle);
                } catch (e) {
                  Utils.showToast(localisedErrorMessage(e));
                }
              }
            : () async {
                try {
                  setState(() {
                    circle = circle.copyWith(
                        isUserJoined: true,
                        membersCount: circle.membersCount + 1);
                    partyIds.add(circle.id.toString());
                    if (partyIds.isEmpty) {
                      enableNext = false;
                    } else {
                      enableNext = true;
                    }
                  });
                  updateCircleInParties(circle);
                  AppAnalytics.logJoinCircle(circle, "party_suggestions");
                  await _circleServiceV2.userJoin(circle);
                } catch (e) {
                  Utils.showToast(localisedErrorMessage(e));
                }
              },
        child: Padding(
          padding: const EdgeInsets.only(top: 3.0),
          child: Text(
            context.getString(StringKey.joinLabel),
            style: const TextStyle(fontSize: 12, color: Colors.blue),
          ),
        ));
  }

  Widget leaveButton(Circle circle, Function(Circle) updateCircleInParties) {
    return ElevatedButton(
      style: ButtonStyle(
        fixedSize: MaterialStateProperty.all(const Size(80, 28)),
        shape: MaterialStateProperty.all(RoundedRectangleBorder(
            side: const BorderSide(
              color: Colors.blue,
            ),
            borderRadius: BorderRadius.circular(6))),
        elevation: MaterialStateProperty.all(0),
        backgroundColor: MaterialStateProperty.all(Colors.transparent),
        minimumSize: MaterialStateProperty.all(const Size(0, 0)),
      ),
      onPressed: restrictCircleJoin
          ? () async {
              setState(() {
                circle = circle.copyWith(
                    isUserJoined: false, membersCount: circle.membersCount - 1);
                enableNext = false;
              });
              updateCircleInParties(circle);
              joinedCircle = null;
              partyIds.remove(circle.id.toString());
              try {
                await _circleServiceV2.userUnjoin(circle);
                AppAnalytics.logLeaveCircle(
                  circle,
                  source: "party_suggestions",
                );
              } catch (e) {
                Utils.showToast(localisedErrorMessage(e));
                setState(() {
                  circle = circle.copyWith(
                      isUserJoined: true,
                      membersCount: circle.membersCount + 1);
                });
                updateCircleInParties(circle);
              }
            }
          : () async {
              setState(() {
                circle = circle.copyWith(
                    isUserJoined: false, membersCount: circle.membersCount - 1);
                partyIds.remove(circle.id.toString());
                if (partyIds.isEmpty) {
                  enableNext = false;
                } else {
                  enableNext = true;
                }
              });
              updateCircleInParties(circle);
              try {
                await _circleServiceV2.userUnjoin(circle);
                AppAnalytics.logLeaveCircle(
                  circle,
                  source: "party_suggestions",
                );
              } catch (e) {
                Utils.showToast(localisedErrorMessage(e));
                setState(() {
                  circle = circle.copyWith(
                      isUserJoined: true,
                      membersCount: circle.membersCount + 1);
                });
                updateCircleInParties(circle);
              }
            },
      child: const Icon(
        Icons.done,
        color: Colors.blue,
        size: 20,
      ),
    );
  }

  getNextButton() {
    return ElevatedButton(
        style: ButtonStyle(
            backgroundColor: MaterialStateProperty.all(enableNext
                ? Theme.of(context).primaryColor
                : const Color(0xffc5ccd1)),
            fixedSize: MaterialStateProperty.all(
                Size(MediaQuery.of(context).size.width / 1.4, 50))),
        onPressed: enableNext
            ? () {
                trackNextButtonClick();
                _feedEventsBus.fire(FeedInvalidatedEvent());
                Navigator.of(context).pop();
              }
            : () {
                trackSkipButtonClick();
                Navigator.of(context).pop();
              },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              enableNext
                  ? context.getString(StringKey.continueButtonLabel)
                  : context.getString(StringKey.skipButtonLabel),
              style: const TextStyle(fontSize: 17, color: Colors.white),
            ),
            const SizedBox(
              width: 10,
            ),
            const Padding(
              padding: EdgeInsets.only(bottom: 5),
              child: Icon(
                Icons.arrow_forward_rounded,
                size: 20,
                color: Colors.white,
              ),
            )
          ],
        ));
  }

  getSkipButton() {
    return ElevatedButton(
        style: ButtonStyle(
            elevation: MaterialStateProperty.all(0),
            backgroundColor: MaterialStateProperty.all(Colors.transparent),
            fixedSize: MaterialStateProperty.all(
                Size(MediaQuery.of(context).size.width / 1.6, 50))),
        onPressed: () {
          trackSkipButtonClick();
          if (partyIds.isNotEmpty) {
            //Provider.of<AppConstants>(context, listen: false).refreshFeed();
          }
          Navigator.of(context).pop();
        },
        child: Text(
          context.getString(StringKey.skipButtonLabel),
          style: const TextStyle(color: Colors.white, fontSize: 17),
        ));
  }

  getHeader() {
    return Text(
      context.getString(StringKey.partySuggestionsScreenHeaderText),
      style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
    );
  }

  Circle? joinedCircle;

  fetchParties() async {
    try {
      parties = await _circleServiceV2.fetchSuggestedParties();
      if (mounted) {
        setState(() {
          isLoaded = true;
        });
      }
      trackPartySuggestionsShown();
    } catch (e) {
      Utils.showToast(localisedErrorMessage(e));
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      onPopInvoked: (bool popped) async {
        if (popped) {
          trackBackButtonClick();
          if (partyIds.isNotEmpty) {
            _feedEventsBus.fire(FeedInvalidatedEvent());
          }
        }
      },
      child: Scaffold(
        body: Container(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          color: Colors.white,
          child: Padding(
              padding:
                  const EdgeInsets.only(top: 0, bottom: 0, left: 20, right: 20),
              child: (showList || widget.skipPartyMemberDecision)
                  ? showPartyList()
                  : showDecision(context)),
        ),
      ),
    );
  }

  showPartyList() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 20),
        child: Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            const SizedBox(
              height: 10,
            ),
            getHeader(),
            const SizedBox(
              height: 30,
            ),
            Expanded(
                child: isLoaded
                    ? getPartyList()
                    : const UsersShimmer(
                        count: 10,
                      )),
            if (widget.allowNextCta) ...[
              const SizedBox(
                height: 20,
              ),
              getNextButton(),
            ]
          ],
        ),
      ),
    );
  }

  trackYesClick() {
    AppAnalytics.logEvent(name: 'party_suggestions_yes_click');
  }

  trackNoClick() {
    AppAnalytics.logEvent(name: 'party_suggestions_no_click');
  }

  Widget showDecision(BuildContext context) {
    return SafeArea(
      child: SizedBox(
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height,
        child: Column(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              context.getString(StringKey.areYouPoliticalFanText),
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(
              height: 35,
            ),
            ElevatedButton(
                style: ButtonStyle(
                    fixedSize: MaterialStateProperty.all(const Size(250, 52))),
                onPressed: () {
                  trackYesClick();
                  setState(() {
                    showList = true;
                  });
                },
                child: Text(
                  context.getString(StringKey.yesLabel),
                  style: const TextStyle(fontSize: 15),
                )),
            const SizedBox(
              height: 10,
            ),
            Text(
              context.getString(StringKey.joinForLatestPoliticalUpdatesText),
              style: const TextStyle(fontSize: 13),
            ),
            const SizedBox(
              height: 30,
            ),
            ElevatedButton(
                style: ButtonStyle(
                    fixedSize: MaterialStateProperty.all(const Size(250, 52))),
                onPressed: () {
                  trackNoClick();
                  Navigator.of(context).pop();
                },
                child: Text(
                  context.getString(StringKey.noLabel),
                  style: const TextStyle(fontSize: 15),
                )),
            const SizedBox(
              height: 10,
            ),
            Text(
              context.getString(StringKey.knowWhatIsHappeningText),
              style: const TextStyle(fontSize: 13),
            ),
          ],
        ),
      ),
    );
  }
}
