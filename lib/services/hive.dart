import 'dart:typed_data';

import 'package:hive_flutter/hive_flutter.dart';
import 'package:injectable/injectable.dart';
import 'package:praja/constants/hive_type_ids.dart';
import 'package:praja/features/localization/string_store.dart';
import 'package:praja/features/posters/services/poster_preview_store.dart';
import 'package:praja/services/link/link_preview_store.dart';
import 'package:praja/services/user/user_identity_store.dart';

import 'circle/circle_identity_store.dart';
import 'post/post_preview_store.dart';

/// Ensures Hive is initialized only once in the app.
@lazySingleton
class HiveDelegate {
  bool _initialized = false;

  Future<void> _initialize() async {
    if (_initialized) return;
    await Hive.initFlutter();
    if (!Hive.isAdapterRegistered(HiveTypeId.userIdentityValue)) {
      Hive.registerAdapter(UserIdentityValueAdapter());
    }
    if (!Hive.isAdapterRegistered(HiveTypeId.circleIdentityValue)) {
      Hive.registerAdapter(CircleIdentityValueAdapter());
    }
    if (!Hive.isAdapterRegistered(HiveTypeId.postPreviewValue)) {
      Hive.registerAdapter(PostPreviewValueAdapter());
    }
    if (!Hive.isAdapterRegistered(HiveTypeId.linkPreviewValue)) {
      Hive.registerAdapter(LinkPreviewValueAdapter());
    }
    if (!Hive.isAdapterRegistered(HiveTypeId.createPosterPreviewValue)) {
      Hive.registerAdapter(CreatePosterPreviewValueAdapter());
    }
    if (!Hive.isAdapterRegistered(HiveTypeId.pluralizedStrings)) {
      Hive.registerAdapter(PluralizedStringAdapter());
    }
    _initialized = true;
  }

  Future<Box<E>> openBox<E>(
    String name, {
    HiveCipher? encryptionCipher,
    bool crashRecovery = true,
    String? path,
    Uint8List? bytes,
    String? collection,
  }) async {
    await _initialize();
    return await Hive.openBox<E>(
      name,
      encryptionCipher: encryptionCipher,
      crashRecovery: crashRecovery,
      path: path,
      bytes: bytes,
      collection: collection,
    );
  }
}
