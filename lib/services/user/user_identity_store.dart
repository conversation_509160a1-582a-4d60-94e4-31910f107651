import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:injectable/injectable.dart';
import 'package:praja/constants/hive_type_ids.dart';
import 'package:praja/models/user_identity.dart';
import 'package:praja/services/hive.dart';
import 'package:praja/services/user_store.dart';
import 'package:praja/utils/logger.dart';

part 'user_identity_store.g.dart';

/// Persistent Key value store for [UserIdentity]s
@lazySingleton
class UserIdentityStore {
  HiveDelegate hive;
  UserStore userStore;

  UserIdentityStore(this.hive, this.userStore);

  Future<void> listenToLogout() async {
    userStore.userStream.listen((user) async {
      if (user == null) {
        final box = await _getBox();
        box.clear();
      }
    });
  }

  Future<UserIdentitiesResult> getIdentities(List<int> userIds) async {
    final box = await _getBox();
    final staleIds = <int>[];
    final missingIds = <int>[];
    final serializedIdentityList = <String>[];
    for (final userId in userIds) {
      final value = box.get(userId);
      if (value != null) {
        serializedIdentityList.add(value.userIdentitySerialized);
        if (value.isStale) {
          staleIds.add(userId);
        }
      } else {
        missingIds.add(userId);
      }
    }

    try {
      final identityMap =
          await compute(_decodeAsUserIdentityMap, serializedIdentityList);
      return UserIdentitiesResult(
          identities: identityMap, staleIds: staleIds, missingIds: missingIds);
    } catch (e, stackTrace) {
      // this can happen if the json is not conforming to the current [UserIdentity] model
      // since the shape of the data has changed since the last time it was stored
      logNonFatal('Error while decoding user identities from hive box', e,
          stackTrace: stackTrace);

      // clearing the box to avoid the same errors occurring again
      await box.clear();

      return UserIdentitiesResult(
          identities: {}, staleIds: [], missingIds: userIds);
    }
  }

  Future<void> putMap(Map<String, UserIdentity> identities) async {
    final box = await _getBox();
    final map = await compute(_encodeMapAsValueMap, identities);
    await box.putAll(map);
  }

  Future<void> putList(List<UserIdentity> identities) async {
    final box = await _getBox();
    final map = await compute(_encodeListAsValueMap, identities);
    await box.putAll(map);
  }

  Box<UserIdentityValue>? _box;

  Future<Box<UserIdentityValue>> _getBox() async {
    return _box ??= await hive.openBox<UserIdentityValue>('user_identities');
  }
}

/// Json serializes the user identities and creates a map ready to be stored in hive
Map<int, UserIdentityValue> _encodeMapAsValueMap(
    Map<String, UserIdentity> identities) {
  final now = DateTime.now().millisecondsSinceEpoch;
  final map = <int, UserIdentityValue>{};
  for (final value in identities.values) {
    map[value.id] = UserIdentityValue(
      userIdentitySerialized: json.encode(value.toJson()),
      updatedAt: now,
    );
  }
  return map;
}

/// Json serializes the user identities and creates a map ready to be stored in hive
Map<int, UserIdentityValue> _encodeListAsValueMap(
    List<UserIdentity> identities) {
  final now = DateTime.now().millisecondsSinceEpoch;
  final map = <int, UserIdentityValue>{};
  for (final value in identities) {
    map[value.id] = UserIdentityValue(
      userIdentitySerialized: json.encode(value.toJson()),
      updatedAt: now,
    );
  }
  return map;
}

Map<int, UserIdentity> _decodeAsUserIdentityMap(
    List<String> serializedIdentityList) {
  final map = <int, UserIdentity>{};
  for (final serializedIdentity in serializedIdentityList) {
    final userIdentity = UserIdentity.fromJson(json.decode(serializedIdentity));
    map[userIdentity.id] = userIdentity;
  }
  return map;
}

class UserIdentitiesResult {
  final Map<int, UserIdentity> identities;
  final List<int> staleIds;
  final List<int> missingIds;

  UserIdentitiesResult(
      {required this.identities,
      required this.staleIds,
      required this.missingIds});
}

/// Value stored in the hive box
/// includes updatedAt timestamp to determine staleness
@HiveType(typeId: HiveTypeId.userIdentityValue)
class UserIdentityValue extends HiveObject {
  @HiveField(0)
  final String userIdentitySerialized;
  @HiveField(1)
  final int updatedAt;

  UserIdentityValue(
      {required this.userIdentitySerialized, required this.updatedAt});

  bool get isStale =>
      DateTime.now().millisecondsSinceEpoch - updatedAt >
      const Duration(minutes: 15).inMilliseconds;

  @override
  String toString() {
    return 'UserIdentityValue{userIdentity: $userIdentitySerialized, updatedAt: $updatedAt}';
  }
}
