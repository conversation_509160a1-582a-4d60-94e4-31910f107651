import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';

//An instance of AppCacheManager has stale duration of 30 days
class AppCacheManager extends CacheManager {
  static const key = "libCachedImageDataV2";

  static AppCacheManager? _instance;
  static const Duration _duration = Duration(days: 30);
  static AppCacheManager get instance {
    _instance ??= AppCacheManager._();
    return _instance!;
  }

  /// The DefaultCacheManager that can be easily used directly. The code of
  /// this implementation can be used as inspiration for more complex cache
  /// managers.
  @Deprecated('Use AppCacheManager.instance instead')
  factory AppCacheManager() {
    _instance ??= AppCacheManager._();

    return _instance!;
  }

  AppCacheManager._()
      : super(Config(key, stalePeriod: _duration, maxNrOfCacheObjects: 1000));

  Future<String> getFilePath() async {
    var directory = await getTemporaryDirectory();
    return p.join(directory.path, key);
  }
}
