import 'dart:async';

import 'package:flutter/material.dart';

class AppState {
  static AppLifecycleState _currentState = AppLifecycleState.resumed;
  static AppLifecycleState get currentState => _currentState;
  static set currentState(AppLifecycleState state) {
    _currentState = state;
    _controller.add(state);
  }

  static final StreamController<AppLifecycleState> _controller =
      StreamController.broadcast();

  /// Stream of [AppLifecycleState] changes
  static Stream<AppLifecycleState> get lifecycleStateStream =>
      _controller.stream;

  static Stream<AppOpenState> get appOpenStateStream =>
      lifecycleStateStream.map((state) => AppOpenState.from(state)).distinct();

  /// App is visible and responding to user input
  static bool isInForeground() {
    return _currentState.isInForeground();
  }

  /// App is not visible
  static bool isInBackground() {
    return _currentState.isInBackground();
  }
}

extension AppLifecycleStateX on AppLifecycleState {
  bool isInForeground() {
    return this == AppLifecycleState.resumed;
  }

  bool isInBackground() {
    return this == AppLifecycleState.paused;
  }
}

enum AppOpenState {
  open,
  closed;

  factory AppOpenState.from(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.resumed:
      case AppLifecycleState.inactive:
        return AppOpenState.open;

      case AppLifecycleState.paused:
      case AppLifecycleState.detached:
      case AppLifecycleState.hidden:
        return AppOpenState.closed;
    }
  }
}
