//Party Gradients
final Map<String, dynamic> ysrcpPartyGradients = {
  "badge_banner_gradients": {
    "colors": [4278205290, 4278347444, 4278347444, 4278205290],
    "stops": [0.0, 0.3368, 0.6826, 1.0],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "upper_footer_gradients": {
    "colors": [4278205290, 4278347444, 4278347444, 4278205290],
    "stops": [0.0, 0.3368, 0.6826, 1.0],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "identity_border_gradients": {
    "colors": [4278205290, 4278347444, 4278347444, 4278205290],
    "stops": [0.0, 0.3368, 0.6826, 1.0],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "footer_gradients": {
    "colors": [4278226502, 4280466360, 4278347444],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0, "end_x": -1.0, "end_y": 0}
  },
  "party_icon_background_gradients": {
    "colors": [4278226502, 4280466360, 4278347444],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0, "end_x": -1.0, "end_y": 0}
  },
  "badge_ribbon_background_gradients": {
    "colors": [2147498858, 2147641012, 2147525631, 2147641012, 2147498858],
    "stops": [0.0111, 0.3012, 0.5163, 0.7417, 0.9944],
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "background_gradients": {
    "colors": [4278226502, 4280466360, 4278347444],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": -1.0, "end_x": 1.0, "end_y": 1.0}
  }
};
final Map<String, dynamic> tdpPartyGradients = {
  "badge_banner_gradients": {
    "colors": [4290190120, 4292026416, 4290190120],
    "stops": [0.005, 0.5, 0.995],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "upper_footer_gradients": {
    "colors": [4290190120, 4292026416, 4290190120],
    "stops": [0.005, 0.5, 0.995],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "identity_border_gradients": {
    "colors": [4290190120, 4292026416, 4290190120],
    "stops": [0.005, 0.5, 0.995],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "footer_gradients": {
    "colors": [4294360320, 4292026416],
    "stops": [0.0603, 0.9463],
    "directions": {"begin_x": 1.0, "begin_y": 0.0, "end_x": -1.0, "end_y": 0.0}
  },
  "party_icon_background_gradients": {
    "colors": [4294360320, 4292026416],
    "stops": [0.0603, 0.9463],
    "directions": {"begin_x": 1.0, "begin_y": 0.0, "end_x": -1.0, "end_y": 0.0}
  },
  "badge_ribbon_background_gradients": {
    "colors": [2164025858, 2163337504, 2164260863, 2164025858, 2163337504],
    "stops": [-0.0389, 0.235, 0.4109, 0.7924, 1.0421],
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": -0.0}
  },
  "background_gradients": {
    "colors": [4294360320, 4292026416],
    "stops": [0.0603, 0.9463],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  }
};
final Map<String, dynamic> bjpPartyGradients = {
  "badge_banner_gradients": {
    "colors": [4289480448, 4294210070, 4294210070, 4289480448],
    "stops": [0.0, 0.3472, 0.6441, 1.0],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "upper_footer_gradients": {
    "colors": [4289480448, 4294210070, 4294210070, 4289480448],
    "stops": [0.0, 0.3472, 0.6441, 1.0],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "identity_border_gradients": {
    "colors": [4289480448, 4294210070, 4294210070, 4289480448],
    "stops": [0.0, 0.3472, 0.6441, 1.0],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "footer_gradients": {
    "colors": [4294951461, 4294952827, 4294210070],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0, "end_x": -1.0, "end_y": 0}
  },
  "party_icon_background_gradients": {
    "colors": [4294951461, 4294952827, 4294210070],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0, "end_x": -1.0, "end_y": 0}
  },
  "badge_ribbon_background_gradients": {
    "colors": [2158774016, 2163503638, 2163503638, 2158774016],
    "stops": [-0.0603, 0.3482, 0.6975, 1.1163],
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": -0.0}
  },
  "background_gradients": {
    "colors": [4294951461, 4294952827, 4294210070],
    "stops": null,
    "directions": {
      "begin_x": -1.0,
      "begin_y": -1.0,
      "end_x": -1.0,
      "end_y": 1.0
    }
  }
};
final Map<String, dynamic> congressPartyGradients = {
  "badge_banner_gradients": {
    "colors": [4278205978, 4279206463, 4279206463, 4278205978],
    "stops": [0.0, 0.2899, 0.6701, 1.0],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "upper_footer_gradients": {
    "colors": [4278205978, 4279206463, 4279206463, 4278205978],
    "stops": [0.0, 0.2899, 0.6701, 1.0],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "identity_border_gradients": {
    "colors": [4278205978, 4279206463, 4279206463, 4278205978],
    "stops": [0.0, 0.2899, 0.6701, 1.0],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "footer_gradients": {
    "colors": [4294144034, 4294966509, 4281703236],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0, "end_x": -1.0, "end_y": 0}
  },
  "party_icon_background_gradients": {
    "colors": [4294144034, 4294966509, 4281703236],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0, "end_x": -1.0, "end_y": 0}
  },
  "badge_ribbon_background_gradients": {
    "colors": [2147499546, 2148500031, 2148500031, 2147499546],
    "stops": [-0.0603, 0.2808, 0.7282, 1.1163],
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": -0.0}
  },
  "background_gradients": {
    "colors": [4294144034, 4294966509, 4281703236],
    "stops": null,
    "directions": {
      "begin_x": -1.0,
      "begin_y": -1.0,
      "end_x": -1.0,
      "end_y": 1.0
    }
  }
};
final Map<String, dynamic> brsPartyGradients = {
  "badge_banner_gradients": {
    "colors": [4289462374, 4293722252, 4293722252, 4289462374],
    "stops": [0.0295, 0.3889, 0.6336, 0.9826],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "upper_footer_gradients": {
    "colors": [4289462374, 4293722252, 4293722252, 4289462374],
    "stops": [0.0295, 0.3889, 0.6336, 0.9826],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "identity_border_gradients": {
    "colors": [4289462374, 4293722252, 4293722252, 4289462374],
    "stops": [0.0295, 0.3889, 0.6336, 0.9826],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "footer_gradients": {
    "colors": [4294213573, 4293722252],
    "stops": [0.0, 0.97],
    "directions": {"begin_x": 1.0, "begin_y": 0, "end_x": -1.0, "end_y": 0}
  },
  "party_icon_background_gradients": {
    "colors": [4294213573, 4293722252],
    "stops": [0.0, 0.97],
    "directions": {"begin_x": 1.0, "begin_y": 0, "end_x": -1.0, "end_y": 0}
  },
  "badge_ribbon_background_gradients": {
    "colors": [4289462374, 4293722252, 4293722252, 4289462374],
    "stops": [-0.0282, 0.363, 0.6295, 1.0094],
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": -0.0}
  },
  "background_gradients": {
    "colors": [4294213573, 4293722252],
    "stops": null,
    "directions": {
      "begin_x": -1.0,
      "begin_y": 1.0,
      "end_x": -1.0,
      "end_y": -1.0
    }
  }
};
final Map<String, dynamic> neutralPartyGradients = {
  "badge_banner_gradients": {
    "colors": [4278215167, 4288798207],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0, "end_x": -1.0, "end_y": 0}
  },
  "upper_footer_gradients": {
    "colors": [16777215, 16777215],
    "stops": [0, 1],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "identity_border_gradients": {
    "colors": [16777215, 16777215],
    "stops": [0, 1],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "footer_gradients": {
    "colors": [4278215167, 4288798207],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0, "end_x": -1.0, "end_y": 0}
  },
  "party_icon_background_gradients": {
    "colors": [4278215167, 4288798207],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0, "end_x": -1.0, "end_y": 0}
  },
  "badge_ribbon_background_gradients": {
    "colors": [4287619839, 4278748927],
    "stops": [0.389, 1.0421],
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "background_gradients": {
    "colors": [4278215167, 4288798207],
    "stops": [0.0, 1.0],
    "directions": {"begin_x": 0.0, "begin_y": -1.0, "end_x": 0.0, "end_y": 1.0}
  }
};
final Map<String, dynamic> jspPartyGradients = {
  "badge_banner_gradients": {
    "colors": [4283695104, 4291559424, 4291559424, 4283695104],
    "stops": [0.0, 0.3264, 0.7118, 1.0],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "upper_footer_gradients": {
    "colors": [4283695104, 4291559424, 4291559424, 4283695104],
    "stops": [0.0, 0.3264, 0.7118, 1.0],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "identity_border_gradients": {
    "colors": [4283695104, 4291559424, 4291559424, 4283695104],
    "stops": [0.0, 0.3264, 0.7118, 1.0],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "footer_gradients": {
    "colors": [4291559424, 4294946992, 4291559424],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0, "end_x": -1.0, "end_y": 0}
  },
  "party_icon_background_gradients": {
    "colors": [4291559424, 4294946992, 4291559424],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0, "end_x": -1.0, "end_y": 0}
  },
  "badge_ribbon_background_gradients": {
    "colors": [2152988672, 2160852992, 2160852992, 2152988672],
    "stops": [-0.0603, 0.3237, 0.7772, 1.1163],
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "background_gradients": {
    "colors": [4291559424, 4294946992, 4291559424],
    "stops": null,
    "directions": {
      "begin_x": -1.0,
      "begin_y": -1.0,
      "end_x": -1.0,
      "end_y": 1.0
    }
  }
};

//Shiny Identity Gradients for All Parties
final Map<String, dynamic> ysrcpShinyGradientsMock = {
  "identity_border_gradients": {
    "colors": [
      0xFFFFFFFF, // White
      0xFF0266B4, // Blue
      0xFF2393EA, // Lighter Blue
      0xFFFFFFFF, // White
      0xFF0266B4, // Blue
      0xFFFFFFFF, // White
      0xFF003B6A, // Dark Blue
    ],
    "stops": [
      -0.0531,
      0.2081,
      0.3296,
      0.4987,
      0.8824,
      1.0248,
      1.1132,
    ],
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "identity_inner_background_gradients": {
    "colors": [0xff0266B4, 0xff0266B4],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0.0, "end_x": -1.0, "end_y": 0.0}
  },
  "badge_banner_gradients": {
    "colors": [4278205290, 4278347444, 4278347444, 4278205290],
    "stops": [0.0, 0.3368, 0.6826, 1.0],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "footer_gradients": {
    "colors": [0x00000000, 0x00000000, 0x00000000],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0, "end_x": -1.0, "end_y": 0}
  },
  "badge_ribbon_background_gradients": {
    "colors": [2147498858, 2147641012, 2147525631, 2147641012, 2147498858],
    "stops": [0.0111, 0.3012, 0.5163, 0.7417, 0.9944],
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "background_gradients": {
    "colors": [4278226502, 4280466360, 4278347444],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": -1.0, "end_x": 1.0, "end_y": 1.0}
  }
};
final Map<String, dynamic> tdpShinyGradientsMock = {
  "identity_border_gradients": {
    "colors": [
      0xFFD32030, // Red
      0xFFFFFFFF, // White
      0xFFF37216, // Orange
      0xFFFFFFFF, // White
      0xFFF37216, // Orange
      0xFFD32030, // Red
      0xFFFFFFFF,
    ],
    "stops": [
      -0.0142,
      0.1858,
      0.4025,
      0.5791,
      0.7399,
      0.8310,
      0.9501,
    ],
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "identity_inner_background_gradients": {
    "colors": [0xffF6BD00, 0xffF6BD00],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0.0, "end_x": -1.0, "end_y": 0.0}
  },
  "badge_banner_gradients": {
    "colors": [4290190120, 4292026416, 4290190120],
    "stops": [0.005, 0.5, 0.995],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "footer_gradients": {
    "colors": [0x00000000, 0x00000000, 0x00000000],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0, "end_x": -1.0, "end_y": 0}
  },
  "badge_ribbon_background_gradients": {
    "colors": [2164025858, 2163337504, 2164260863, 2164025858, 2163337504],
    "stops": [-0.0389, 0.235, 0.4109, 0.7924, 1.0421],
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": -0.0}
  },
  "background_gradients": {
    "colors": [4294360320, 4292026416],
    "stops": [0.0603, 0.9463],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  }
};
final Map<String, dynamic> jspShinyGradientsMock = {
  "identity_border_gradients": {
    "colors": [
      0xFFCC0000, // Red
      0xFFFFFFFF, // White
      0xFFCC0000, // Red
      0xFFFFFFFF, // White
      0xFFFFB0B0, // Light Pink
      0xFFCC0000, // Red
      0xFFFFFFFF,
    ],
    "stops": [
      0.0007,
      0.0927,
      0.2669,
      0.4567,
      0.6412,
      0.7898,
      0.9280,
    ],
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "identity_inner_background_gradients": {
    "colors": [0xffCC0000, 0xffCC0000],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0.0, "end_x": -1.0, "end_y": 0.0}
  },
  "badge_banner_gradients": {
    "colors": [4283695104, 4291559424, 4291559424, 4283695104],
    "stops": [0.0, 0.3264, 0.7118, 1.0],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "footer_gradients": {
    "colors": [0x00000000, 0x00000000, 0x00000000],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0, "end_x": -1.0, "end_y": 0}
  },
  "badge_ribbon_background_gradients": {
    "colors": [2152988672, 2160852992, 2160852992, 2152988672],
    "stops": [-0.0603, 0.3237, 0.7772, 1.1163],
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "background_gradients": {
    "colors": [4291559424, 4294946992, 4291559424],
    "stops": null,
    "directions": {
      "begin_x": -1.0,
      "begin_y": -1.0,
      "end_x": -1.0,
      "end_y": 1.0
    }
  }
};
final Map<String, dynamic> bjpShinyGradientsMock = {
  "identity_border_gradients": {
    "colors": [
      0xFFFFFFFF, // White
      0xFFAC4700, // Brownish Orange
      0xFFF47216, // Light Orange
      0xFFFFFFFF, // White
      0xFFF47216, // Light Orange
      0xFFFFFFFF, // White
      0xFFFF6F09,
    ],
    "stops": [
      0.0316,
      0.167,
      0.3482,
      0.5174,
      0.6975,
      0.8838,
      0.9907,
    ],
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "identity_inner_background_gradients": {
    "colors": [0xffF47216, 0xffF47216],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0.0, "end_x": -1.0, "end_y": 0.0}
  },
  "badge_banner_gradients": {
    "colors": [4289480448, 4294210070, 4294210070, 4289480448],
    "stops": [0.0, 0.3472, 0.6441, 1.0],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "footer_gradients": {
    "colors": [0x00000000, 0x00000000, 0x00000000],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0, "end_x": -1.0, "end_y": 0}
  },
  "badge_ribbon_background_gradients": {
    "colors": [2158774016, 2163503638, 2163503638, 2158774016],
    "stops": [-0.0603, 0.3482, 0.6975, 1.1163],
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": -0.0}
  },
  "background_gradients": {
    "colors": [4294951461, 4294952827, 4294210070],
    "stops": null,
    "directions": {
      "begin_x": -1.0,
      "begin_y": -1.0,
      "end_x": -1.0,
      "end_y": 1.0
    }
  }
};
final Map<String, dynamic> congressShinyGradientsMock = {
  "identity_border_gradients": {
    "colors": [
      0xFFFFFFFF, // White
      0xFF0F823F, // Dark Green
      0xFFFFFFFF, // White
      0xFF0F823F, // Dark Green
      0xFFFFFFFF, // White
      0xFF4B775D,
    ],
    "stops": [
      0.0791,
      0.1848,
      0.5158,
      0.7282,
      0.9284,
      0.9919,
    ],
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "identity_inner_background_gradients": {
    "colors": [
      0xFFF37022, // Orange
      0xFFF3FFFB, // Light Blue-Green
      0xFF0F823F, // Dark Green
    ],
    "stops": [
      0.0, // 0%
      0.5191, // 51.91%
      1.0, // 100%
    ],
    "directions": {"begin_x": 1.0, "begin_y": 0.0, "end_x": -1.0, "end_y": 0.0}
  },
  "badge_banner_gradients": {
    "colors": [4278205978, 4279206463, 4279206463, 4278205978],
    "stops": [0.0, 0.2899, 0.6701, 1.0],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "footer_gradients": {
    "colors": [0x00000000, 0x00000000, 0x00000000],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0, "end_x": -1.0, "end_y": 0}
  },
  "badge_ribbon_background_gradients": {
    "colors": [2147499546, 2148500031, 2148500031, 2147499546],
    "stops": [-0.0603, 0.2808, 0.7282, 1.1163],
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": -0.0}
  },
  "background_gradients": {
    "colors": [4294144034, 4294966509, 4281703236],
    "stops": null,
    "directions": {
      "begin_x": -1.0,
      "begin_y": -1.0,
      "end_x": -1.0,
      "end_y": 1.0
    }
  }
};
final Map<String, dynamic> neutralShinyGradientsMock = {
  "identity_border_gradients": {
    "colors": [
      0xFFFFFFFF, // White
      0xFF0266B4, // Dark Blue
      0xFF2393EA, // Light Blue
      0xFFFFFFFF, // White
      0xFF0266B4, // Dark Blue
      0xFFFFFFFF, // White
      0xFF003B6A, // Very Dark Blue
    ],
    "stops": [
      -0.0531,
      0.2081,
      0.3296,
      0.4987,
      0.8824,
      1.0248,
      1.1132,
    ],
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "identity_inner_background_gradients": {
    "colors": [0xff0667FF, 0xff0667FF],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0.0, "end_x": -1.0, "end_y": 0.0}
  },
  "badge_banner_gradients": {
    "colors": [4278205290, 4278347444, 4278347444, 4278205290],
    "stops": [0.0475, 0.3633, 0.6758, 0.9852],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "upper_footer_gradients": {
    "colors": [4278205290, 4278347444, 4278347444, 4278205290],
    "stops": [0.0475, 0.3633, 0.6758, 0.9852],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "footer_gradients": {
    "colors": [0x00000000, 0x00000000, 0x00000000],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0, "end_x": -1.0, "end_y": 0}
  },
  "badge_ribbon_background_gradients": {
    "colors": [4287619839, 4278748927],
    "stops": [0.389, 1.0421],
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "background_gradients": {
    "colors": [4278215167, 4288798207],
    "stops": [0.0, 1.0],
    "directions": {"begin_x": 0.0, "begin_y": -1.0, "end_x": 0.0, "end_y": 1.0}
  }
};
final Map<String, dynamic> brsShinyGradientsMock = {
  "identity_border_gradients": {
    "colors": [
      0xFFAC0066, // Dark Pink
      0xFFFBC1E3, // Light Pink
      0xFFED008C, // Medium Pink
      0xFFFFAFDE, // Pale Pink
      0xFFED008C, // Medium Pink
      0xFFFFAFDE, // Pale Pink
      0xFFAC0066, // Dark Pink
    ],
    "stops": [
      -0.0149,
      0.1608,
      0.3309,
      0.5067,
      0.7052,
      0.8695,
      1.0094,
    ],
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "identity_inner_background_gradients": {
    "colors": [0xffF47FC5, 0xffF47FC5],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0.0, "end_x": -1.0, "end_y": 0.0}
  },
  "badge_banner_gradients": {
    "colors": [4289462374, 4293722252, 4293722252, 4289462374],
    "stops": [0.0295, 0.3889, 0.6336, 0.9826],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "footer_gradients": {
    "colors": [0x00000000, 0x00000000, 0x00000000],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0, "end_x": -1.0, "end_y": 0}
  },
  "badge_ribbon_background_gradients": {
    "colors": [4289462374, 4293722252, 4293722252, 4289462374],
    "stops": [-0.0282, 0.363, 0.6295, 1.0094],
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": -0.0}
  },
  "background_gradients": {
    "colors": [4294213573, 4293722252],
    "stops": null,
    "directions": {
      "begin_x": -1.0,
      "begin_y": 1.0,
      "end_x": -1.0,
      "end_y": -1.0
    }
  }
};

//H2 Leaders Background Gradient
final Map<String, dynamic> h2CongressGradients = {
  "colors": [4294144034, 4294966509, 4281703236],
  "directions": {"begin_x": -1.0, "begin_y": -1.0, "end_x": -1.0, "end_y": 1.0}
};
final Map<String, dynamic> h2BrsGradients = {
  "colors": [4294213573, 4293722252],
  "directions": {"begin_x": -1.0, "begin_y": 1.0, "end_x": -1.0, "end_y": -1.0}
};
final Map<String, dynamic> h2JspGradients = {
  "colors": [4291559424, 4294946992, 4291559424],
  "directions": {"begin_x": -1.0, "begin_y": -1.0, "end_x": -1.0, "end_y": 1.0}
};
final Map<String, dynamic> h2TdpGradients = {
  "colors": [4294360320, 4292026416],
  "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
};
final Map<String, dynamic> h2BjpGradients = {
  "colors": [4294951461, 4294952827, 4294210070],
  "directions": {"begin_x": -1.0, "begin_y": -1.0, "end_x": -1.0, "end_y": 1.0}
};
final Map<String, dynamic> h2YsrcpGradients = {
  "colors": [4278226502, 4280466360, 4278347444],
  "directions": {"begin_x": 1.0, "begin_y": -1.0, "end_x": 1.0, "end_y": 1.0}
};
final Map<String, dynamic> h2NeutralGradients = {
  "colors": [4278215167, 4288798207],
  "directions": {"begin_x": 0.0, "begin_y": -1.0, "end_x": 0.0, "end_y": 1.0}
};

// Plain Color Gradients
final Map<String, dynamic> whiteColorGradients = {
  "badge_banner_gradients": {
    "colors": [0xFFFFFFFF, 0xFFFFFFFF],
    "stops": null,
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "upper_footer_gradients": {
    "colors": [0xFFFFFFFF, 0xFFFFFFFF],
    "stops": null,
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "footer_gradients": {
    "colors": [0xFFFFFFFF, 0xFFFFFFFF],
    "stops": null,
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "identity_border_gradients": {
    "colors": [0xFFFFFFFF, 0xFFFFFFFF],
    "stops": null,
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "party_icon_background_gradients": {
    "colors": [0xFFFFFFFF, 0xFFFFFFFF],
    "stops": null,
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "badge_ribbon_background_gradients": {
    "colors": [0xFFFFFFFF, 0xFFFFFFFF],
    "stops": null,
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "background_gradients": {
    "colors": [0xFFFFFFFF, 0xFFFFFFFF],
    "stops": null,
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  }
};

//White Footer Color & Party Gradients
final Map<String, dynamic> ysrcpWhiteGradients = {
  "badge_banner_gradients": {
    "colors": [4278205290, 4278347444, 4278347444, 4278205290],
    "stops": [0.0, 0.3368, 0.6826, 1.0],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "identity_border_gradients": {
    "colors": [4278205290, 4278347444, 4278347444, 4278205290],
    "stops": [0.0, 0.3368, 0.6826, 1.0],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "upper_footer_gradients": {
    "colors": [0xFFFFFFFF, 0xFFFFFFFF],
    "stops": null,
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "footer_gradients": {
    "colors": [0xFFFFFFFF, 0xFFFFFFFF],
    "stops": null,
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "party_icon_background_gradients": {
    "colors": [4278226502, 4280466360, 4278347444],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0, "end_x": -1.0, "end_y": 0}
  },
  "badge_ribbon_background_gradients": {
    "colors": [2147498858, 2147641012, 2147525631, 2147641012, 2147498858],
    "stops": [0.0111, 0.3012, 0.5163, 0.7417, 0.9944],
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "background_gradients": {
    "colors": [4278226502, 4280466360, 4278347444],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": -1.0, "end_x": 1.0, "end_y": 1.0}
  }
};
final Map<String, dynamic> tdpWhiteGradients = {
  "badge_banner_gradients": {
    "colors": [4290190120, 4292026416, 4290190120],
    "stops": [0.005, 0.5, 0.995],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "identity_border_gradients": {
    "colors": [4290190120, 4292026416, 4290190120],
    "stops": [0.005, 0.5, 0.995],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "upper_footer_gradients": {
    "colors": [0xFFFFFFFF, 0xFFFFFFFF],
    "stops": null,
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "footer_gradients": {
    "colors": [0xFFFFFFFF, 0xFFFFFFFF],
    "stops": null,
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "party_icon_background_gradients": {
    "colors": [4294360320, 4292026416],
    "stops": [0.0603, 0.9463],
    "directions": {"begin_x": 1.0, "begin_y": 0.0, "end_x": -1.0, "end_y": 0.0}
  },
  "badge_ribbon_background_gradients": {
    "colors": [2164025858, 2163337504, 2164260863, 2164025858, 2163337504],
    "stops": [-0.0389, 0.235, 0.4109, 0.7924, 1.0421],
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": -0.0}
  },
  "background_gradients": {
    "colors": [4294360320, 4292026416],
    "stops": [0.0603, 0.9463],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  }
};
final Map<String, dynamic> bjpWhiteGradients = {
  "badge_banner_gradients": {
    "colors": [4289480448, 4294210070, 4294210070, 4289480448],
    "stops": [0.0, 0.3472, 0.6441, 1.0],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "identity_border_gradients": {
    "colors": [4289480448, 4294210070, 4294210070, 4289480448],
    "stops": [0.0, 0.3472, 0.6441, 1.0],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "upper_footer_gradients": {
    "colors": [0xFFFFFFFF, 0xFFFFFFFF],
    "stops": null,
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "footer_gradients": {
    "colors": [0xFFFFFFFF, 0xFFFFFFFF],
    "stops": null,
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "party_icon_background_gradients": {
    "colors": [4294951461, 4294952827, 4294210070],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0, "end_x": -1.0, "end_y": 0}
  },
  "badge_ribbon_background_gradients": {
    "colors": [2158774016, 2163503638, 2163503638, 2158774016],
    "stops": [-0.0603, 0.3482, 0.6975, 1.1163],
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": -0.0}
  },
  "background_gradients": {
    "colors": [4294951461, 4294952827, 4294210070],
    "stops": null,
    "directions": {
      "begin_x": -1.0,
      "begin_y": -1.0,
      "end_x": -1.0,
      "end_y": 1.0
    }
  }
};
final Map<String, dynamic> congressWhiteGradients = {
  "badge_banner_gradients": {
    "colors": [4278205978, 4279206463, 4279206463, 4278205978],
    "stops": [0.0, 0.2899, 0.6701, 1.0],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "identity_border_gradients": {
    "colors": [4278205978, 4279206463, 4279206463, 4278205978],
    "stops": [0.0, 0.2899, 0.6701, 1.0],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "upper_footer_gradients": {
    "colors": [0xFFFFFFFF, 0xFFFFFFFF],
    "stops": null,
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "footer_gradients": {
    "colors": [0xFFFFFFFF, 0xFFFFFFFF],
    "stops": null,
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "party_icon_background_gradients": {
    "colors": [4294144034, 4294966509, 4281703236],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0, "end_x": -1.0, "end_y": 0}
  },
  "badge_ribbon_background_gradients": {
    "colors": [2147499546, 2148500031, 2148500031, 2147499546],
    "stops": [-0.0603, 0.2808, 0.7282, 1.1163],
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": -0.0}
  },
  "background_gradients": {
    "colors": [4294144034, 4294966509, 4281703236],
    "stops": null,
    "directions": {
      "begin_x": -1.0,
      "begin_y": -1.0,
      "end_x": -1.0,
      "end_y": 1.0
    }
  }
};
final Map<String, dynamic> brsWhiteGradients = {
  "badge_banner_gradients": {
    "colors": [4289462374, 4293722252, 4293722252, 4289462374],
    "stops": [0.0295, 0.3889, 0.6336, 0.9826],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "identity_border_gradients": {
    "colors": [4289462374, 4293722252, 4293722252, 4289462374],
    "stops": [0.0295, 0.3889, 0.6336, 0.9826],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "upper_footer_gradients": {
    "colors": [0xFFFFFFFF, 0xFFFFFFFF],
    "stops": null,
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "footer_gradients": {
    "colors": [0xFFFFFFFF, 0xFFFFFFFF],
    "stops": null,
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "party_icon_background_gradients": {
    "colors": [4294213573, 4293722252],
    "stops": [0.0, 0.97],
    "directions": {"begin_x": 1.0, "begin_y": 0, "end_x": -1.0, "end_y": 0}
  },
  "badge_ribbon_background_gradients": {
    "colors": [4289462374, 4293722252, 4293722252, 4289462374],
    "stops": [-0.0282, 0.363, 0.6295, 1.0094],
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": -0.0}
  },
  "background_gradients": {
    "colors": [4294213573, 4293722252],
    "stops": null,
    "directions": {
      "begin_x": -1.0,
      "begin_y": 1.0,
      "end_x": -1.0,
      "end_y": -1.0
    }
  }
};
final Map<String, dynamic> neutralWhiteGradients = {
  "badge_banner_gradients": {
    "colors": [4278215167, 4288798207],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0, "end_x": -1.0, "end_y": 0}
  },
  "identity_border_gradients": {
    "colors": [16777215, 16777215],
    "stops": [0, 1],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "upper_footer_gradients": {
    "colors": [0xFFFFFFFF, 0xFFFFFFFF],
    "stops": null,
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "footer_gradients": {
    "colors": [0xFFFFFFFF, 0xFFFFFFFF],
    "stops": null,
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "party_icon_background_gradients": {
    "colors": [4278215167, 4288798207],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0, "end_x": -1.0, "end_y": 0}
  },
  "badge_ribbon_background_gradients": {
    "colors": [4287619839, 4278748927],
    "stops": [0.389, 1.0421],
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "background_gradients": {
    "colors": [4278215167, 4288798207],
    "stops": [0.0, 1.0],
    "directions": {"begin_x": 0.0, "begin_y": -1.0, "end_x": 0.0, "end_y": 1.0}
  }
};
final Map<String, dynamic> jspWhiteGradients = {
  "badge_banner_gradients": {
    "colors": [4283695104, 4291559424, 4291559424, 4283695104],
    "stops": [0.0, 0.3264, 0.7118, 1.0],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "identity_border_gradients": {
    "colors": [4283695104, 4291559424, 4291559424, 4283695104],
    "stops": [0.0, 0.3264, 0.7118, 1.0],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "upper_footer_gradients": {
    "colors": [0xFFFFFFFF, 0xFFFFFFFF],
    "stops": null,
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "footer_gradients": {
    "colors": [0xFFFFFFFF, 0xFFFFFFFF],
    "stops": null,
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "party_icon_background_gradients": {
    "colors": [4291559424, 4294946992, 4291559424],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0, "end_x": -1.0, "end_y": 0}
  },
  "badge_ribbon_background_gradients": {
    "colors": [2152988672, 2160852992, 2160852992, 2152988672],
    "stops": [-0.0603, 0.3237, 0.7772, 1.1163],
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": 0.0}
  },
  "background_gradients": {
    "colors": [4291559424, 4294946992, 4291559424],
    "stops": null,
    "directions": {
      "begin_x": -1.0,
      "begin_y": -1.0,
      "end_x": -1.0,
      "end_y": 1.0
    }
  }
};

//Pure Golden Frame Gradients
final Map<String, dynamic> pureGoldenFrameGradients = {
  "badge_banner_gradients": {
    "colors": [16777215, 16777215],
    "stops": [0, 1],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "footer_gradients": {
    "colors": [
      4293836359,
      4294499191,
      4294962838,
      4294961327,
      4294962838,
      4288832030,
      4293829964,
      4294499191,
      4293836359,
      4288575773
    ],
    "stops": [
      0.0,
      0.0816,
      0.1753,
      0.2586,
      0.4097,
      0.5451,
      0.6545,
      0.8264,
      0.9253,
      1.0
    ],
    "directions": {"begin_x": 1.0, "begin_y": 0.1, "end_x": -1.0, "end_y": -0.1}
  },
  "badge_ribbon_background_gradients": {
    "colors": [2164025858, 2163337504, 2164260863, 2164025858, 2163337504],
    "stops": [-0.0389, 0.235, 0.4109, 0.7924, 1.0421],
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": -0.0}
  },
  "background_gradients": {
    "colors": [
      4288379660,
      4291930136,
      4294171000,
      4294964419,
      4294967295,
      4294964419,
      4294171001,
      4291930136,
      4288379660
    ],
    "stops": [
      0.0141,
      0.1252,
      0.3134,
      0.4424,
      0.5285,
      0.6468,
      0.7705,
      0.9587,
      1.0465
    ],
    "directions": {"begin_x": 1.0, "begin_y": 0.1, "end_x": -1.0, "end_y": -0.1}
  },
  "inner_background_gradients": {
    "colors": [
      4294171000,
      4292714264,
      4294964419,
      4294964419,
      4294171001,
      4292714264
    ],
    "stops": [0.0909, 0.1889, 0.4163, 0.5715, 0.7349, 0.8752],
    "directions": {"begin_x": -1.0, "begin_y": 0.1, "end_x": 1.0, "end_y": -0.1}
  }
};

//Primary and Secondary Colors for All Parties
final Map<String, dynamic> ysrcpShinyColors = {
  "primary_color": 0xff51D5FF,
  "secondary_color": 0xff008E46,
};
final Map<String, dynamic> tdpShinyColors = {
  "primary_color": 0xffFCF5DE,
  "secondary_color": 0xffCC0000,
};
final Map<String, dynamic> jspShinyColors = {
  "primary_color": 0xffFF9898,
  "secondary_color": 0xffF6A8A8,
};
final Map<String, dynamic> bjpShinyColors = {
  "primary_color": 0xffFFEFAA,
  "secondary_color": 0xffFDC74D,
};
final Map<String, dynamic> congressShinyColors = {
  "primary_color": 0xffFFEBA7,
  "secondary_color": 0xff79FEB9,
};
final Map<String, dynamic> neutralShinyColors = {
  "primary_color": 0xff40C0EB,
  "secondary_color": 0xff40C0EB,
};
final Map<String, dynamic> brsShinyColors = {
  "primary_color": 0xffFBD1EA,
  "secondary_color": 0xffDF0084,
};

// Primary and Secondary Colors for Trapezoidal Identities
final Map<String, dynamic> ysrcpTrapezoidalColors = {
  "primary_color": 0xff0266B4,
  "secondary_color": 0xff0266B4,
};
final Map<String, dynamic> tdpTrapezoidalColors = {
  "primary_color": 0xFFF6BD00,
  "secondary_color": 0xFFD32030,
};
final Map<String, dynamic> jspTrapezoidalColors = {
  "primary_color": 0xffCC0000,
  "secondary_color": 0xffCC0000,
};
final Map<String, dynamic> bjpTrapezoidalColors = {
  "primary_color": 0xffF47216,
  "secondary_color": 0xffF47216,
};
final Map<String, dynamic> congressTrapezoidalColors = {
  "primary_color": 0xff359B44,
  "secondary_color": 0xff359B44,
};
final Map<String, dynamic> neutralTrapezoidalColors = {
  "primary_color": 0xffA1DDFF,
  "secondary_color": 0xffA1DDFF,
};
final Map<String, dynamic> brsTrapezoidalColors = {
  "primary_color": 0xffF47FC5,
  "secondary_color": 0xffF47FC5,
};

//Transparent Gradients
final Map<String, dynamic> transparentGradients = {
  "footer_gradients": {
    "colors": [0x00000000, 0x00000000],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0.0, "end_x": -1.0, "end_y": 0.0}
  },
  "upper_footer_gradients": {
    "colors": [0x00000000, 0x00000000],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0.0, "end_x": -1.0, "end_y": 0.0}
  },
  "identity_border_gradients": {
    "colors": [4278205290, 4278347444, 4278347444, 4278205290],
    "stops": [0.0, 0.3368, 0.6826, 1.0],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "badge_ribbon_background_gradients": {
    "colors": [0x00000000, 0x00000000, 0x00000000],
    "stops": null,
    "directions": {"begin_x": -1.0, "begin_y": 0.0, "end_x": 1.0, "end_y": -0.0}
  },
  "background_gradients": {
    "colors": [
      4288379660,
      4291930136,
      4294171000,
      4294964419,
      4294967295,
      4294964419,
      4294171001,
      4291930136,
      4288379660
    ],
    "stops": [
      0.0141,
      0.1252,
      0.3134,
      0.4424,
      0.5285,
      0.6468,
      0.7705,
      0.9587,
      1.0465
    ],
    "directions": {"begin_x": 1.0, "begin_y": 0.1, "end_x": -1.0, "end_y": -0.1}
  },
  "badge_banner_gradients": {
    "colors": [16777215, 16777215],
    "stops": [0, 1],
    "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
  },
  "party_icon_background_gradients": {
    "colors": [4278226502, 4280466360, 4278347444],
    "stops": null,
    "directions": {"begin_x": 1.0, "begin_y": 0, "end_x": -1.0, "end_y": 0}
  },
};
