import 'package:praja/features/posters/models/poster_carousel.dart';

const Map<String, dynamic> posterCarouselFreeShareMocks = {
  "feed_item_id": "poster-carousel-free-share-mocks",
  "feed_type": "poster_carousel",
  "title": "చత్రపతి వర్ధంతి ",
  "more_cta": "మరిన్ని డిజైన్లు",
  "more_deeplink": "/posters/layout?id=841",
  "items": [
    {
      "event_date": "ఏప్రిల్ 9",
      "creative": {
        "v2_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/e2b47967-a6ae-40d4-82b3-a74d3708c6ad.jpg",
        "v1_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/488e6353-64f2-4f2f-9f24-92141bae6754.jpg",
        "paid": false,
        "h1_background_type": "light",
        "h2_background_type": "sticker",
        "leader_photo_ring_color": 4294967295,
        "h2_background_gradients": {
          "colors": [4294360320, 4292026416],
          "directions": {
            "begin_x": -1.0,
            "begin_y": 0,
            "end_x": 1.0,
            "end_y": 0
          }
        },
        "is_locked": false,
        "analytics_params": {
          "h1_background_type": "light",
          "h2_background_type": "sticker",
          "photo_v2_supported": true,
          "photo_v3_supported": true,
          "paid": false,
          "category_id": 841,
          "category_name": "చత్రపతి వర్ధంతి "
        }
      },
      "layout": {
        "layout_type": "basic",
        "golden_frame": false,
        "show_praja_logo": true,
        "shadow_color": null,
        "v1": null,
        "share_text":
            "మీరు కూడా ఈ విధంగా *మీ ఫోటో మరియు మీ పేరుతో* ఉన్న  పోస్టర్ ని ఇప్పుడు *Praja App* లో ఉచితంగా పొందవచ్చు.\nతెలుగు రాష్ట్రాలలో *లక్షలాది* మంది రోజు వాడుతున్న సరికొత్త *పొలిటికల్ సోషల్ మీడియా* యాప్. ఇప్పుడే *డౌన్లోడ్* చేసుకోని నాయకుల తో *కనెక్ట్* అవ్వచ్చు అలాగే మీ *స్థానిక విషయాలు* తెలుసుకోవచ్చు.👇👇\nhttps://prajaapp.sng.link/A3x5b/5oqr?paffid=1201101",
        "gradients": {
          "background_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "badge_banner_gradients": {
            "colors": [16777215, 16777215],
            "stops": [0, 1],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "badge_ribbon_background_gradients": {
            "colors": [
              2164025858,
              2163337504,
              2164260863,
              2164025858,
              2163337504
            ],
            "stops": [-0.0389, 0.235, 0.4109, 0.7924, 1.0421],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0.0,
              "end_x": 1.0,
              "end_y": -0.0
            }
          },
          "footer_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": 1.0,
              "begin_y": 0.0,
              "end_x": -1.0,
              "end_y": 0.0
            }
          },
          "identity_border_gradients": {
            "colors": [
              4288379660,
              4291930136,
              4294171000,
              4294964419,
              4294967295,
              4294964419,
              4294171001,
              4291930136,
              4288379660
            ],
            "stops": [
              0.0141,
              0.1252,
              0.3134,
              0.4424,
              0.5285,
              0.6468,
              0.7705,
              0.9587,
              1.0465
            ],
            "directions": {
              "begin_x": 0.0,
              "begin_y": -1.0,
              "end_x": 0.0,
              "end_y": 1.0
            }
          },
          "identity_inner_background_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "inner_background_gradients": null,
          "party_icon_background_gradients": null,
          "h2_background_gradients": {
            "colors": [4294360320, 4292026416],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "upper_footer_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": 1.0,
              "begin_y": 0.0,
              "end_x": -1.0,
              "end_y": 0.0
            }
          }
        },
        "text_color": 4294967295,
        "badge_text_color": 4294967295,
        "identity": {
          "user": {
            "id": 1201101,
            "name": "kakinada youth",
            "photo_url": "",
            "badge": null
          },
          "type": "flat_user"
        },
        "party_icon": null,
        "is_locked": false,
        "header_2_photos": [
          {
            "radius": 42.727999999999994,
            "position_x": 369.65,
            "position_y": 29.68,
            "photo_url":
                "https://a-cdn.thecircleapp.in/production/photos/41/9ef78851f86d926d2c1fc71cc00e20a0.png"
          },
          {
            "radius": 42.727999999999994,
            "position_x": 486.65,
            "position_y": 29.68,
            "photo_url":
                "https://a-cdn.thecircleapp.in/production/photos/41/8c9e88a70232378a16f035fe314088f1.png"
          }
        ],
        "is_bordered_layout": false,
        "fonts_config": {
          "name": {
            "font_family": "Noto Sans Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          },
          "badge": {
            "font_family": "Noto Sans Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          }
        },
        "analytics_params": {
          "layout_type": "basic",
          "is_locked": false,
          "golden_frame": false,
          "header_2_count": 2,
          "identity_type": "flat_user",
          "is_circle_sponsored_frame": false,
          "is_circle_sponsored_frame_locked": false,
          "user_posters_subscription_status": "free"
        }
      },
      "params": {
        "creative_id": 3928,
        "id": 841,
      }
    },
    {
      "creative": {
        "v2_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/e2b47967-a6ae-40d4-82b3-a74d3708c6ad.jpg",
        "v1_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/488e6353-64f2-4f2f-9f24-92141bae6754.jpg",
        "paid": false,
        "h1_background_type": "light",
        "h2_background_type": "sticker",
        "leader_photo_ring_color": 4294967295,
        "h2_background_gradients": {
          "colors": [4294360320, 4292026416],
          "directions": {
            "begin_x": -1.0,
            "begin_y": 0,
            "end_x": 1.0,
            "end_y": 0
          }
        },
        "is_locked": false,
        "analytics_params": {
          "h1_background_type": "light",
          "h2_background_type": "sticker",
          "photo_v2_supported": true,
          "photo_v3_supported": true,
          "paid": false,
          "category_id": 841,
          "category_name": "చత్రపతి వర్ధంతి "
        }
      },
      "layout": {
        "layout_type": "basic",
        "golden_frame": false,
        "show_praja_logo": true,
        "shadow_color": null,
        "v1": null,
        "share_text":
            "మీరు కూడా ఈ విధంగా *మీ ఫోటో మరియు మీ పేరుతో* ఉన్న  పోస్టర్ ని ఇప్పుడు *Praja App* లో ఉచితంగా పొందవచ్చు.\nతెలుగు రాష్ట్రాలలో *లక్షలాది* మంది రోజు వాడుతున్న సరికొత్త *పొలిటికల్ సోషల్ మీడియా* యాప్. ఇప్పుడే *డౌన్లోడ్* చేసుకోని నాయకుల తో *కనెక్ట్* అవ్వచ్చు అలాగే మీ *స్థానిక విషయాలు* తెలుసుకోవచ్చు.👇👇\nhttps://prajaapp.sng.link/A3x5b/5oqr?paffid=1201101",
        "gradients": {
          "background_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "badge_banner_gradients": {
            "colors": [16777215, 16777215],
            "stops": [0, 1],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "badge_ribbon_background_gradients": {
            "colors": [
              2164025858,
              2163337504,
              2164260863,
              2164025858,
              2163337504
            ],
            "stops": [-0.0389, 0.235, 0.4109, 0.7924, 1.0421],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0.0,
              "end_x": 1.0,
              "end_y": -0.0
            }
          },
          "footer_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": 1.0,
              "begin_y": 0.0,
              "end_x": -1.0,
              "end_y": 0.0
            }
          },
          "identity_border_gradients": {
            "colors": [
              4288379660,
              4291930136,
              4294171000,
              4294964419,
              4294967295,
              4294964419,
              4294171001,
              4291930136,
              4288379660
            ],
            "stops": [
              0.0141,
              0.1252,
              0.3134,
              0.4424,
              0.5285,
              0.6468,
              0.7705,
              0.9587,
              1.0465
            ],
            "directions": {
              "begin_x": 0.0,
              "begin_y": -1.0,
              "end_x": 0.0,
              "end_y": 1.0
            }
          },
          "identity_inner_background_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "inner_background_gradients": null,
          "party_icon_background_gradients": null,
          "h2_background_gradients": {
            "colors": [4294360320, 4292026416],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "upper_footer_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": 1.0,
              "begin_y": 0.0,
              "end_x": -1.0,
              "end_y": 0.0
            }
          }
        },
        "text_color": 4294967295,
        "badge_text_color": 4294967295,
        "identity": {
          "user": {
            "id": 1201101,
            "name": "kakinada youth",
            "photo_url": "",
            "badge": null
          },
          "type": "flat_user"
        },
        "party_icon": null,
        "is_locked": false,
        "header_2_photos": [
          {
            "radius": 42.727999999999994,
            "position_x": 369.65,
            "position_y": 29.68,
            "photo_url":
                "https://a-cdn.thecircleapp.in/production/photos/41/9ef78851f86d926d2c1fc71cc00e20a0.png"
          },
          {
            "radius": 42.727999999999994,
            "position_x": 486.65,
            "position_y": 29.68,
            "photo_url":
                "https://a-cdn.thecircleapp.in/production/photos/41/8c9e88a70232378a16f035fe314088f1.png"
          }
        ],
        "is_bordered_layout": false,
        "fonts_config": {
          "name": {
            "font_family": "Noto Sans Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          },
          "badge": {
            "font_family": "Noto Sans Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          }
        },
        "analytics_params": {
          "layout_type": "basic",
          "is_locked": false,
          "golden_frame": false,
          "header_2_count": 2,
          "identity_type": "flat_user",
          "is_circle_sponsored_frame": false,
          "is_circle_sponsored_frame_locked": false,
          "user_posters_subscription_status": "free"
        }
      },
      "params": {
        "creative_id": 3928,
        "id": 841,
      }
    }
  ],
};
const Map<String, dynamic> posterCarouselSposoredShareMocks = {
  "feed_item_id": "poster-carousel-sponsored-share-mocks",
  "feed_type": "poster_carousel",
  "title": "చత్రపతి వర్ధంతి ",
  "more_cta": "మరిన్ని డిజైన్లు",
  "more_deeplink": "/posters/layout?id=841",
  "items": [
    {
      "creative": {
        "v2_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/979114db-94f0-4f76-80a3-f3533c126afd.jpg",
        "v1_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/3a1719ca-5567-4b63-bbcd-7705743cdcf9.jpg",
        "paid": false,
        "h1_background_type": "light",
        "h2_background_type": "sticker",
        "leader_photo_ring_color": 4294967295,
        "h2_background_gradients": {
          "colors": [4294360320, 4292026416],
          "directions": {
            "begin_x": -1.0,
            "begin_y": 0,
            "end_x": 1.0,
            "end_y": 0
          }
        },
        "is_locked": false,
        "analytics_params": {
          "h1_background_type": "light",
          "h2_background_type": "sticker",
          "photo_v2_supported": true,
          "photo_v3_supported": true,
          "paid": false,
          "category_id": 841,
          "category_name": "చత్రపతి వర్ధంతి "
        }
      },
      "layout": {
        "layout_type": "basic",
        "golden_frame": false,
        "show_praja_logo": false,
        "shadow_color": null,
        "v1": null,
        "share_text":
            "తెలుగు దేశం పార్టీ మీ కోసం *స్పెషల్ పోస్టర్‌* స్పాన్సర్ చేసింది.\n\nవెంటనే పోస్టర్ పొందండి.\nhttps://prajaapp.sng.link/A3x5b/5oqr/r_b847ce06f6",
        "gradients": {
          "background_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {"begin_x": -1, "begin_y": 0, "end_x": 1, "end_y": 0}
          },
          "badge_banner_gradients": {
            "colors": [16777215, 16777215],
            "stops": [0, 1],
            "directions": {"begin_x": -1, "begin_y": 0, "end_x": 1, "end_y": 0}
          },
          "badge_ribbon_background_gradients": {
            "colors": [
              2164025858,
              2163337504,
              2164260863,
              2164025858,
              2163337504
            ],
            "stops": [-0.0389, 0.235, 0.4109, 0.7924, 1.0421],
            "directions": {"begin_x": -1, "begin_y": 0, "end_x": 1, "end_y": 0}
          },
          "footer_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {"begin_x": 1, "begin_y": 0, "end_x": -1, "end_y": 0}
          },
          "identity_border_gradients": {
            "colors": [
              4288379660,
              4291930136,
              4294171000,
              4294964419,
              4294967295,
              4294964419,
              4294171001,
              4291930136,
              4288379660
            ],
            "stops": [
              0.0141,
              0.1252,
              0.3134,
              0.4424,
              0.5285,
              0.6468,
              0.7705,
              0.9587,
              1.0465
            ],
            "directions": {"begin_x": 0, "begin_y": -1, "end_x": 0, "end_y": 1}
          },
          "identity_inner_background_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {"begin_x": -1, "begin_y": 0, "end_x": 1, "end_y": 0}
          },
          "inner_background_gradients": null,
          "party_icon_background_gradients": null,
          "h2_background_gradients": {
            "colors": [4294360320, 4292026416],
            "directions": {"begin_x": -1, "begin_y": 0, "end_x": 1, "end_y": 0}
          },
          "upper_footer_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {"begin_x": 1, "begin_y": 0, "end_x": -1, "end_y": 0}
          }
        },
        "text_color": 4294967295,
        "badge_text_color": 4294967295,
        "identity": {
          "user": {
            "id": 1061424,
            "name": "మణిదీప్ పోలిరెడ్డి",
            "photo_url":
                "https://a-cdn.thecircleapp.in/production/photos/1061424/a35bed3520b329105312ac715c4a3ea5.jpg",
            "badge": null
          },
          "type": "flat_user",
          "is_user_position_back": false,
          "show_badge_ribbon": true
        },
        "party_icon": null,
        "id": 10200,
        "is_bordered_layout": false,
        "is_locked": false,
        "frame_type": "basic",
        "fonts_config": {
          "name": {
            "font_family": "Noto Sans Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          },
          "badge": {
            "font_family": "Noto Sans Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          }
        },
        "sponsorship": {
          "icon_url":
              "https://a-cdn.thecircleapp.in/production/photos/41/1e460beb5867ac8e23d4a654360e13ed.jpg",
          "line_1": "స్పాన్సర్",
          "line_2": "తెలుగు దేశం పార్టీ",
          "line_2_text_color": 4294967295,
          "gradients": {
            "colors": [4290190120, 4292026416, 4290190120],
            "stops": [0.005, 0.5, 0.995],
            "directions": {"begin_x": -1, "begin_y": 0, "end_x": 1, "end_y": 0}
          }
        },
        "header_1_photos": [
          {
            "radius": 60,
            "position_x": 82,
            "position_y": 25.6,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/2f71c3af-665c-4935-adac-8e89409fdb78.png"
          },
          {
            "radius": 60,
            "position_x": 240,
            "position_y": 25.6,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/08ed3e17-b1bd-41ae-a9e3-c5c43df3f939.png"
          },
          {
            "radius": 60,
            "position_x": 398,
            "position_y": 25.6,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/b7d47b06-9e8f-47f1-8248-6224d2dc3dac.png"
          }
        ],
        "header_2_photos": [],
        "analytics_params": {
          "layout_type": "basic",
          "is_locked": false,
          "golden_frame": false,
          "header_1_count": 3,
          "header_2_count": 0,
          "identity_type": "flat_user",
          "is_user_position_back": false,
          "circle_id": 31402,
          "frame_type": "basic",
          "is_circle_sponsored_frame": true,
          "is_circle_sponsored_frame_locked": false,
          "user_posters_subscription_status": "free"
        }
      },
      "params": {
        "creative_id": 3928,
        "id": 841,
      }
    },
    {
      "creative": {
        "v2_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/e2b47967-a6ae-40d4-82b3-a74d3708c6ad.jpg",
        "v1_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/488e6353-64f2-4f2f-9f24-92141bae6754.jpg",
        "paid": false,
        "h1_background_type": "light",
        "h2_background_type": "sticker",
        "leader_photo_ring_color": 4294967295,
        "h2_background_gradients": {
          "colors": [4294360320, 4292026416],
          "directions": {
            "begin_x": -1.0,
            "begin_y": 0,
            "end_x": 1.0,
            "end_y": 0
          }
        },
        "is_locked": false,
        "analytics_params": {
          "h1_background_type": "light",
          "h2_background_type": "sticker",
          "photo_v2_supported": true,
          "photo_v3_supported": true,
          "paid": false,
          "category_id": 841,
          "category_name": "చత్రపతి వర్ధంతి "
        }
      },
      "layout": {
        "layout_type": "basic",
        "golden_frame": false,
        "show_praja_logo": true,
        "shadow_color": null,
        "v1": null,
        "share_text":
            "మీరు కూడా ఈ విధంగా *మీ ఫోటో మరియు మీ పేరుతో* ఉన్న  పోస్టర్ ని ఇప్పుడు *Praja App* లో ఉచితంగా పొందవచ్చు.\nతెలుగు రాష్ట్రాలలో *లక్షలాది* మంది రోజు వాడుతున్న సరికొత్త *పొలిటికల్ సోషల్ మీడియా* యాప్. ఇప్పుడే *డౌన్లోడ్* చేసుకోని నాయకుల తో *కనెక్ట్* అవ్వచ్చు అలాగే మీ *స్థానిక విషయాలు* తెలుసుకోవచ్చు.👇👇\nhttps://prajaapp.sng.link/A3x5b/5oqr?paffid=1201101",
        "gradients": {
          "background_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "badge_banner_gradients": {
            "colors": [16777215, 16777215],
            "stops": [0, 1],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "badge_ribbon_background_gradients": {
            "colors": [
              2164025858,
              2163337504,
              2164260863,
              2164025858,
              2163337504
            ],
            "stops": [-0.0389, 0.235, 0.4109, 0.7924, 1.0421],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0.0,
              "end_x": 1.0,
              "end_y": -0.0
            }
          },
          "footer_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": 1.0,
              "begin_y": 0.0,
              "end_x": -1.0,
              "end_y": 0.0
            }
          },
          "identity_border_gradients": {
            "colors": [
              4288379660,
              4291930136,
              4294171000,
              4294964419,
              4294967295,
              4294964419,
              4294171001,
              4291930136,
              4288379660
            ],
            "stops": [
              0.0141,
              0.1252,
              0.3134,
              0.4424,
              0.5285,
              0.6468,
              0.7705,
              0.9587,
              1.0465
            ],
            "directions": {
              "begin_x": 0.0,
              "begin_y": -1.0,
              "end_x": 0.0,
              "end_y": 1.0
            }
          },
          "identity_inner_background_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "inner_background_gradients": null,
          "party_icon_background_gradients": null,
          "h2_background_gradients": {
            "colors": [4294360320, 4292026416],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "upper_footer_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": 1.0,
              "begin_y": 0.0,
              "end_x": -1.0,
              "end_y": 0.0
            }
          }
        },
        "text_color": 4294967295,
        "badge_text_color": 4294967295,
        "identity": {
          "user": {
            "id": 1201101,
            "name": "kakinada youth",
            "photo_url": "",
            "badge": null
          },
          "type": "flat_user"
        },
        "party_icon": null,
        "is_locked": false,
        "header_2_photos": [
          {
            "radius": 42.727999999999994,
            "position_x": 369.65,
            "position_y": 29.68,
            "photo_url":
                "https://a-cdn.thecircleapp.in/production/photos/41/9ef78851f86d926d2c1fc71cc00e20a0.png"
          },
          {
            "radius": 42.727999999999994,
            "position_x": 486.65,
            "position_y": 29.68,
            "photo_url":
                "https://a-cdn.thecircleapp.in/production/photos/41/8c9e88a70232378a16f035fe314088f1.png"
          }
        ],
        "is_bordered_layout": false,
        "fonts_config": {
          "name": {
            "font_family": "Noto Sans Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          },
          "badge": {
            "font_family": "Noto Sans Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          }
        },
        "analytics_params": {
          "layout_type": "basic",
          "is_locked": false,
          "golden_frame": false,
          "header_2_count": 2,
          "identity_type": "flat_user",
          "is_circle_sponsored_frame": false,
          "is_circle_sponsored_frame_locked": false,
          "user_posters_subscription_status": "free"
        }
      },
      "params": {
        "creative_id": 3928,
        "id": 841,
      }
    },
  ],
};

const Map<String, dynamic> posterCarouselPremiumPitchMock = {
  "feed_item_id": "premium-pitch-poster-carousel-mock",
  "feed_type": "poster_carousel",
  "title": "చత్రపతి వర్ధంతి",
  "more_cta": "మరిన్ని డిజైన్లు",
  "more_deeplink": "/posters/layout?id=841",
  "items": [
    {
      "creative": {
        "v2_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/979114db-94f0-4f76-80a3-f3533c126afd.jpg",
        "v1_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/3a1719ca-5567-4b63-bbcd-7705743cdcf9.jpg",
        "paid": false,
        "h1_background_type": "light",
        "h2_background_type": "sticker",
        "leader_photo_ring_color": 4294967295,
        "h2_background_gradients": {
          "colors": [4294360320, 4292026416],
          "directions": {
            "begin_x": -1.0,
            "begin_y": 0,
            "end_x": 1.0,
            "end_y": 0
          }
        },
        "is_locked": false,
        "analytics_params": {
          "h1_background_type": "light",
          "h2_background_type": "sticker",
          "photo_v2_supported": true,
          "photo_v3_supported": true,
          "paid": false,
          "category_id": 841,
          "category_name": "చత్రపతి వర్ధంతి "
        }
      },
      "layout": {
        "layout_type": "premium",
        "golden_frame": false,
        "show_praja_logo": false,
        "shadow_color": null,
        "v1": null,
        "share_text": " ",
        "gradients": {
          "background_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "badge_banner_gradients": {
            "colors": [16777215, 16777215],
            "stops": [0, 1],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "badge_ribbon_background_gradients": {
            "colors": [
              2164025858,
              2163337504,
              2164260863,
              2164025858,
              2163337504
            ],
            "stops": [-0.0389, 0.235, 0.4109, 0.7924, 1.0421],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0.0,
              "end_x": 1.0,
              "end_y": -0.0
            }
          },
          "footer_gradients": {
            "colors": [4294967295, 4294967295],
            "stops": null,
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "identity_border_gradients": {
            "colors": [
              4288379660,
              4291930136,
              4294171000,
              4294964419,
              4294967295,
              4294964419,
              4294171001,
              4291930136,
              4288379660
            ],
            "stops": [
              0.0141,
              0.1252,
              0.3134,
              0.4424,
              0.5285,
              0.6468,
              0.7705,
              0.9587,
              1.0465
            ],
            "directions": {
              "begin_x": 0.0,
              "begin_y": -1.0,
              "end_x": 0.0,
              "end_y": 1.0
            }
          },
          "identity_inner_background_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "inner_background_gradients": null,
          "party_icon_background_gradients": null,
          "h2_background_gradients": {
            "colors": [4294360320, 4292026416],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "upper_footer_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": 1.0,
              "begin_y": 0.0,
              "end_x": -1.0,
              "end_y": 0.0
            }
          }
        },
        "text_color": 4278190080,
        "badge_text_color": 4278190080,
        "premium_pitch": {
          "title": "ప్రీమియం పోస్టర్స్ పొందండి",
          "description":
              "మీ ప్రియతమ నాయకుల ఫొటోలతో అంతులేని ప్రీమియం డిజైన్లు. *₹250/నెల* నుండి ప్రారంభం",
          "cta_text": "15 రోజులు ఉచితంగా పొందండి",
          "submit_url": "/save-premium-lead"
        },
        "identity": {
          "user": {
            "id": 1201101,
            "name": "kakinada youth",
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/19/2c73ba8a-f200-466a-a06a-01e6077c653a.png",
            "badge": {
              "id": 1,
              "active": true,
              "badge_text": "మీ హోదా",
              "description": "మీ హోదా",
              "badge_banner": "SILVER"
            }
          },
          "type": "trapezoidal_identity",
          "is_user_position_back": true,
          "show_badge_ribbon": false,
          "party_highlight_color_primary": 4294360320,
          "party_highlight_color_secondary": 4292026416,
          "party_icon_url":
              "https://a-cdn.thecircleapp.in/production/admin-media/15/8fc7644b3306f7457adfab2c13ee877e.png"
        },
        "header_1_photos": [
          {
            "radius": 75,
            "position_x": 32,
            "position_y": 32,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/19/ea1a529b-f33f-4d2b-b505-9673e4692075.png"
          },
          {
            "radius": 75,
            "position_x": 448,
            "position_y": 32,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/19/ea1a529b-f33f-4d2b-b505-9673e4692075.png"
          }
        ],
        "header_2_photos": [
          {
            "radius": 40.18,
            "position_x": 247.24,
            "position_y": 48.83,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/19/ea1a529b-f33f-4d2b-b505-9673e4692075.png"
          },
          {
            "radius": 40.18,
            "position_x": 339.24,
            "position_y": 48.83,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/19/ea1a529b-f33f-4d2b-b505-9673e4692075.png"
          },
          {
            "radius": 40.18,
            "position_x": 211.24,
            "position_y": 131.83,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/19/ea1a529b-f33f-4d2b-b505-9673e4692075.png"
          },
          {
            "radius": 40.18,
            "position_x": 303.24,
            "position_y": 131.83,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/19/ea1a529b-f33f-4d2b-b505-9673e4692075.png"
          }
        ],
        "is_locked": true,
        "is_bordered_layout": false,
        "neutral_frame": false,
        "enable_outer_frame": false,
        "frame_type": "premium",
        "fonts_config": {
          "name": {
            "font_family": "Anek Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          },
          "badge": {
            "font_family": "Noto Sans Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          }
        },
        "analytics_params": {
          "layout_type": "premium",
          "is_locked": true,
          "golden_frame": false,
          "header_1_count": 2,
          "header_2_count": 4,
          "identity_type": "trapezoidal_identity",
          "is_user_position_back": true,
          "party_icon_position": null,
          "frame_type": "premium",
          "is_circle_sponsored_frame": false,
          "is_premium_pitch": true,
          "premium_pitch_view_count": 67
        }
      },
      "params": {
        "creative_id": 3928,
        "id": 841,
      }
    },
    {
      "creative": {
        "v2_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/979114db-94f0-4f76-80a3-f3533c126afd.jpg",
        "v1_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/3a1719ca-5567-4b63-bbcd-7705743cdcf9.jpg",
        "paid": false,
        "h1_background_type": "light",
        "h2_background_type": "sticker",
        "leader_photo_ring_color": 4294967295,
        "h2_background_gradients": {
          "colors": [4294360320, 4292026416],
          "directions": {
            "begin_x": -1.0,
            "begin_y": 0,
            "end_x": 1.0,
            "end_y": 0
          }
        },
        "is_locked": false,
        "analytics_params": {
          "h1_background_type": "light",
          "h2_background_type": "sticker",
          "photo_v2_supported": true,
          "photo_v3_supported": true,
          "paid": false,
          "category_id": 841,
          "category_name": "చత్రపతి వర్ధంతి "
        }
      },
      "layout": {
        "layout_type": "basic",
        "golden_frame": false,
        "show_praja_logo": true,
        "shadow_color": null,
        "v1": null,
        "share_text":
            "మీరు కూడా ఈ విధంగా *మీ ఫోటో మరియు మీ పేరుతో* ఉన్న  పోస్టర్ ని ఇప్పుడు *Praja App* లో ఉచితంగా పొందవచ్చు.\nతెలుగు రాష్ట్రాలలో *లక్షలాది* మంది రోజు వాడుతున్న సరికొత్త *పొలిటికల్ సోషల్ మీడియా* యాప్. ఇప్పుడే *డౌన్లోడ్* చేసుకోని నాయకుల తో *కనెక్ట్* అవ్వచ్చు అలాగే మీ *స్థానిక విషయాలు* తెలుసుకోవచ్చు.👇👇\nhttps://prajaapp.sng.link/A3x5b/5oqr?paffid=1201101",
        "gradients": {
          "background_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "badge_banner_gradients": {
            "colors": [16777215, 16777215],
            "stops": [0, 1],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "badge_ribbon_background_gradients": {
            "colors": [
              2164025858,
              2163337504,
              2164260863,
              2164025858,
              2163337504
            ],
            "stops": [-0.0389, 0.235, 0.4109, 0.7924, 1.0421],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0.0,
              "end_x": 1.0,
              "end_y": -0.0
            }
          },
          "footer_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": 1.0,
              "begin_y": 0.0,
              "end_x": -1.0,
              "end_y": 0.0
            }
          },
          "identity_border_gradients": {
            "colors": [
              4288379660,
              4291930136,
              4294171000,
              4294964419,
              4294967295,
              4294964419,
              4294171001,
              4291930136,
              4288379660
            ],
            "stops": [
              0.0141,
              0.1252,
              0.3134,
              0.4424,
              0.5285,
              0.6468,
              0.7705,
              0.9587,
              1.0465
            ],
            "directions": {
              "begin_x": 0.0,
              "begin_y": -1.0,
              "end_x": 0.0,
              "end_y": 1.0
            }
          },
          "identity_inner_background_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "inner_background_gradients": null,
          "party_icon_background_gradients": null,
          "h2_background_gradients": {
            "colors": [4294360320, 4292026416],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "upper_footer_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": 1.0,
              "begin_y": 0.0,
              "end_x": -1.0,
              "end_y": 0.0
            }
          }
        },
        "text_color": 4294967295,
        "badge_text_color": 4294967295,
        "identity": {
          "user": {
            "id": 1201101,
            "name": "kakinada youth",
            "photo_url": "",
            "badge": null
          },
          "type": "flat_user"
        },
        "party_icon": null,
        "is_locked": false,
        "header_2_photos": [
          {
            "radius": 42.727999999999994,
            "position_x": 369.65,
            "position_y": 29.68,
            "photo_url":
                "https://a-cdn.thecircleapp.in/production/photos/41/9ef78851f86d926d2c1fc71cc00e20a0.png"
          },
          {
            "radius": 42.727999999999994,
            "position_x": 486.65,
            "position_y": 29.68,
            "photo_url":
                "https://a-cdn.thecircleapp.in/production/photos/41/8c9e88a70232378a16f035fe314088f1.png"
          }
        ],
        "is_bordered_layout": false,
        "fonts_config": {
          "name": {
            "font_family": "Noto Sans Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          },
          "badge": {
            "font_family": "Noto Sans Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          }
        },
        "analytics_params": {
          "layout_type": "basic",
          "is_locked": false,
          "golden_frame": false,
          "header_2_count": 2,
          "identity_type": "flat_user",
          "is_circle_sponsored_frame": false,
          "is_circle_sponsored_frame_locked": false,
          "user_posters_subscription_status": "free"
        }
      },
      "params": {
        "creative_id": 3928,
        "id": 841,
      }
    },
    {
      "creative": {
        "v2_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/e2b47967-a6ae-40d4-82b3-a74d3708c6ad.jpg",
        "v1_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/488e6353-64f2-4f2f-9f24-92141bae6754.jpg",
        "paid": false,
        "h1_background_type": "light",
        "h2_background_type": "sticker",
        "leader_photo_ring_color": 4294967295,
        "h2_background_gradients": {
          "colors": [4294360320, 4292026416],
          "directions": {
            "begin_x": -1.0,
            "begin_y": 0,
            "end_x": 1.0,
            "end_y": 0
          }
        },
        "is_locked": false,
        "analytics_params": {
          "h1_background_type": "light",
          "h2_background_type": "sticker",
          "photo_v2_supported": true,
          "photo_v3_supported": true,
          "paid": false,
          "category_id": 841,
          "category_name": "చత్రపతి వర్ధంతి "
        }
      },
      "layout": {
        "layout_type": "basic",
        "golden_frame": false,
        "show_praja_logo": true,
        "shadow_color": null,
        "v1": null,
        "share_text":
            "మీరు కూడా ఈ విధంగా *మీ ఫోటో మరియు మీ పేరుతో* ఉన్న  పోస్టర్ ని ఇప్పుడు *Praja App* లో ఉచితంగా పొందవచ్చు.\nతెలుగు రాష్ట్రాలలో *లక్షలాది* మంది రోజు వాడుతున్న సరికొత్త *పొలిటికల్ సోషల్ మీడియా* యాప్. ఇప్పుడే *డౌన్లోడ్* చేసుకోని నాయకుల తో *కనెక్ట్* అవ్వచ్చు అలాగే మీ *స్థానిక విషయాలు* తెలుసుకోవచ్చు.👇👇\nhttps://prajaapp.sng.link/A3x5b/5oqr?paffid=1201101",
        "gradients": {
          "background_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "badge_banner_gradients": {
            "colors": [16777215, 16777215],
            "stops": [0, 1],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "badge_ribbon_background_gradients": {
            "colors": [
              2164025858,
              2163337504,
              2164260863,
              2164025858,
              2163337504
            ],
            "stops": [-0.0389, 0.235, 0.4109, 0.7924, 1.0421],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0.0,
              "end_x": 1.0,
              "end_y": -0.0
            }
          },
          "footer_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": 1.0,
              "begin_y": 0.0,
              "end_x": -1.0,
              "end_y": 0.0
            }
          },
          "identity_border_gradients": {
            "colors": [
              4288379660,
              4291930136,
              4294171000,
              4294964419,
              4294967295,
              4294964419,
              4294171001,
              4291930136,
              4288379660
            ],
            "stops": [
              0.0141,
              0.1252,
              0.3134,
              0.4424,
              0.5285,
              0.6468,
              0.7705,
              0.9587,
              1.0465
            ],
            "directions": {
              "begin_x": 0.0,
              "begin_y": -1.0,
              "end_x": 0.0,
              "end_y": 1.0
            }
          },
          "identity_inner_background_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "inner_background_gradients": null,
          "party_icon_background_gradients": null,
          "h2_background_gradients": {
            "colors": [4294360320, 4292026416],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "upper_footer_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": 1.0,
              "begin_y": 0.0,
              "end_x": -1.0,
              "end_y": 0.0
            }
          }
        },
        "text_color": 4294967295,
        "badge_text_color": 4294967295,
        "identity": {
          "user": {
            "id": 1201101,
            "name": "kakinada youth",
            "photo_url": "",
            "badge": null
          },
          "type": "flat_user"
        },
        "party_icon": null,
        "is_locked": false,
        "header_2_photos": [
          {
            "radius": 42.727999999999994,
            "position_x": 369.65,
            "position_y": 29.68,
            "photo_url":
                "https://a-cdn.thecircleapp.in/production/photos/41/9ef78851f86d926d2c1fc71cc00e20a0.png"
          },
          {
            "radius": 42.727999999999994,
            "position_x": 486.65,
            "position_y": 29.68,
            "photo_url":
                "https://a-cdn.thecircleapp.in/production/photos/41/8c9e88a70232378a16f035fe314088f1.png"
          }
        ],
        "is_bordered_layout": false,
        "fonts_config": {
          "name": {
            "font_family": "Noto Sans Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          },
          "badge": {
            "font_family": "Noto Sans Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          }
        },
        "analytics_params": {
          "layout_type": "basic",
          "is_locked": false,
          "golden_frame": false,
          "header_2_count": 2,
          "identity_type": "flat_user",
          "is_circle_sponsored_frame": false,
          "is_circle_sponsored_frame_locked": false,
          "user_posters_subscription_status": "free"
        }
      },
      "params": {
        "creative_id": 3928,
        "id": 841,
      }
    },
  ],
};

const posterCarouselFanRequestMock = {
  "feed_item_id": "fan-request-poster-carousel-mock",
  "feed_type": "poster_carousel",
  "title": "చత్రపతి వర్ధంతి",
  "items": [
    {
      "creative": {
        "v2_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/979114db-94f0-4f76-80a3-f3533c126afd.jpg",
        "v1_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/3a1719ca-5567-4b63-bbcd-7705743cdcf9.jpg",
        "paid": false,
        "h1_background_type": "light",
        "h2_background_type": "sticker",
        "leader_photo_ring_color": 4294967295,
        "h2_background_gradients": {
          "colors": [4294360320, 4292026416],
          "directions": {
            "begin_x": -1.0,
            "begin_y": 0,
            "end_x": 1.0,
            "end_y": 0
          }
        },
        "is_locked": false,
        "analytics_params": {
          "h1_background_type": "light",
          "h2_background_type": "sticker",
          "photo_v2_supported": true,
          "photo_v3_supported": true,
          "paid": false,
          "category_id": 841,
          "category_name": "చత్రపతి వర్ధంతి "
        }
      },
      "layout": {
        "layout_type": "basic",
        "golden_frame": false,
        "show_praja_logo": false,
        "shadow_color": null,
        "v1": null,
        "share_text":
            "తెలుగు దేశం పార్టీ మీ కోసం *స్పెషల్ పోస్టర్‌* స్పాన్సర్ చేసింది.\n\nవెంటనే పోస్టర్ పొందండి.\nhttps://prajaapp.sng.link/A3x5b/5oqr/r_b847ce06f6",
        "gradients": {
          "background_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {"begin_x": -1, "begin_y": 0, "end_x": 1, "end_y": 0}
          },
          "badge_banner_gradients": {
            "colors": [16777215, 16777215],
            "stops": [0, 1],
            "directions": {"begin_x": -1, "begin_y": 0, "end_x": 1, "end_y": 0}
          },
          "badge_ribbon_background_gradients": {
            "colors": [
              2164025858,
              2163337504,
              2164260863,
              2164025858,
              2163337504
            ],
            "stops": [-0.0389, 0.235, 0.4109, 0.7924, 1.0421],
            "directions": {"begin_x": -1, "begin_y": 0, "end_x": 1, "end_y": 0}
          },
          "footer_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {"begin_x": 1, "begin_y": 0, "end_x": -1, "end_y": 0}
          },
          "identity_border_gradients": {
            "colors": [
              4288379660,
              4291930136,
              4294171000,
              4294964419,
              4294967295,
              4294964419,
              4294171001,
              4291930136,
              4288379660
            ],
            "stops": [
              0.0141,
              0.1252,
              0.3134,
              0.4424,
              0.5285,
              0.6468,
              0.7705,
              0.9587,
              1.0465
            ],
            "directions": {"begin_x": 0, "begin_y": -1, "end_x": 0, "end_y": 1}
          },
          "identity_inner_background_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {"begin_x": -1, "begin_y": 0, "end_x": 1, "end_y": 0}
          },
          "inner_background_gradients": null,
          "party_icon_background_gradients": null,
          "h2_background_gradients": {
            "colors": [4294360320, 4292026416],
            "directions": {"begin_x": -1, "begin_y": 0, "end_x": 1, "end_y": 0}
          },
          "upper_footer_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {"begin_x": 1, "begin_y": 0, "end_x": -1, "end_y": 0}
          }
        },
        "text_color": 4294967295,
        "badge_text_color": 4294967295,
        "identity": {
          "user": {
            "id": 1061424,
            "name": "మణిదీప్ పోలిరెడ్డి",
            "photo_url":
                "https://a-cdn.thecircleapp.in/production/photos/1061424/a35bed3520b329105312ac715c4a3ea5.jpg",
            "badge": null
          },
          "type": "flat_user",
          "is_user_position_back": false,
          "show_badge_ribbon": true
        },
        "party_icon": null,
        "id": 10200,
        "is_bordered_layout": false,
        "is_locked": false,
        "frame_type": "basic",
        "fonts_config": {
          "name": {
            "font_family": "Noto Sans Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          },
          "badge": {
            "font_family": "Noto Sans Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          }
        },
        "sponsorship": {
          "icon_url":
              "https://a-cdn.thecircleapp.in/production/photos/41/1e460beb5867ac8e23d4a654360e13ed.jpg",
          "line_1": "స్పాన్సర్",
          "line_2": "తెలుగు దేశం పార్టీ",
          "line_2_text_color": 4294967295,
          "gradients": {
            "colors": [4290190120, 4292026416, 4290190120],
            "stops": [0.005, 0.5, 0.995],
            "directions": {"begin_x": -1, "begin_y": 0, "end_x": 1, "end_y": 0}
          }
        },
        "fan_poster_request": {
          "circle_id": 38109,
          "cta_text": "రిక్వెస్ట్ చేయండి",
          "requested_cta_text": "రిక్వెస్ట్ చేయబడింది",
        },
        "header_1_photos": [
          {
            "radius": 60,
            "position_x": 82,
            "position_y": 25.6,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/2f71c3af-665c-4935-adac-8e89409fdb78.png"
          },
          {
            "radius": 60,
            "position_x": 240,
            "position_y": 25.6,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/08ed3e17-b1bd-41ae-a9e3-c5c43df3f939.png"
          },
          {
            "radius": 60,
            "position_x": 398,
            "position_y": 25.6,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/b7d47b06-9e8f-47f1-8248-6224d2dc3dac.png"
          }
        ],
        "header_2_photos": [],
        "analytics_params": {
          "layout_type": "basic",
          "is_locked": false,
          "golden_frame": false,
          "header_1_count": 3,
          "header_2_count": 0,
          "identity_type": "flat_user",
          "is_user_position_back": false,
          "circle_id": 31402,
          "frame_type": "basic",
          "is_circle_sponsored_frame": true,
          "is_circle_sponsored_frame_locked": false,
          "user_posters_subscription_status": "free"
        }
      },
      "params": {
        "creative_id": 3928,
        "id": 841,
      }
    },
    {
      "creative": {
        "v2_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/979114db-94f0-4f76-80a3-f3533c126afd.jpg",
        "v1_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/3a1719ca-5567-4b63-bbcd-7705743cdcf9.jpg",
        "paid": false,
        "h1_background_type": "light",
        "h2_background_type": "sticker",
        "leader_photo_ring_color": 4294967295,
        "h2_background_gradients": {
          "colors": [4294360320, 4292026416],
          "directions": {
            "begin_x": -1.0,
            "begin_y": 0,
            "end_x": 1.0,
            "end_y": 0
          }
        },
        "is_locked": false,
        "analytics_params": {
          "h1_background_type": "light",
          "h2_background_type": "sticker",
          "photo_v2_supported": true,
          "photo_v3_supported": true,
          "paid": false,
          "category_id": 841,
          "category_name": "చత్రపతి వర్ధంతి "
        }
      },
      "layout": {
        "layout_type": "basic",
        "golden_frame": false,
        "show_praja_logo": true,
        "shadow_color": null,
        "v1": null,
        "share_text":
            "మీరు కూడా ఈ విధంగా *మీ ఫోటో మరియు మీ పేరుతో* ఉన్న  పోస్టర్ ని ఇప్పుడు *Praja App* లో ఉచితంగా పొందవచ్చు.\nతెలుగు రాష్ట్రాలలో *లక్షలాది* మంది రోజు వాడుతున్న సరికొత్త *పొలిటికల్ సోషల్ మీడియా* యాప్. ఇప్పుడే *డౌన్లోడ్* చేసుకోని నాయకుల తో *కనెక్ట్* అవ్వచ్చు అలాగే మీ *స్థానిక విషయాలు* తెలుసుకోవచ్చు.👇👇\nhttps://prajaapp.sng.link/A3x5b/5oqr?paffid=1201101",
        "gradients": {
          "background_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "badge_banner_gradients": {
            "colors": [16777215, 16777215],
            "stops": [0, 1],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "badge_ribbon_background_gradients": {
            "colors": [
              2164025858,
              2163337504,
              2164260863,
              2164025858,
              2163337504
            ],
            "stops": [-0.0389, 0.235, 0.4109, 0.7924, 1.0421],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0.0,
              "end_x": 1.0,
              "end_y": -0.0
            }
          },
          "footer_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": 1.0,
              "begin_y": 0.0,
              "end_x": -1.0,
              "end_y": 0.0
            }
          },
          "identity_border_gradients": {
            "colors": [
              4288379660,
              4291930136,
              4294171000,
              4294964419,
              4294967295,
              4294964419,
              4294171001,
              4291930136,
              4288379660
            ],
            "stops": [
              0.0141,
              0.1252,
              0.3134,
              0.4424,
              0.5285,
              0.6468,
              0.7705,
              0.9587,
              1.0465
            ],
            "directions": {
              "begin_x": 0.0,
              "begin_y": -1.0,
              "end_x": 0.0,
              "end_y": 1.0
            }
          },
          "identity_inner_background_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "inner_background_gradients": null,
          "party_icon_background_gradients": null,
          "h2_background_gradients": {
            "colors": [4294360320, 4292026416],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "upper_footer_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": 1.0,
              "begin_y": 0.0,
              "end_x": -1.0,
              "end_y": 0.0
            }
          }
        },
        "text_color": 4294967295,
        "badge_text_color": 4294967295,
        "identity": {
          "user": {
            "id": 1201101,
            "name": "kakinada youth",
            "photo_url": "",
            "badge": null
          },
          "type": "flat_user"
        },
        "party_icon": null,
        "is_locked": false,
        "header_2_photos": [
          {
            "radius": 42.727999999999994,
            "position_x": 369.65,
            "position_y": 29.68,
            "photo_url":
                "https://a-cdn.thecircleapp.in/production/photos/41/9ef78851f86d926d2c1fc71cc00e20a0.png"
          },
          {
            "radius": 42.727999999999994,
            "position_x": 486.65,
            "position_y": 29.68,
            "photo_url":
                "https://a-cdn.thecircleapp.in/production/photos/41/8c9e88a70232378a16f035fe314088f1.png"
          }
        ],
        "is_bordered_layout": false,
        "fonts_config": {
          "name": {
            "font_family": "Noto Sans Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          },
          "badge": {
            "font_family": "Noto Sans Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          }
        },
        "analytics_params": {
          "layout_type": "basic",
          "is_locked": false,
          "golden_frame": false,
          "header_2_count": 2,
          "identity_type": "flat_user",
          "is_circle_sponsored_frame": false,
          "is_circle_sponsored_frame_locked": false,
          "user_posters_subscription_status": "free"
        }
      },
      "params": {
        "creative_id": 3928,
        "id": 841,
      }
    },
    {
      "creative": {
        "v2_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/e2b47967-a6ae-40d4-82b3-a74d3708c6ad.jpg",
        "v1_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/488e6353-64f2-4f2f-9f24-92141bae6754.jpg",
        "paid": false,
        "h1_background_type": "light",
        "h2_background_type": "sticker",
        "leader_photo_ring_color": 4294967295,
        "h2_background_gradients": {
          "colors": [4294360320, 4292026416],
          "directions": {
            "begin_x": -1.0,
            "begin_y": 0,
            "end_x": 1.0,
            "end_y": 0
          }
        },
        "is_locked": false,
        "analytics_params": {
          "h1_background_type": "light",
          "h2_background_type": "sticker",
          "photo_v2_supported": true,
          "photo_v3_supported": true,
          "paid": false,
          "category_id": 841,
          "category_name": "చత్రపతి వర్ధంతి "
        }
      },
      "layout": {
        "layout_type": "basic",
        "golden_frame": false,
        "show_praja_logo": true,
        "shadow_color": null,
        "v1": null,
        "share_text":
            "మీరు కూడా ఈ విధంగా *మీ ఫోటో మరియు మీ పేరుతో* ఉన్న  పోస్టర్ ని ఇప్పుడు *Praja App* లో ఉచితంగా పొందవచ్చు.\nతెలుగు రాష్ట్రాలలో *లక్షలాది* మంది రోజు వాడుతున్న సరికొత్త *పొలిటికల్ సోషల్ మీడియా* యాప్. ఇప్పుడే *డౌన్లోడ్* చేసుకోని నాయకుల తో *కనెక్ట్* అవ్వచ్చు అలాగే మీ *స్థానిక విషయాలు* తెలుసుకోవచ్చు.👇👇\nhttps://prajaapp.sng.link/A3x5b/5oqr?paffid=1201101",
        "gradients": {
          "background_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "badge_banner_gradients": {
            "colors": [16777215, 16777215],
            "stops": [0, 1],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "badge_ribbon_background_gradients": {
            "colors": [
              2164025858,
              2163337504,
              2164260863,
              2164025858,
              2163337504
            ],
            "stops": [-0.0389, 0.235, 0.4109, 0.7924, 1.0421],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0.0,
              "end_x": 1.0,
              "end_y": -0.0
            }
          },
          "footer_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": 1.0,
              "begin_y": 0.0,
              "end_x": -1.0,
              "end_y": 0.0
            }
          },
          "identity_border_gradients": {
            "colors": [
              4288379660,
              4291930136,
              4294171000,
              4294964419,
              4294967295,
              4294964419,
              4294171001,
              4291930136,
              4288379660
            ],
            "stops": [
              0.0141,
              0.1252,
              0.3134,
              0.4424,
              0.5285,
              0.6468,
              0.7705,
              0.9587,
              1.0465
            ],
            "directions": {
              "begin_x": 0.0,
              "begin_y": -1.0,
              "end_x": 0.0,
              "end_y": 1.0
            }
          },
          "identity_inner_background_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "inner_background_gradients": null,
          "party_icon_background_gradients": null,
          "h2_background_gradients": {
            "colors": [4294360320, 4292026416],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "upper_footer_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": 1.0,
              "begin_y": 0.0,
              "end_x": -1.0,
              "end_y": 0.0
            }
          }
        },
        "text_color": 4294967295,
        "badge_text_color": 4294967295,
        "identity": {
          "user": {
            "id": 1201101,
            "name": "kakinada youth",
            "photo_url": "",
            "badge": null
          },
          "type": "flat_user"
        },
        "party_icon": null,
        "is_locked": false,
        "header_2_photos": [
          {
            "radius": 42.727999999999994,
            "position_x": 369.65,
            "position_y": 29.68,
            "photo_url":
                "https://a-cdn.thecircleapp.in/production/photos/41/9ef78851f86d926d2c1fc71cc00e20a0.png"
          },
          {
            "radius": 42.727999999999994,
            "position_x": 486.65,
            "position_y": 29.68,
            "photo_url":
                "https://a-cdn.thecircleapp.in/production/photos/41/8c9e88a70232378a16f035fe314088f1.png"
          }
        ],
        "is_bordered_layout": false,
        "fonts_config": {
          "name": {
            "font_family": "Noto Sans Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          },
          "badge": {
            "font_family": "Noto Sans Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          }
        },
        "analytics_params": {
          "layout_type": "basic",
          "is_locked": false,
          "golden_frame": false,
          "header_2_count": 2,
          "identity_type": "flat_user",
          "is_circle_sponsored_frame": false,
          "is_circle_sponsored_frame_locked": false,
          "user_posters_subscription_status": "free"
        }
      },
      "params": {
        "creative_id": 3928,
        "id": 841,
      }
    },
    {
      "creative": {
        "v2_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/979114db-94f0-4f76-80a3-f3533c126afd.jpg",
        "v1_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/3a1719ca-5567-4b63-bbcd-7705743cdcf9.jpg",
        "paid": false,
        "h1_background_type": "light",
        "h2_background_type": "sticker",
        "leader_photo_ring_color": 4294967295,
        "h2_background_gradients": {
          "colors": [4294360320, 4292026416],
          "directions": {
            "begin_x": -1.0,
            "begin_y": 0,
            "end_x": 1.0,
            "end_y": 0
          }
        },
        "is_locked": false,
        "analytics_params": {
          "h1_background_type": "light",
          "h2_background_type": "sticker",
          "photo_v2_supported": true,
          "photo_v3_supported": true,
          "paid": false,
          "category_id": 841,
          "category_name": "చత్రపతి వర్ధంతి "
        }
      },
      "layout": {
        "layout_type": "basic",
        "golden_frame": false,
        "show_praja_logo": true,
        "shadow_color": null,
        "v1": null,
        "share_text":
            "మీరు కూడా ఈ విధంగా *మీ ఫోటో మరియు మీ పేరుతో* ఉన్న  పోస్టర్ ని ఇప్పుడు *Praja App* లో ఉచితంగా పొందవచ్చు.\nతెలుగు రాష్ట్రాలలో *లక్షలాది* మంది రోజు వాడుతున్న సరికొత్త *పొలిటికల్ సోషల్ మీడియా* యాప్. ఇప్పుడే *డౌన్లోడ్* చేసుకోని నాయకుల తో *కనెక్ట్* అవ్వచ్చు అలాగే మీ *స్థానిక విషయాలు* తెలుసుకోవచ్చు.👇👇\nhttps://prajaapp.sng.link/A3x5b/5oqr?paffid=1201101",
        "gradients": {
          "background_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "badge_banner_gradients": {
            "colors": [16777215, 16777215],
            "stops": [0, 1],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "badge_ribbon_background_gradients": {
            "colors": [
              2164025858,
              2163337504,
              2164260863,
              2164025858,
              2163337504
            ],
            "stops": [-0.0389, 0.235, 0.4109, 0.7924, 1.0421],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0.0,
              "end_x": 1.0,
              "end_y": -0.0
            }
          },
          "footer_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": 1.0,
              "begin_y": 0.0,
              "end_x": -1.0,
              "end_y": 0.0
            }
          },
          "identity_border_gradients": {
            "colors": [
              4288379660,
              4291930136,
              4294171000,
              4294964419,
              4294967295,
              4294964419,
              4294171001,
              4291930136,
              4288379660
            ],
            "stops": [
              0.0141,
              0.1252,
              0.3134,
              0.4424,
              0.5285,
              0.6468,
              0.7705,
              0.9587,
              1.0465
            ],
            "directions": {
              "begin_x": 0.0,
              "begin_y": -1.0,
              "end_x": 0.0,
              "end_y": 1.0
            }
          },
          "identity_inner_background_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "inner_background_gradients": null,
          "party_icon_background_gradients": null,
          "h2_background_gradients": {
            "colors": [4294360320, 4292026416],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "upper_footer_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": 1.0,
              "begin_y": 0.0,
              "end_x": -1.0,
              "end_y": 0.0
            }
          }
        },
        "text_color": 4294967295,
        "badge_text_color": 4294967295,
        "identity": {
          "user": {
            "id": 1201101,
            "name": "kakinada youth",
            "photo_url": "",
            "badge": null
          },
          "type": "flat_user"
        },
        "party_icon": null,
        "is_locked": false,
        "header_2_photos": [
          {
            "radius": 42.727999999999994,
            "position_x": 369.65,
            "position_y": 29.68,
            "photo_url":
                "https://a-cdn.thecircleapp.in/production/photos/41/9ef78851f86d926d2c1fc71cc00e20a0.png"
          },
          {
            "radius": 42.727999999999994,
            "position_x": 486.65,
            "position_y": 29.68,
            "photo_url":
                "https://a-cdn.thecircleapp.in/production/photos/41/8c9e88a70232378a16f035fe314088f1.png"
          }
        ],
        "is_bordered_layout": false,
        "fonts_config": {
          "name": {
            "font_family": "Noto Sans Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          },
          "badge": {
            "font_family": "Noto Sans Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          }
        },
        "analytics_params": {
          "layout_type": "basic",
          "is_locked": false,
          "golden_frame": false,
          "header_2_count": 2,
          "identity_type": "flat_user",
          "is_circle_sponsored_frame": false,
          "is_circle_sponsored_frame_locked": false,
          "user_posters_subscription_status": "free"
        }
      },
      "params": {
        "creative_id": 3928,
        "id": 841,
      }
    },
    {
      "creative": {
        "v2_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/e2b47967-a6ae-40d4-82b3-a74d3708c6ad.jpg",
        "v1_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/488e6353-64f2-4f2f-9f24-92141bae6754.jpg",
        "paid": false,
        "h1_background_type": "light",
        "h2_background_type": "sticker",
        "leader_photo_ring_color": 4294967295,
        "h2_background_gradients": {
          "colors": [4294360320, 4292026416],
          "directions": {
            "begin_x": -1.0,
            "begin_y": 0,
            "end_x": 1.0,
            "end_y": 0
          }
        },
        "is_locked": false,
        "analytics_params": {
          "h1_background_type": "light",
          "h2_background_type": "sticker",
          "photo_v2_supported": true,
          "photo_v3_supported": true,
          "paid": false,
          "category_id": 841,
          "category_name": "చత్రపతి వర్ధంతి "
        }
      },
      "layout": {
        "layout_type": "basic",
        "golden_frame": false,
        "show_praja_logo": true,
        "shadow_color": null,
        "v1": null,
        "share_text":
            "మీరు కూడా ఈ విధంగా *మీ ఫోటో మరియు మీ పేరుతో* ఉన్న  పోస్టర్ ని ఇప్పుడు *Praja App* లో ఉచితంగా పొందవచ్చు.\nతెలుగు రాష్ట్రాలలో *లక్షలాది* మంది రోజు వాడుతున్న సరికొత్త *పొలిటికల్ సోషల్ మీడియా* యాప్. ఇప్పుడే *డౌన్లోడ్* చేసుకోని నాయకుల తో *కనెక్ట్* అవ్వచ్చు అలాగే మీ *స్థానిక విషయాలు* తెలుసుకోవచ్చు.👇👇\nhttps://prajaapp.sng.link/A3x5b/5oqr?paffid=1201101",
        "gradients": {
          "background_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "badge_banner_gradients": {
            "colors": [16777215, 16777215],
            "stops": [0, 1],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "badge_ribbon_background_gradients": {
            "colors": [
              2164025858,
              2163337504,
              2164260863,
              2164025858,
              2163337504
            ],
            "stops": [-0.0389, 0.235, 0.4109, 0.7924, 1.0421],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0.0,
              "end_x": 1.0,
              "end_y": -0.0
            }
          },
          "footer_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": 1.0,
              "begin_y": 0.0,
              "end_x": -1.0,
              "end_y": 0.0
            }
          },
          "identity_border_gradients": {
            "colors": [
              4288379660,
              4291930136,
              4294171000,
              4294964419,
              4294967295,
              4294964419,
              4294171001,
              4291930136,
              4288379660
            ],
            "stops": [
              0.0141,
              0.1252,
              0.3134,
              0.4424,
              0.5285,
              0.6468,
              0.7705,
              0.9587,
              1.0465
            ],
            "directions": {
              "begin_x": 0.0,
              "begin_y": -1.0,
              "end_x": 0.0,
              "end_y": 1.0
            }
          },
          "identity_inner_background_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "inner_background_gradients": null,
          "party_icon_background_gradients": null,
          "h2_background_gradients": {
            "colors": [4294360320, 4292026416],
            "directions": {
              "begin_x": -1.0,
              "begin_y": 0,
              "end_x": 1.0,
              "end_y": 0
            }
          },
          "upper_footer_gradients": {
            "colors": [4294360320, 4292026416],
            "stops": [0.0603, 0.9463],
            "directions": {
              "begin_x": 1.0,
              "begin_y": 0.0,
              "end_x": -1.0,
              "end_y": 0.0
            }
          }
        },
        "text_color": 4294967295,
        "badge_text_color": 4294967295,
        "identity": {
          "user": {
            "id": 1201101,
            "name": "kakinada youth",
            "photo_url": "",
            "badge": null
          },
          "type": "flat_user"
        },
        "party_icon": null,
        "is_locked": false,
        "header_2_photos": [
          {
            "radius": 42.727999999999994,
            "position_x": 369.65,
            "position_y": 29.68,
            "photo_url":
                "https://a-cdn.thecircleapp.in/production/photos/41/9ef78851f86d926d2c1fc71cc00e20a0.png"
          },
          {
            "radius": 42.727999999999994,
            "position_x": 486.65,
            "position_y": 29.68,
            "photo_url":
                "https://a-cdn.thecircleapp.in/production/photos/41/8c9e88a70232378a16f035fe314088f1.png"
          }
        ],
        "is_bordered_layout": false,
        "fonts_config": {
          "name": {
            "font_family": "Noto Sans Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          },
          "badge": {
            "font_family": "Noto Sans Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          }
        },
        "analytics_params": {
          "layout_type": "basic",
          "is_locked": false,
          "golden_frame": false,
          "header_2_count": 2,
          "identity_type": "flat_user",
          "is_circle_sponsored_frame": false,
          "is_circle_sponsored_frame_locked": false,
          "user_posters_subscription_status": "free"
        }
      },
      "params": {
        "creative_id": 3928,
        "id": 841,
      }
    },
  ],
};

const posterCarouselPremiumMocks = {
  "feed_type": "poster_carousel",
  "feed_item_id": "premium-poster-carousel-mocks",
  "show_help": true,
  "items": [
    {
      "creative": {
        "v2_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/a307dafb-9146-4c17-ae00-fb05bcaed861.jpg",
        "v1_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/9a3ea81b-4c4f-402c-9c6e-fe93a55fefaf.jpg",
        "paid": false,
        "h1_background_type": "light",
        "h2_background_type": "sticker",
        "leader_photo_ring_color": 4294967295,
        "h2_background_gradients": {
          "colors": [4278215167, 4288798207],
          "directions": {"begin_x": 0, "begin_y": -1, "end_x": 0, "end_y": 1}
        },
        "is_locked": false,
        "analytics_params": {
          "h1_background_type": "light",
          "h2_background_type": "sticker",
          "photo_v2_supported": true,
          "photo_v3_supported": true,
          "paid": false,
          "circle_id": 33010,
          "circle_name": "తెలంగాణ",
          "circle_name_en": "Telangana",
          "circle_type": "location",
          "circle_level": "state",
          "category_kind": "info"
        }
      },
      "layout": {
        "layout_type": "premium",
        "golden_frame": false,
        "show_praja_logo": true,
        "shadow_color": null,
        "v1": null,
        "share_text": " ",
        "gradients": {
          "background_gradients": {
            "colors": [4278215167, 4288798207],
            "stops": [0, 1],
            "directions": {"begin_x": 0, "begin_y": -1, "end_x": 0, "end_y": 1}
          },
          "badge_banner_gradients": {
            "colors": [16777215, 16777215],
            "stops": [0, 1],
            "directions": {"begin_x": -1, "begin_y": 0, "end_x": 1, "end_y": 0}
          },
          "badge_ribbon_background_gradients": {
            "colors": [4287619839, 4278748927],
            "stops": [0.389, 1.0421],
            "directions": {"begin_x": -1, "begin_y": 0, "end_x": 1, "end_y": 0}
          },
          "footer_gradients": {
            "colors": [4278215167, 4288798207],
            "stops": null,
            "directions": {"begin_x": 1, "begin_y": 0, "end_x": -1, "end_y": 0}
          },
          "identity_border_gradients": {
            "colors": [
              4288379660,
              4291930136,
              4294171000,
              4294964419,
              4294967295,
              4294964419,
              4294171001,
              4291930136,
              4288379660
            ],
            "stops": [
              0.0141,
              0.1252,
              0.3134,
              0.4424,
              0.5285,
              0.6468,
              0.7705,
              0.9587,
              1.0465
            ],
            "directions": {"begin_x": 0, "begin_y": -1, "end_x": 0, "end_y": 1}
          },
          "identity_inner_background_gradients": {
            "colors": [4278215167, 4288798207],
            "stops": [0, 1],
            "directions": {"begin_x": 0, "begin_y": -1, "end_x": 0, "end_y": 1}
          },
          "inner_background_gradients": null,
          "party_icon_background_gradients": null,
          "h2_background_gradients": {
            "colors": [4278215167, 4288798207],
            "directions": {"begin_x": 0, "begin_y": -1, "end_x": 0, "end_y": 1}
          },
          "upper_footer_gradients": {
            "colors": [4278215167, 4288798207],
            "stops": null,
            "directions": {"begin_x": 1, "begin_y": 0, "end_x": -1, "end_y": 0}
          }
        },
        "text_color": 4294967295,
        "badge_text_color": 4294967295,
        "identity": {
          "user": {
            "id": 18,
            "name": "Uday Kambhampati",
            "photo_url":
                "https://a-cdn.thecircleapp.in/production/admin-media/24/137c680781f53a81e40f8a86ba9c29d8.jpg",
            "badge": null
          },
          "type": "flat_user_badge_circle",
          "is_user_position_back": true,
          "show_badge_ribbon": true,
          "party_highlight_color_primary": 4288798207,
          "slogan_text": "",
          "party_icon_url": null
        },
        "party_icon": null,
        "header_1_photos": [
          {
            "radius": 44,
            "position_x": 39,
            "position_y": 33.6,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/f3cf65a7-a249-4594-be3c-8547256207ad.png"
          },
          {
            "radius": 44,
            "position_x": 477,
            "position_y": 33.6,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/27e7d7e2-8b77-46e2-b2e3-01ba3becbeb6.png"
          }
        ],
        "header_2_photos": [
          {
            "radius": 27.439999999999998,
            "position_x": 165.06,
            "position_y": 63.767999999999994,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/80e19ee8-02bc-449c-937e-b19dea22162c.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 242.06,
            "position_y": 63.767999999999994,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/cdbefce9-c75e-4b7d-a160-c71cfbef8287.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 319.06,
            "position_y": 63.767999999999994,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/9804e1c0-9b44-4df9-a942-92616523764f.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 396.06,
            "position_y": 63.767999999999994,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/16471dbe-455c-458b-8729-38d16b14c93f.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 93.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/705b5658-72dd-4581-9c32-1ee385a995e9.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 168.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/58783e99-a39f-419d-803f-342a4b6daaaf.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 243.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/35291be4-d164-411c-935a-cdea0768c135.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 318.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/a76f3b3f-0084-4794-8e9c-c491eccc3c49.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 393.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/223e67f9-7fe1-47c1-9316-cb2f6d757de2.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 468.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/e5f8e6e1-3cec-4359-96b3-fe29bbfdb47a.png"
          }
        ],
        "is_locked": false,
        "id": 12,
        "is_bordered_layout": false,
        "neutral_frame": false,
        "enable_outer_frame": true,
        "frame_type": "premium",
        "fonts_config": {
          "name": {
            "font_family": "Anek Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          },
          "badge": {
            "font_family": "Noto Sans Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          }
        },
        "is_selected": true,
        "analytics_params": {
          "layout_type": "premium",
          "is_locked": false,
          "golden_frame": false,
          "header_1_count": 2,
          "header_2_count": 10,
          "identity_type": "flat_user_badge_circle",
          "is_user_position_back": true,
          "circle_id": 33010,
          "frame_type": "premium",
          "is_circle_sponsored_frame": false,
          "is_circle_sponsored_frame_locked": false,
          "user_posters_subscription_status": "paid"
        }
      },
      "params": {
        "creative_id": 33,
        "creative_kind": "info",
        "circle_id": 33010,
      }
    },
    {
      "creative": {
        "v2_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/a307dafb-9146-4c17-ae00-fb05bcaed861.jpg",
        "v1_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/9a3ea81b-4c4f-402c-9c6e-fe93a55fefaf.jpg",
        "paid": false,
        "h1_background_type": "light",
        "h2_background_type": "sticker",
        "leader_photo_ring_color": 4294967295,
        "h2_background_gradients": {
          "colors": [4278215167, 4288798207],
          "directions": {"begin_x": 0, "begin_y": -1, "end_x": 0, "end_y": 1}
        },
        "is_locked": false,
        "analytics_params": {
          "h1_background_type": "light",
          "h2_background_type": "sticker",
          "photo_v2_supported": true,
          "photo_v3_supported": true,
          "paid": false,
          "circle_id": 33010,
          "circle_name": "తెలంగాణ",
          "circle_name_en": "Telangana",
          "circle_type": "location",
          "circle_level": "state",
          "category_kind": "info"
        }
      },
      "layout": {
        "layout_type": "premium",
        "golden_frame": true,
        "show_praja_logo": true,
        "shadow_color": null,
        "v1": null,
        "share_text": " ",
        "gradients": {
          "background_gradients": {
            "colors": [
              4288379660,
              4291930136,
              4294171000,
              4294964419,
              4294967295,
              4294964419,
              4294171001,
              4291930136,
              4288379660
            ],
            "stops": [
              0.0141,
              0.1252,
              0.3134,
              0.4424,
              0.5285,
              0.6468,
              0.7705,
              0.9587,
              1.0465
            ],
            "directions": {
              "begin_x": 1,
              "begin_y": 0.1,
              "end_x": -1,
              "end_y": -0.1
            }
          },
          "badge_banner_gradients": {
            "colors": [16777215, 16777215],
            "stops": [0, 1],
            "directions": {"begin_x": -1, "begin_y": 0, "end_x": 1, "end_y": 0}
          },
          "badge_ribbon_background_gradients": {
            "colors": [
              4288379660,
              4291930136,
              4294171000,
              4294964419,
              4294967295,
              4294964419,
              4294171001,
              4291930136,
              4288379660
            ],
            "stops": [
              0.0141,
              0.1252,
              0.3134,
              0.4424,
              0.5285,
              0.6468,
              0.7705,
              0.9587,
              1.0465
            ],
            "directions": {"begin_x": -1, "begin_y": 0, "end_x": 1, "end_y": 0}
          },
          "footer_gradients": {
            "colors": [4294967295, 4294967295],
            "stops": null,
            "directions": {"begin_x": -1, "begin_y": 0, "end_x": 1, "end_y": 0}
          },
          "identity_border_gradients": {
            "colors": [
              4288379660,
              4291930136,
              4294171000,
              4294964419,
              4294967295,
              4294964419,
              4294171001,
              4291930136,
              4288379660
            ],
            "stops": [
              0.0141,
              0.1252,
              0.3134,
              0.4424,
              0.5285,
              0.6468,
              0.7705,
              0.9587,
              1.0465
            ],
            "directions": {"begin_x": 0, "begin_y": -1, "end_x": 0, "end_y": 1}
          },
          "identity_inner_background_gradients": {
            "colors": [4278215167, 4288798207],
            "stops": [0, 1],
            "directions": {"begin_x": 0, "begin_y": -1, "end_x": 0, "end_y": 1}
          },
          "inner_background_gradients": null,
          "party_icon_background_gradients": null,
          "h2_background_gradients": {
            "colors": [4278215167, 4288798207],
            "directions": {"begin_x": 0, "begin_y": -1, "end_x": 0, "end_y": 1}
          },
          "upper_footer_gradients": {
            "colors": [4278215167, 4288798207],
            "stops": null,
            "directions": {"begin_x": 1, "begin_y": 0, "end_x": -1, "end_y": 0}
          }
        },
        "text_color": null,
        "badge_text_color": 4294967295,
        "identity": {
          "user": {
            "id": 18,
            "name": "Uday Kambhampati",
            "photo_url":
                "https://a-cdn.thecircleapp.in/production/admin-media/24/deba8a92-2ba4-4f0f-885a-1a2bf063e643.png",
            "badge": null
          },
          "type": "gold_lettered_user",
          "is_user_position_back": true,
          "show_badge_ribbon": true,
          "party_highlight_color_primary": 4288798207,
          "slogan_text": "",
          "party_icon_url": null
        },
        "party_icon": null,
        "header_1_photos": [
          {
            "radius": 44,
            "position_x": 39,
            "position_y": 33.6,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/f3cf65a7-a249-4594-be3c-8547256207ad.png"
          },
          {
            "radius": 44,
            "position_x": 477,
            "position_y": 33.6,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/27e7d7e2-8b77-46e2-b2e3-01ba3becbeb6.png"
          }
        ],
        "header_2_photos": [
          {
            "radius": 27.439999999999998,
            "position_x": 165.06,
            "position_y": 63.767999999999994,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/80e19ee8-02bc-449c-937e-b19dea22162c.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 242.06,
            "position_y": 63.767999999999994,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/cdbefce9-c75e-4b7d-a160-c71cfbef8287.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 319.06,
            "position_y": 63.767999999999994,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/9804e1c0-9b44-4df9-a942-92616523764f.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 396.06,
            "position_y": 63.767999999999994,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/16471dbe-455c-458b-8729-38d16b14c93f.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 93.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/705b5658-72dd-4581-9c32-1ee385a995e9.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 168.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/58783e99-a39f-419d-803f-342a4b6daaaf.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 243.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/35291be4-d164-411c-935a-cdea0768c135.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 318.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/a76f3b3f-0084-4794-8e9c-c491eccc3c49.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 393.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/223e67f9-7fe1-47c1-9316-cb2f6d757de2.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 468.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/e5f8e6e1-3cec-4359-96b3-fe29bbfdb47a.png"
          }
        ],
        "is_locked": false,
        "id": 16,
        "is_bordered_layout": false,
        "neutral_frame": false,
        "enable_outer_frame": true,
        "frame_type": "status",
        "fonts_config": {
          "name": {
            "font_family": "Anek Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          },
          "badge": {
            "font_family": "Noto Sans Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          }
        },
        "is_selected": true,
        "analytics_params": {
          "layout_type": "premium",
          "is_locked": false,
          "golden_frame": true,
          "header_1_count": 2,
          "header_2_count": 10,
          "identity_type": "gold_lettered_user",
          "is_user_position_back": true,
          "circle_id": 33010,
          "frame_type": "status",
          "is_circle_sponsored_frame": false,
          "is_circle_sponsored_frame_locked": false,
          "user_posters_subscription_status": "paid"
        }
      },
      "params": {
        "creative_id": 33,
        "creative_kind": "info",
        "circle_id": 33010,
      }
    },
    {
      "creative": {
        "v2_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/a307dafb-9146-4c17-ae00-fb05bcaed861.jpg",
        "v1_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/9a3ea81b-4c4f-402c-9c6e-fe93a55fefaf.jpg",
        "paid": false,
        "h1_background_type": "light",
        "h2_background_type": "sticker",
        "leader_photo_ring_color": 4294967295,
        "h2_background_gradients": {
          "colors": [4278215167, 4288798207],
          "directions": {"begin_x": 0, "begin_y": -1, "end_x": 0, "end_y": 1}
        },
        "is_locked": false,
        "analytics_params": {
          "h1_background_type": "light",
          "h2_background_type": "sticker",
          "photo_v2_supported": true,
          "photo_v3_supported": true,
          "paid": false,
          "circle_id": 33010,
          "circle_name": "తెలంగాణ",
          "circle_name_en": "Telangana",
          "circle_type": "location",
          "circle_level": "state",
          "category_kind": "info"
        }
      },
      "layout": {
        "layout_type": "premium",
        "golden_frame": true,
        "show_praja_logo": true,
        "shadow_color": null,
        "v1": null,
        "share_text": " ",
        "gradients": {
          "background_gradients": {
            "colors": [
              4288379660,
              4291930136,
              4294171000,
              4294964419,
              4294967295,
              4294964419,
              4294171001,
              4291930136,
              4288379660
            ],
            "stops": [
              0.0141,
              0.1252,
              0.3134,
              0.4424,
              0.5285,
              0.6468,
              0.7705,
              0.9587,
              1.0465
            ],
            "directions": {
              "begin_x": 1,
              "begin_y": 0.1,
              "end_x": -1,
              "end_y": -0.1
            }
          },
          "badge_banner_gradients": {
            "colors": [16777215, 16777215],
            "stops": [0, 1],
            "directions": {"begin_x": -1, "begin_y": 0, "end_x": 1, "end_y": 0}
          },
          "badge_ribbon_background_gradients": {
            "colors": [
              2164025858,
              2163337504,
              2164260863,
              2164025858,
              2163337504
            ],
            "stops": [-0.0389, 0.235, 0.4109, 0.7924, 1.0421],
            "directions": {"begin_x": -1, "begin_y": 0, "end_x": 1, "end_y": 0}
          },
          "footer_gradients": {
            "colors": [
              4293836359,
              4294499191,
              4294962838,
              4294961327,
              4294962838,
              4288832030,
              4293829964,
              4294499191,
              4293836359,
              4288575773
            ],
            "stops": [
              0,
              0.0816,
              0.1753,
              0.2586,
              0.4097,
              0.5451,
              0.6545,
              0.8264,
              0.9253,
              1
            ],
            "directions": {
              "begin_x": 1,
              "begin_y": 0.1,
              "end_x": -1,
              "end_y": -0.1
            }
          },
          "identity_border_gradients": {
            "colors": [
              4288379660,
              4291930136,
              4294171000,
              4294964419,
              4294967295,
              4294964419,
              4294171001,
              4291930136,
              4288379660
            ],
            "stops": [
              0.0141,
              0.1252,
              0.3134,
              0.4424,
              0.5285,
              0.6468,
              0.7705,
              0.9587,
              1.0465
            ],
            "directions": {"begin_x": 0, "begin_y": -1, "end_x": 0, "end_y": 1}
          },
          "identity_inner_background_gradients": {
            "colors": [4278215167, 4288798207],
            "stops": [0, 1],
            "directions": {"begin_x": 0, "begin_y": -1, "end_x": 0, "end_y": 1}
          },
          "inner_background_gradients": {
            "colors": [
              4294171000,
              4292714264,
              4294964419,
              4294964419,
              4294171001,
              4292714264
            ],
            "stops": [0.0909, 0.1889, 0.4163, 0.5715, 0.7349, 0.8752],
            "directions": {
              "begin_x": -1,
              "begin_y": 0.1,
              "end_x": 1,
              "end_y": -0.1
            }
          },
          "party_icon_background_gradients": null,
          "h2_background_gradients": {
            "colors": [4278215167, 4288798207],
            "directions": {"begin_x": 0, "begin_y": -1, "end_x": 0, "end_y": 1}
          },
          "upper_footer_gradients": {
            "colors": [4278215167, 4288798207],
            "stops": null,
            "directions": {"begin_x": 1, "begin_y": 0, "end_x": -1, "end_y": 0}
          }
        },
        "text_color": 4279519637,
        "badge_text_color": 4294967295,
        "identity": {
          "user": {
            "id": 18,
            "name": "Uday Kambhampati",
            "photo_url":
                "https://a-cdn.thecircleapp.in/production/admin-media/24/deba8a92-2ba4-4f0f-885a-1a2bf063e643.png",
            "badge": null
          },
          "type": "curved_with_depth",
          "is_user_position_back": true,
          "show_badge_ribbon": true,
          "party_highlight_color_primary": 4288798207,
          "slogan_text": "",
          "party_icon_url": null
        },
        "party_icon": null,
        "header_1_photos": [
          {
            "radius": 44,
            "position_x": 39,
            "position_y": 33.6,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/f3cf65a7-a249-4594-be3c-8547256207ad.png"
          },
          {
            "radius": 44,
            "position_x": 477,
            "position_y": 33.6,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/27e7d7e2-8b77-46e2-b2e3-01ba3becbeb6.png"
          }
        ],
        "header_2_photos": [
          {
            "radius": 27.439999999999998,
            "position_x": 165.06,
            "position_y": 63.767999999999994,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/80e19ee8-02bc-449c-937e-b19dea22162c.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 242.06,
            "position_y": 63.767999999999994,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/cdbefce9-c75e-4b7d-a160-c71cfbef8287.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 319.06,
            "position_y": 63.767999999999994,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/9804e1c0-9b44-4df9-a942-92616523764f.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 396.06,
            "position_y": 63.767999999999994,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/16471dbe-455c-458b-8729-38d16b14c93f.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 93.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/705b5658-72dd-4581-9c32-1ee385a995e9.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 168.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/58783e99-a39f-419d-803f-342a4b6daaaf.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 243.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/35291be4-d164-411c-935a-cdea0768c135.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 318.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/a76f3b3f-0084-4794-8e9c-c491eccc3c49.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 393.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/223e67f9-7fe1-47c1-9316-cb2f6d757de2.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 468.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/e5f8e6e1-3cec-4359-96b3-fe29bbfdb47a.png"
          }
        ],
        "is_locked": false,
        "id": 17,
        "is_bordered_layout": false,
        "neutral_frame": false,
        "enable_outer_frame": true,
        "frame_type": "status",
        "fonts_config": {
          "name": {
            "font_family": "Anek Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          },
          "badge": {
            "font_family": "Noto Sans Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          }
        },
        "is_selected": true,
        "analytics_params": {
          "layout_type": "premium",
          "is_locked": false,
          "golden_frame": true,
          "header_1_count": 2,
          "header_2_count": 10,
          "identity_type": "curved_with_depth",
          "is_user_position_back": true,
          "circle_id": 33010,
          "frame_type": "status",
          "is_circle_sponsored_frame": false,
          "is_circle_sponsored_frame_locked": false,
          "user_posters_subscription_status": "paid"
        }
      },
      "params": {
        "creative_id": 33,
        "creative_kind": "info",
        "circle_id": 33010,
      }
    }
  ],
};

const posterCarouselTrialPmfHitMocks = {
  "feed_type": "poster_carousel",
  "feed_item_id": "trial-pmf-hit-poster-carousel-mocks",
  "show_help": true,
  "items": [
    {
      "creative": {
        "v2_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/a307dafb-9146-4c17-ae00-fb05bcaed861.jpg",
        "v1_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/9a3ea81b-4c4f-402c-9c6e-fe93a55fefaf.jpg",
        "paid": false,
        "h1_background_type": "light",
        "h2_background_type": "sticker",
        "leader_photo_ring_color": 4294967295,
        "h2_background_gradients": {
          "colors": [4278215167, 4288798207],
          "directions": {"begin_x": 0, "begin_y": -1, "end_x": 0, "end_y": 1}
        },
        "is_locked": false,
        "analytics_params": {
          "h1_background_type": "light",
          "h2_background_type": "sticker",
          "photo_v2_supported": true,
          "photo_v3_supported": true,
          "paid": false,
          "circle_id": 33010,
          "circle_name": "తెలంగాణ",
          "circle_name_en": "Telangana",
          "circle_type": "location",
          "circle_level": "state",
          "category_kind": "info"
        }
      },
      "layout": {
        "layout_type": "premium",
        "subscription_screen": {
          "image_url":
              "https://a-cdn.thecircleapp.in/production/admin-media/33/9a3ea81b-4c4f-402c-9c6e-fe93a55fefaf.jpg",
          "subscribe_button": {
            "amount": 3000,
            "period": "12 నెలలు",
            "content": "ప్రత్యేక పోస్టర్లు"
          },
          "free_button_text": "ఉచితంగా ప్రయత్నించండి",
          "order_id": "random_order_id",
          "terms": {
            "link_text": "నియమాలు",
            "link_url": "",
          },
        },
        "golden_frame": false,
        "show_praja_logo": true,
        "shadow_color": null,
        "v1": null,
        "share_text": " ",
        "gradients": {
          "background_gradients": {
            "colors": [4278215167, 4288798207],
            "stops": [0, 1],
            "directions": {"begin_x": 0, "begin_y": -1, "end_x": 0, "end_y": 1}
          },
          "badge_banner_gradients": {
            "colors": [16777215, 16777215],
            "stops": [0, 1],
            "directions": {"begin_x": -1, "begin_y": 0, "end_x": 1, "end_y": 0}
          },
          "badge_ribbon_background_gradients": {
            "colors": [4287619839, 4278748927],
            "stops": [0.389, 1.0421],
            "directions": {"begin_x": -1, "begin_y": 0, "end_x": 1, "end_y": 0}
          },
          "footer_gradients": {
            "colors": [4278215167, 4288798207],
            "stops": null,
            "directions": {"begin_x": 1, "begin_y": 0, "end_x": -1, "end_y": 0}
          },
          "identity_border_gradients": {
            "colors": [
              4288379660,
              4291930136,
              4294171000,
              4294964419,
              4294967295,
              4294964419,
              4294171001,
              4291930136,
              4288379660
            ],
            "stops": [
              0.0141,
              0.1252,
              0.3134,
              0.4424,
              0.5285,
              0.6468,
              0.7705,
              0.9587,
              1.0465
            ],
            "directions": {"begin_x": 0, "begin_y": -1, "end_x": 0, "end_y": 1}
          },
          "identity_inner_background_gradients": {
            "colors": [4278215167, 4288798207],
            "stops": [0, 1],
            "directions": {"begin_x": 0, "begin_y": -1, "end_x": 0, "end_y": 1}
          },
          "inner_background_gradients": null,
          "party_icon_background_gradients": null,
          "h2_background_gradients": {
            "colors": [4278215167, 4288798207],
            "directions": {"begin_x": 0, "begin_y": -1, "end_x": 0, "end_y": 1}
          },
          "upper_footer_gradients": {
            "colors": [4278215167, 4288798207],
            "stops": null,
            "directions": {"begin_x": 1, "begin_y": 0, "end_x": -1, "end_y": 0}
          }
        },
        "text_color": 4294967295,
        "badge_text_color": 4294967295,
        "identity": {
          "user": {
            "id": 18,
            "name": "Uday Kambhampati",
            "photo_url":
                "https://a-cdn.thecircleapp.in/production/admin-media/24/137c680781f53a81e40f8a86ba9c29d8.jpg",
            "badge": null
          },
          "type": "flat_user_badge_circle",
          "is_user_position_back": true,
          "show_badge_ribbon": true,
          "party_highlight_color_primary": 4288798207,
          "slogan_text": "",
          "party_icon_url": null
        },
        "party_icon": null,
        "header_1_photos": [
          {
            "radius": 44,
            "position_x": 39,
            "position_y": 33.6,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/f3cf65a7-a249-4594-be3c-8547256207ad.png"
          },
          {
            "radius": 44,
            "position_x": 477,
            "position_y": 33.6,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/27e7d7e2-8b77-46e2-b2e3-01ba3becbeb6.png"
          }
        ],
        "header_2_photos": [
          {
            "radius": 27.439999999999998,
            "position_x": 165.06,
            "position_y": 63.767999999999994,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/80e19ee8-02bc-449c-937e-b19dea22162c.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 242.06,
            "position_y": 63.767999999999994,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/cdbefce9-c75e-4b7d-a160-c71cfbef8287.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 319.06,
            "position_y": 63.767999999999994,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/9804e1c0-9b44-4df9-a942-92616523764f.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 396.06,
            "position_y": 63.767999999999994,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/16471dbe-455c-458b-8729-38d16b14c93f.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 93.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/705b5658-72dd-4581-9c32-1ee385a995e9.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 168.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/58783e99-a39f-419d-803f-342a4b6daaaf.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 243.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/35291be4-d164-411c-935a-cdea0768c135.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 318.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/a76f3b3f-0084-4794-8e9c-c491eccc3c49.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 393.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/223e67f9-7fe1-47c1-9316-cb2f6d757de2.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 468.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/e5f8e6e1-3cec-4359-96b3-fe29bbfdb47a.png"
          }
        ],
        "is_locked": false,
        "id": 12,
        "is_bordered_layout": false,
        "neutral_frame": false,
        "enable_outer_frame": true,
        "frame_type": "premium",
        "fonts_config": {
          "name": {
            "font_family": "Anek Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          },
          "badge": {
            "font_family": "Noto Sans Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          }
        },
        "is_selected": true,
        "analytics_params": {
          "layout_type": "premium",
          "is_locked": false,
          "golden_frame": false,
          "header_1_count": 2,
          "header_2_count": 10,
          "identity_type": "flat_user_badge_circle",
          "is_user_position_back": true,
          "circle_id": 33010,
          "frame_type": "premium",
          "is_circle_sponsored_frame": false,
          "is_circle_sponsored_frame_locked": false,
          "user_posters_subscription_status": "paid"
        }
      },
      "params": {
        "creative_id": 33,
        "creative_kind": "info",
        "circle_id": 33010,
      }
    },
    {
      "creative": {
        "v2_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/a307dafb-9146-4c17-ae00-fb05bcaed861.jpg",
        "v1_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/9a3ea81b-4c4f-402c-9c6e-fe93a55fefaf.jpg",
        "paid": false,
        "h1_background_type": "light",
        "h2_background_type": "sticker",
        "leader_photo_ring_color": 4294967295,
        "h2_background_gradients": {
          "colors": [4278215167, 4288798207],
          "directions": {"begin_x": 0, "begin_y": -1, "end_x": 0, "end_y": 1}
        },
        "is_locked": false,
        "analytics_params": {
          "h1_background_type": "light",
          "h2_background_type": "sticker",
          "photo_v2_supported": true,
          "photo_v3_supported": true,
          "paid": false,
          "circle_id": 33010,
          "circle_name": "తెలంగాణ",
          "circle_name_en": "Telangana",
          "circle_type": "location",
          "circle_level": "state",
          "category_kind": "info"
        }
      },
      "layout": {
        "layout_type": "premium",
        "golden_frame": true,
        "show_praja_logo": true,
        "shadow_color": null,
        "v1": null,
        "share_text": " ",
        "gradients": {
          "background_gradients": {
            "colors": [
              4288379660,
              4291930136,
              4294171000,
              4294964419,
              4294967295,
              4294964419,
              4294171001,
              4291930136,
              4288379660
            ],
            "stops": [
              0.0141,
              0.1252,
              0.3134,
              0.4424,
              0.5285,
              0.6468,
              0.7705,
              0.9587,
              1.0465
            ],
            "directions": {
              "begin_x": 1,
              "begin_y": 0.1,
              "end_x": -1,
              "end_y": -0.1
            }
          },
          "badge_banner_gradients": {
            "colors": [16777215, 16777215],
            "stops": [0, 1],
            "directions": {"begin_x": -1, "begin_y": 0, "end_x": 1, "end_y": 0}
          },
          "badge_ribbon_background_gradients": {
            "colors": [
              4288379660,
              4291930136,
              4294171000,
              4294964419,
              4294967295,
              4294964419,
              4294171001,
              4291930136,
              4288379660
            ],
            "stops": [
              0.0141,
              0.1252,
              0.3134,
              0.4424,
              0.5285,
              0.6468,
              0.7705,
              0.9587,
              1.0465
            ],
            "directions": {"begin_x": -1, "begin_y": 0, "end_x": 1, "end_y": 0}
          },
          "footer_gradients": {
            "colors": [4294967295, 4294967295],
            "stops": null,
            "directions": {"begin_x": -1, "begin_y": 0, "end_x": 1, "end_y": 0}
          },
          "identity_border_gradients": {
            "colors": [
              4288379660,
              4291930136,
              4294171000,
              4294964419,
              4294967295,
              4294964419,
              4294171001,
              4291930136,
              4288379660
            ],
            "stops": [
              0.0141,
              0.1252,
              0.3134,
              0.4424,
              0.5285,
              0.6468,
              0.7705,
              0.9587,
              1.0465
            ],
            "directions": {"begin_x": 0, "begin_y": -1, "end_x": 0, "end_y": 1}
          },
          "identity_inner_background_gradients": {
            "colors": [4278215167, 4288798207],
            "stops": [0, 1],
            "directions": {"begin_x": 0, "begin_y": -1, "end_x": 0, "end_y": 1}
          },
          "inner_background_gradients": null,
          "party_icon_background_gradients": null,
          "h2_background_gradients": {
            "colors": [4278215167, 4288798207],
            "directions": {"begin_x": 0, "begin_y": -1, "end_x": 0, "end_y": 1}
          },
          "upper_footer_gradients": {
            "colors": [4278215167, 4288798207],
            "stops": null,
            "directions": {"begin_x": 1, "begin_y": 0, "end_x": -1, "end_y": 0}
          }
        },
        "text_color": null,
        "badge_text_color": 4294967295,
        "identity": {
          "user": {
            "id": 18,
            "name": "Uday Kambhampati",
            "photo_url":
                "https://a-cdn.thecircleapp.in/production/admin-media/24/deba8a92-2ba4-4f0f-885a-1a2bf063e643.png",
            "badge": null
          },
          "type": "gold_lettered_user",
          "is_user_position_back": true,
          "show_badge_ribbon": true,
          "party_highlight_color_primary": 4288798207,
          "slogan_text": "",
          "party_icon_url": null
        },
        "party_icon": null,
        "header_1_photos": [
          {
            "radius": 44,
            "position_x": 39,
            "position_y": 33.6,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/f3cf65a7-a249-4594-be3c-8547256207ad.png"
          },
          {
            "radius": 44,
            "position_x": 477,
            "position_y": 33.6,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/27e7d7e2-8b77-46e2-b2e3-01ba3becbeb6.png"
          }
        ],
        "header_2_photos": [
          {
            "radius": 27.439999999999998,
            "position_x": 165.06,
            "position_y": 63.767999999999994,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/80e19ee8-02bc-449c-937e-b19dea22162c.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 242.06,
            "position_y": 63.767999999999994,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/cdbefce9-c75e-4b7d-a160-c71cfbef8287.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 319.06,
            "position_y": 63.767999999999994,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/9804e1c0-9b44-4df9-a942-92616523764f.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 396.06,
            "position_y": 63.767999999999994,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/16471dbe-455c-458b-8729-38d16b14c93f.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 93.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/705b5658-72dd-4581-9c32-1ee385a995e9.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 168.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/58783e99-a39f-419d-803f-342a4b6daaaf.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 243.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/35291be4-d164-411c-935a-cdea0768c135.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 318.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/a76f3b3f-0084-4794-8e9c-c491eccc3c49.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 393.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/223e67f9-7fe1-47c1-9316-cb2f6d757de2.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 468.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/e5f8e6e1-3cec-4359-96b3-fe29bbfdb47a.png"
          }
        ],
        "is_locked": false,
        "id": 16,
        "is_bordered_layout": false,
        "neutral_frame": false,
        "enable_outer_frame": true,
        "frame_type": "status",
        "fonts_config": {
          "name": {
            "font_family": "Anek Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          },
          "badge": {
            "font_family": "Noto Sans Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          }
        },
        "is_selected": true,
        "analytics_params": {
          "layout_type": "premium",
          "is_locked": false,
          "golden_frame": true,
          "header_1_count": 2,
          "header_2_count": 10,
          "identity_type": "gold_lettered_user",
          "is_user_position_back": true,
          "circle_id": 33010,
          "frame_type": "status",
          "is_circle_sponsored_frame": false,
          "is_circle_sponsored_frame_locked": false,
          "user_posters_subscription_status": "paid"
        }
      },
      "params": {
        "creative_id": 33,
        "creative_kind": "info",
        "circle_id": 33010,
      }
    },
    {
      "creative": {
        "v2_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/a307dafb-9146-4c17-ae00-fb05bcaed861.jpg",
        "v1_url":
            "https://a-cdn.thecircleapp.in/production/admin-media/33/9a3ea81b-4c4f-402c-9c6e-fe93a55fefaf.jpg",
        "paid": false,
        "h1_background_type": "light",
        "h2_background_type": "sticker",
        "leader_photo_ring_color": 4294967295,
        "h2_background_gradients": {
          "colors": [4278215167, 4288798207],
          "directions": {"begin_x": 0, "begin_y": -1, "end_x": 0, "end_y": 1}
        },
        "is_locked": false,
        "analytics_params": {
          "h1_background_type": "light",
          "h2_background_type": "sticker",
          "photo_v2_supported": true,
          "photo_v3_supported": true,
          "paid": false,
          "circle_id": 33010,
          "circle_name": "తెలంగాణ",
          "circle_name_en": "Telangana",
          "circle_type": "location",
          "circle_level": "state",
          "category_kind": "info"
        }
      },
      "layout": {
        "layout_type": "premium",
        "golden_frame": true,
        "show_praja_logo": true,
        "shadow_color": null,
        "v1": null,
        "share_text": " ",
        "gradients": {
          "background_gradients": {
            "colors": [
              4288379660,
              4291930136,
              4294171000,
              4294964419,
              4294967295,
              4294964419,
              4294171001,
              4291930136,
              4288379660
            ],
            "stops": [
              0.0141,
              0.1252,
              0.3134,
              0.4424,
              0.5285,
              0.6468,
              0.7705,
              0.9587,
              1.0465
            ],
            "directions": {
              "begin_x": 1,
              "begin_y": 0.1,
              "end_x": -1,
              "end_y": -0.1
            }
          },
          "badge_banner_gradients": {
            "colors": [16777215, 16777215],
            "stops": [0, 1],
            "directions": {"begin_x": -1, "begin_y": 0, "end_x": 1, "end_y": 0}
          },
          "badge_ribbon_background_gradients": {
            "colors": [
              2164025858,
              2163337504,
              2164260863,
              2164025858,
              2163337504
            ],
            "stops": [-0.0389, 0.235, 0.4109, 0.7924, 1.0421],
            "directions": {"begin_x": -1, "begin_y": 0, "end_x": 1, "end_y": 0}
          },
          "footer_gradients": {
            "colors": [
              4293836359,
              4294499191,
              4294962838,
              4294961327,
              4294962838,
              4288832030,
              4293829964,
              4294499191,
              4293836359,
              4288575773
            ],
            "stops": [
              0,
              0.0816,
              0.1753,
              0.2586,
              0.4097,
              0.5451,
              0.6545,
              0.8264,
              0.9253,
              1
            ],
            "directions": {
              "begin_x": 1,
              "begin_y": 0.1,
              "end_x": -1,
              "end_y": -0.1
            }
          },
          "identity_border_gradients": {
            "colors": [
              4288379660,
              4291930136,
              4294171000,
              4294964419,
              4294967295,
              4294964419,
              4294171001,
              4291930136,
              4288379660
            ],
            "stops": [
              0.0141,
              0.1252,
              0.3134,
              0.4424,
              0.5285,
              0.6468,
              0.7705,
              0.9587,
              1.0465
            ],
            "directions": {"begin_x": 0, "begin_y": -1, "end_x": 0, "end_y": 1}
          },
          "identity_inner_background_gradients": {
            "colors": [4278215167, 4288798207],
            "stops": [0, 1],
            "directions": {"begin_x": 0, "begin_y": -1, "end_x": 0, "end_y": 1}
          },
          "inner_background_gradients": {
            "colors": [
              4294171000,
              4292714264,
              4294964419,
              4294964419,
              4294171001,
              4292714264
            ],
            "stops": [0.0909, 0.1889, 0.4163, 0.5715, 0.7349, 0.8752],
            "directions": {
              "begin_x": -1,
              "begin_y": 0.1,
              "end_x": 1,
              "end_y": -0.1
            }
          },
          "party_icon_background_gradients": null,
          "h2_background_gradients": {
            "colors": [4278215167, 4288798207],
            "directions": {"begin_x": 0, "begin_y": -1, "end_x": 0, "end_y": 1}
          },
          "upper_footer_gradients": {
            "colors": [4278215167, 4288798207],
            "stops": null,
            "directions": {"begin_x": 1, "begin_y": 0, "end_x": -1, "end_y": 0}
          }
        },
        "text_color": 4279519637,
        "badge_text_color": 4294967295,
        "identity": {
          "user": {
            "id": 18,
            "name": "Uday Kambhampati",
            "photo_url":
                "https://a-cdn.thecircleapp.in/production/admin-media/24/deba8a92-2ba4-4f0f-885a-1a2bf063e643.png",
            "badge": null
          },
          "type": "curved_with_depth",
          "is_user_position_back": true,
          "show_badge_ribbon": true,
          "party_highlight_color_primary": 4288798207,
          "slogan_text": "",
          "party_icon_url": null
        },
        "party_icon": null,
        "header_1_photos": [
          {
            "radius": 44,
            "position_x": 39,
            "position_y": 33.6,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/f3cf65a7-a249-4594-be3c-8547256207ad.png"
          },
          {
            "radius": 44,
            "position_x": 477,
            "position_y": 33.6,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/27e7d7e2-8b77-46e2-b2e3-01ba3becbeb6.png"
          }
        ],
        "header_2_photos": [
          {
            "radius": 27.439999999999998,
            "position_x": 165.06,
            "position_y": 63.767999999999994,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/80e19ee8-02bc-449c-937e-b19dea22162c.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 242.06,
            "position_y": 63.767999999999994,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/cdbefce9-c75e-4b7d-a160-c71cfbef8287.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 319.06,
            "position_y": 63.767999999999994,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/9804e1c0-9b44-4df9-a942-92616523764f.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 396.06,
            "position_y": 63.767999999999994,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/16471dbe-455c-458b-8729-38d16b14c93f.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 93.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/705b5658-72dd-4581-9c32-1ee385a995e9.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 168.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/58783e99-a39f-419d-803f-342a4b6daaaf.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 243.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/35291be4-d164-411c-935a-cdea0768c135.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 318.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/a76f3b3f-0084-4794-8e9c-c491eccc3c49.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 393.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/223e67f9-7fe1-47c1-9316-cb2f6d757de2.png"
          },
          {
            "radius": 27.439999999999998,
            "position_x": 468.06,
            "position_y": 124.56800000000001,
            "photo_url":
                "https://a-cdn.thecircleapp.in/fit-in/200x200/filters:quality(80)/production/admin-media/24/e5f8e6e1-3cec-4359-96b3-fe29bbfdb47a.png"
          }
        ],
        "is_locked": false,
        "id": 17,
        "is_bordered_layout": false,
        "neutral_frame": false,
        "enable_outer_frame": true,
        "frame_type": "status",
        "fonts_config": {
          "name": {
            "font_family": "Anek Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          },
          "badge": {
            "font_family": "Noto Sans Telugu",
            "font_style": "normal",
            "font_weight": "w700"
          }
        },
        "is_selected": true,
        "analytics_params": {
          "layout_type": "premium",
          "is_locked": false,
          "golden_frame": true,
          "header_1_count": 2,
          "header_2_count": 10,
          "identity_type": "curved_with_depth",
          "is_user_position_back": true,
          "circle_id": 33010,
          "frame_type": "status",
          "is_circle_sponsored_frame": false,
          "is_circle_sponsored_frame_locked": false,
          "user_posters_subscription_status": "paid"
        }
      },
      "params": {
        "creative_id": 33,
        "creative_kind": "info",
        "circle_id": 33010,
      }
    }
  ],
};

List<PosterCarousel> mockPosterCarousels() {
  return [
    PosterCarousel.fromJson(posterCarouselPremiumMocks),
    PosterCarousel.fromJson(posterCarouselTrialPmfHitMocks),
    PosterCarousel.fromJson(posterCarouselFreeShareMocks),
    PosterCarousel.fromJson(posterCarouselSposoredShareMocks),
    PosterCarousel.fromJson(posterCarouselPremiumPitchMock),
    PosterCarousel.fromJson(posterCarouselFanRequestMock),
  ];
}
