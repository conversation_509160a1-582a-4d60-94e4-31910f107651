import 'package:flutter/material.dart';
import 'package:praja/styles.dart';

typedef AsyncVoidCallback = Future<void> Function();

class LoadingIconButton extends StatefulWidget {
  final AsyncVoidCallback onPressed;
  final double iconSize;
  final Color iconColor;
  final IconData iconData;
  final BoxConstraints? constraints;

  const LoadingIconButton(
      {super.key,
      required this.onPressed,
      required this.iconData,
      this.iconSize = 24.0,
      this.iconColor = Styles.circleIndigo,
      this.constraints});

  @override
  State<StatefulWidget> createState() {
    return _LoadingIconButtonState();
  }
}

class _LoadingIconButtonState extends State<LoadingIconButton> {
  bool isLoading = false;

  void onPressed() async {
    if (isLoading) {
      return;
    }

    setState(() {
      isLoading = true;
    });
    await widget.onPressed();

    if (!mounted) return;

    setState(() {
      isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: isLoading
          ? SizedBox(
              height: widget.iconSize,
              width: widget.iconSize,
              child: Padding(
                padding: const EdgeInsets.all(4.0),
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(widget.iconColor),
                ),
              ),
            )
          : Icon(widget.iconData,
              color: widget.iconColor, size: widget.iconSize),
      constraints: widget.constraints,
      onPressed: isLoading ? null : onPressed,
    );
  }
}
