import 'package:flutter/material.dart';
import 'package:praja/common/feed_toast/feed_toast_item.dart';
import 'package:praja/test/mocks/feed_toast_mock.dart';

class FeedToastDemo extends StatelessWidget {
  const FeedToastDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: feedToastMocks.length,
      itemBuilder: (context, index) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 10.0, bottom: 10.0),
              child: Text(
                titles[index],
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
            FeedToastItem(
              feedToast: feedToastMocks[index],
              source: "testing_page",
            ),
            const SizedBox(height: 10)
          ],
        );
      },
    );
  }
}
