import 'package:flutter/material.dart';

class TrapezoidShapedBG extends StatelessWidget {
  final TrapezoidShapedBgEnum clipperAlignment;
  final Color color;
  const TrapezoidShapedBG(
      {super.key, required this.clipperAlignment, required this.color});

  @override
  Widget build(BuildContext context) {
    return ClipPath(
      clipper: TrapezoidShapedBgTexture(clipperAlignment: clipperAlignment),
      child: Container(
        color: color,
        height: double.infinity,
        width: double.infinity,
      ),
    );
  }
}

class TrapezoidShapedBgTexture extends CustomClipper<Path> {
  final TrapezoidShapedBgEnum clipperAlignment;
  TrapezoidShapedBgTexture({required this.clipperAlignment});

  @override
  Path getClip(Size size) {
    final path = Path();
    if (clipperAlignment == TrapezoidShapedBgEnum.left) {
      path.moveTo(30, 0);
      path.lineTo(size.width, 0);
      path.lineTo(size.width, size.height);
      path.lineTo(0, size.height);
      path.lineTo(30, 0);
      path.close();
      return path;
    } else {
      path.moveTo(0, 0);
      path.lineTo(size.width - 30, 0);
      path.lineTo(size.width, size.height);
      path.lineTo(0, size.height);
      path.lineTo(0, 0);
      path.close();
      return path;
    }
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}

enum TrapezoidShapedBgEnum { left, right }
