import 'package:flutter/material.dart';
import 'package:praja_posters/src/extensions/poster_gradient_extension.dart';
import 'package:praja_posters/src/models/poster_gradient.dart';

class PremiumFrameIdentityBg extends StatelessWidget {
  final PosterGradient gradient;

  const PremiumFrameIdentityBg({super.key, required this.gradient});

  @override
  Widget build(BuildContext context) {
    return ClipPath(
        clipper: _Clipper(),
        child: Container(
            width: double.infinity,
            height: double.infinity,
            decoration: BoxDecoration(gradient: gradient.toGradient())));
  }
}

class _Clipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();
    // if (userPhotoAlignment == UserPhotoAlignment.left) {
    //   path.moveTo(size.width, 0);
    //   path.cubicTo(size.width / 2, 0, size.width / 2, 5 * size.height / 8, 0,
    //       size.height / 2);
    //   path.lineTo(0, size.height);
    //   path.lineTo(size.width, size.height);
    //   path.lineTo(size.width, 0);
    //   path.close();
    // } else {
    path.moveTo(0, 0);
    path.cubicTo(size.width / 2, 0, size.width / 2, size.height / 2, size.width,
        7 * size.height / 25);
    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.lineTo(0, 0);
    path.close();
    // }

    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) => false;
}

class PremiumFrameIdentityBgDemo extends StatelessWidget {
  const PremiumFrameIdentityBgDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
            width: 320,
            height: 84,
            child: PremiumFrameIdentityBg(
              gradient: ycpPartyGradients,
            )),
        const SizedBox(height: 16),
        SizedBox(
            width: 320,
            height: 84,
            child: PremiumFrameIdentityBg(
              gradient: ycpPartyGradients,
            )),
      ],
    );
  }
}
