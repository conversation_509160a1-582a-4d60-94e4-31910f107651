// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'poster_fonts_config.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PosterFontsConfig _$PosterFontsConfigFromJson(Map<String, dynamic> json) =>
    PosterFontsConfig(
      name: json['name'] == null
          ? null
          : PosterFontConfig.fromJson(json['name'] as Map<String, dynamic>),
      badge: json['badge'] == null
          ? null
          : PosterFontConfig.fromJson(json['badge'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PosterFontsConfigToJson(PosterFontsConfig instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('name', instance.name?.toJson());
  writeNotNull('badge', instance.badge?.toJson());
  return val;
}
