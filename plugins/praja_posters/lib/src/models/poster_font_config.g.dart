// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'poster_font_config.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PosterFontConfig _$PosterFontConfigFromJson(Map<String, dynamic> json) =>
    PosterFontConfig(
      fontFamily: json['font_family'] as String,
      fontStyle: json['font_style'] as String? ?? 'normal',
      fontWeight: json['font_weight'] as String? ?? 'w700',
    );

Map<String, dynamic> _$PosterFontConfigToJson(PosterFontConfig instance) =>
    <String, dynamic>{
      'font_family': instance.fontFamily,
      'font_style': instance.fontStyle,
      'font_weight': instance.fontWeight,
    };
