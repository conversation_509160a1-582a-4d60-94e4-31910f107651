import 'package:json_annotation/json_annotation.dart';

part 'poster_badge.g.dart';

@JsonSerializable()
class PosterBadge {
  final int id;
  final bool active;

  @Json<PERSON>ey(name: 'icon_url')
  final String? badgeIconUrl;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'badge_text', defaultValue: '')
  final String badgeRole;

  @Json<PERSON>ey(name: 'badge_banner', defaultValue: PosterBadgeBanner.none)
  final PosterBadgeBanner badgeBanner;

  @<PERSON>son<PERSON>ey(name: 'badge_ring', defaultValue: PosterBadgeRing.noRing)
  final PosterBadgeRing badgeRing;

  @Json<PERSON>ey(name: 'description', defaultValue: '')
  final String description;

  PosterBadge({
    required this.id,
    required this.active,
    required this.badgeIconUrl,
    required this.badgeRole,
    required this.badgeRing,
    required this.badgeBanner,
    required this.description,
  });

  factory PosterBadge.fromJson(Map<String, dynamic> json) =>
      _$PosterBadgeFromJson(json);

  Map<String, dynamic> toJson() => _$PosterBadgeToJson(this);

  @override
  String toString() {
    return 'Badge{id: $id, active: $active, badgeIconUrl: $badgeIconUrl, badgeRole: $badgeRole, badgeRing: $badgeRing, badgeBanner: $badgeBanner, description: $description}';
  }
}

enum PosterBadgeBanner {
  @JsonValue("GOLD")
  gold,
  @JsonValue("SILVER")
  silver,
  @JsonValue("WHITE")
  white,
  @JsonValue("NONE")
  none
}

enum PosterBadgeRing {
  @JsonValue("GOLD_RING")
  goldRing,
  @JsonValue("SILVER_RING")
  silverRing,
  @JsonValue("NO_RING")
  noRing
}
