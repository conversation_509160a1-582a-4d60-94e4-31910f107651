import 'package:praja_posters/src/auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:gradient_borders/box_borders/gradient_box_border.dart';
import 'package:praja_posters/src/extensions/poster_gradient_extension.dart';
import 'package:praja_posters/src/models/poster_badge.dart';
import 'package:praja_posters/src/models/poster_font_config.dart';
import 'package:praja_posters/src/models/poster_gradient.dart';
import 'package:praja_posters/src/ui_widgets/poster_flat_badge_ribbon.dart';
import 'package:praja_posters/src/ui_widgets/praja_poster_image.dart';
import 'package:praja_posters/src/utils/font_utils.dart';

const double _identityBoxHeight = 122;
const double _partyIconHeight = 48;
const double _partyIconWidth = 48;
const double _extendedPartyIconHeight = 52;
const double _extendedPartyIconWidth = 52;

/// Supports [PosterIdentityType.shinyIdentityWithLowShadow], [PosterIdentityType.shinyIdentity]
class ShinyIdentity extends StatelessWidget {
  final String name;
  final PosterBadge? badge;
  final PosterGradient footerGradients;
  final PosterGradient? badgeBannerGradients;
  final PosterGradient? identityInnerBackgroundGradients;
  final PosterGradient? borderGradients;
  final double minNameFontSize;
  final double maxNameFontSize;
  final double minBadgeTextFontSize;
  final double maxBadgeTextFontSize;
  final int nameTextColor;
  final int badgeTextColor;
  final String? partyIcon;
  final bool isHighShadowVariant;
  final int? primaryHighlightColor;
  final int? secondaryHighlightColor;
  final PosterFontConfig? nameFontConfig;
  final PosterFontConfig? badgeFontConfig;
  final bool showBadgeRibbon;

  const ShinyIdentity({
    super.key,
    required this.name,
    this.badge,
    required this.footerGradients,
    this.badgeBannerGradients,
    this.identityInnerBackgroundGradients,
    this.borderGradients,
    this.minNameFontSize = 14,
    this.maxNameFontSize = 22,
    this.minBadgeTextFontSize = 9,
    this.maxBadgeTextFontSize = 11,
    required this.nameTextColor,
    required this.badgeTextColor,
    this.partyIcon,
    this.isHighShadowVariant = false,
    this.primaryHighlightColor,
    this.secondaryHighlightColor,
    this.nameFontConfig,
    this.badgeFontConfig,
    required this.showBadgeRibbon,
  });

  Widget _buildRibbonWidget() {
    final badge = this.badge;
    final showBadgeStrip =
        badge != null && badge.active && badge.description.isNotEmpty;
    final badgeBannerGradients = this.badgeBannerGradients;
    if (badgeBannerGradients == null) {
      return const SizedBox();
    }
    return showBadgeStrip
        ? PosterFlatBadgeRibbon(
            text: badge.description,
            outlineType: badge.badgeBanner,
            backgroundGradient: badgeBannerGradients,
            minBadgeTextFontSize: minBadgeTextFontSize,
            maxBadgeTextFontSize: maxBadgeTextFontSize,
            badgeTextColor: badgeTextColor,
            badgeFontConfig: badgeFontConfig,
          )
        : const SizedBox();
  }

  Widget _buildBadgeRoleWidget() {
    final badge = this.badge;
    if (badge == null || !badge.active || badge.description.isEmpty) {
      return const SizedBox();
    }
    return AutoSizeText(
      badge.description,
      maxLines: 1,
      textAlign: TextAlign.center,
      minFontSize: minBadgeTextFontSize,
      maxFontSize: maxBadgeTextFontSize,
      textScaler: const TextScaler.linear(1.0),
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        color: Color(badgeTextColor),
        fontSize: maxBadgeTextFontSize,
        fontWeight: FontWeight.bold,
        fontFamily: FontUtils.getFontFamily(fontConfig: badgeFontConfig),
      ),
    );
  }

  Widget _buildNameWidget() {
    final nameBrightness =
        ThemeData.estimateBrightnessForColor(Color(nameTextColor));
    return AutoSizeText(
      name,
      maxLines: 1,
      textAlign: TextAlign.center,
      minFontSize: minNameFontSize,
      maxFontSize: maxNameFontSize,
      textScaler: const TextScaler.linear(1.0),
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        shadows: [
          Shadow(
            color: nameBrightness == Brightness.light
                ? Colors.black
                : Colors.white,
            offset: const Offset(-2, 0),
            blurRadius: 0,
          ),
        ],
        color: Color(nameTextColor),
        fontSize: maxNameFontSize,
        fontWeight: FontWeight.bold,
        fontFamily: FontUtils.getFontFamily(fontConfig: nameFontConfig),
      ),
    );
  }

  Widget _getPartyIcon() {
    final partyIcon = this.partyIcon;
    final borderGradients = this.borderGradients;
    if (partyIcon == null) {
      return const SizedBox();
    }
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: (!isHighShadowVariant && borderGradients != null)
            ? GradientBoxBorder(
                gradient: borderGradients.toGradient(),
              )
            : Border.all(
                color: Colors.white,
                width: 2,
              ),
      ),
      child: PrajaPosterImage(
        imageUrl: partyIcon,
        height:
            !isHighShadowVariant ? _extendedPartyIconHeight : _partyIconHeight,
        width: !isHighShadowVariant ? _extendedPartyIconWidth : _partyIconWidth,
        fit: BoxFit.cover,
        alignment: Alignment.topCenter,
        placeholder: (_, url) => const SizedBox(),
        fadeInDuration: const Duration(milliseconds: 100),
      ),
    );
  }

  Widget _getLeftBlurContainer() {
    final primaryHighlightColor = this.primaryHighlightColor;
    if (primaryHighlightColor == null) {
      return const SizedBox();
    }
    return Container(
      height: 66,
      width: 66,
      decoration: BoxDecoration(shape: BoxShape.circle, boxShadow: [
        BoxShadow(
          color: Color(primaryHighlightColor).withOpacity(0.2),
          spreadRadius: 20,
          blurRadius: 10,
          offset: const Offset(0, 0), // changes position of shadow
        ),
        BoxShadow(
          color: Color(primaryHighlightColor)
              .withOpacity(!isHighShadowVariant ? 0.2 : 0.9),
          spreadRadius: 1,
          blurRadius: 12,
          offset: const Offset(0, 0), // changes position of shadow
        ),
      ]),
    );
  }

  Widget _getRightBlurContainer() {
    final secondaryHighlightColor = this.secondaryHighlightColor;
    if (secondaryHighlightColor == null || !isHighShadowVariant) {
      return const SizedBox();
    }
    return Container(
      height: 96,
      width: 96,
      decoration: BoxDecoration(shape: BoxShape.circle, boxShadow: [
        BoxShadow(
          color: Color(secondaryHighlightColor).withOpacity(0.02),
          spreadRadius: 20,
          blurRadius: 10,
          offset: const Offset(0, 0), // changes position of shadow
        ),
        BoxShadow(
          color: Color(secondaryHighlightColor).withOpacity(0.2),
          spreadRadius: 10,
          blurRadius: 12,
          offset: const Offset(0, 0), // changes position of shadow
        ),
      ]),
    );
  }

  Widget _getBody() {
    final badge = this.badge;
    final hasBadge =
        badge != null && badge.active && badge.description.isNotEmpty;
    final innerBackgroundGradient = identityInnerBackgroundGradients;
    if (innerBackgroundGradient == null) {
      return const SizedBox();
    }
    return Container(
      height: _identityBoxHeight,
      padding: const EdgeInsets.only(left: 50, right: 16, top: 12, bottom: 12),
      decoration: BoxDecoration(
        gradient: innerBackgroundGradient.toGradient(),
      ),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Positioned(
            left: -28,
            top: 18,
            child: _getLeftBlurContainer(),
          ),
          Container(
            decoration: BoxDecoration(
              gradient: footerGradients.toGradient(),
              borderRadius: BorderRadius.circular(60),
              border: borderGradients != null
                  ? GradientBoxBorder(
                      gradient: borderGradients!.toGradient(),
                      width: 2,
                    )
                  : null,
            ),
            child: Stack(
              clipBehavior: Clip.none,
              alignment: Alignment.center,
              children: [
                Positioned(
                  right: 0,
                  top: 0,
                  child: _getRightBlurContainer(),
                ),
                if (isHighShadowVariant) ...[
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(60),
                      gradient: LinearGradient(
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                        colors: [
                          Colors.transparent,
                          secondaryHighlightColor != null
                              ? Color(secondaryHighlightColor!).withOpacity(0.4)
                              : Colors.transparent, // secondary highlight color
                        ],
                        stops: const [0.0, 0.95],
                      ),
                    ),
                  ),
                ],
                Padding(
                  padding: const EdgeInsets.only(left: 62, right: 32),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Expanded(
                        flex: hasBadge ? 65 : 100,
                        child: Container(
                          alignment: Alignment.center,
                          child: _buildNameWidget(),
                        ),
                      ),
                      Expanded(
                        flex: hasBadge ? 35 : 0,
                        child: Padding(
                          padding: EdgeInsets.only(bottom: hasBadge ? 6.0 : 0),
                          child: showBadgeRibbon
                              ? _buildRibbonWidget()
                              : _buildBadgeRoleWidget(),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            left: -16,
            top: 24,
            child: _getPartyIcon(),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return _getBody();
  }
}
