import 'package:json_annotation/json_annotation.dart';

enum PosterIdentityType {
  @JsonValue("curved")
  curved("curved"),
  @JsonValue("curved_with_depth")
  curvedWithDepth("curved_with_depth"),
  @JsonValue("flat_user_back")
  flatUserBack("flat_user_back"),
  @JsonValue("flat_user_front")
  flatUserFront("flat_user_front"),
  @JsonValue("flat_user_badge_circle")
  flatUserBadgeCircle("flat_user_badge_circle"),
  @JsonValue("gold_lettered_user_back")
  goldLetteredUserBack("gold_lettered_user_back"),
  @JsonValue("gold_lettered_user_front")
  goldLetteredUserFront("gold_lettered_user_front"),

  // //Identities V2
  @JsonValue("flat_user")
  flatUser("flat_user"),
  @JsonValue("gold_lettered_user")
  goldLetteredUser("gold_lettered_user"),
  @JsonValue("glassy_user")
  glassyUser("glassy_user"),

  // Identities V3
  @JsonValue("plain_identity")
  plainIdentity("plain_identity"),
  @JsonValue("plain_identity_with_party_icon")
  plainIdentityWithPartyIcon("plain_identity_with_party_icon"),
  @JsonValue("party_slogan_identity")
  partySloganIdentity("party_slogan_identity"),
  @JsonValue("party_slogan_identity_with_party_icon")
  partySloganIdentityWithPartyIcon("party_slogan_identity_with_party_icon"),
  @JsonValue("linear_name_and_role_identity")
  linearNameAndRoleIdentity("linear_name_and_role_identity"),
  @JsonValue("trapezoidal_identity")
  trapezoidalIdentity("trapezoidal_identity"),
  @JsonValue("top_trapezoidal_identity")
  topTrapezoidalIdentity("top_trapezoidal_identity"),
  @JsonValue("bottom_trapezoidal_identity")
  bottomTrapezoidalIdentity("bottom_trapezoidal_identity"),
  @JsonValue("stroked_border_identity")
  strokedBorderIdentity("stroked_border_identity"),
  @JsonValue("shiny_identity")
  shinyIdentity("shiny_identity"),
  @JsonValue("shiny_identity_with_low_shadow")
  shinyIdentityWithLowShadow("shiny_identity_with_low_shadow"),
  @JsonValue("multi_color_identity")
  multiColorIdentity("multi_color_identity"),
  @JsonValue("semi_circular_identity")
  semiCircularIdentity("semi_circular_identity"),
  //Status Identities
  @JsonValue("premium_cornered_party_icon_shiny_identity")
  premiumCorneredPartyIconShinyIdentity(
      "premium_cornered_party_icon_shiny_identity"),
  @JsonValue("premium_cornered_party_icon_gradient_identity")
  premiumCorneredPartyIconGradientIdentity(
      "premium_cornered_party_icon_gradient_identity"),
  @JsonValue("party_tag_identity")
  partyTagIdentity("party_tag_identity"),
  @JsonValue("polygonal_profile_identity")
  polygonalProfileIdentity("polygonal_profile_identity"),

  //Basic Identities
  /// For this Identity, The footer gradients are transparent.
  /// rest of all same as basic identity
  @JsonValue("basic_transparent_identity")
  basicTransparentIdentity("basic_transparent_identity"),

  /// For this Identity, The footer gradients are transparent.
  /// As well as we don't show user profile pic
  /// We will just display user name & role [if Exists]
  @JsonValue("basic_no_profile_pic_identity")
  basicNoProfilePicIdentity("basic_no_profile_pic_identity"),

  /// For this Identity we don't show user related data, it just our plain
  /// creative with praja logo [server driven]
  @JsonValue("basic_full_transparent_identity")
  basicFullTransparentIdentity("basic_full_transparent_identity"),
  @JsonValue("unknown")
  unknown("unknown");

  final String value;

  const PosterIdentityType(this.value);

  factory PosterIdentityType.from(String value) {
    return PosterIdentityType.values.firstWhere(
      (e) => e.value == value,
      orElse: () => PosterIdentityType.unknown,
    );
  }
}
