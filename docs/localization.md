# Localization

## Usage
We will take the example of a user profile page with a follower count, follow button and a share button.

### Step 1
**Add the keys for necessary strings in `string_key.dart`**

```dart
enum StringKey {
  //...
  userFollowerCountSuffix('user_follower_count_suffix'),
  userFollowButtonLabel('user_follow_button_label'),
  userShareButtonLabel('user_share_button_label'),
  ;
}
```

### Step 2
**Add the translations of each language in `assets/strings/<lang_code>.yaml` files in all supported languages**

`en.yaml`
```yaml
user_follower_count_suffix: 
  zero: 'followers'
  one: 'follower'
  other: 'followers'
user_follow_button_label: 'Follow %{name}'
user_share_button_label: 'Share'
```

`te.yaml`
```yaml
user_follower_count_suffix: 
  zero: 'అనుయాయులు'
  one: 'అనుయాయు'
  other: 'అనుయాయులు'
user_follow_button_label: 'అనుయాయించండి %{name}'
user_share_button_label: 'షేర్'
```

### Step 3
**Access the translations using `BuildContext.getString()` or `BuildContext.getPluralizedString()` in your widgets**

```dart
@override
Widget build(BuildContext context) {
  final shareButtonLabel = context.getString(StringKey.userShareButtonLabel);
  final followButtonLabel = context.getString(StringKey.userFollowButtonLabel, parameters: {'name': user.name});
  final followerCountSuffix = context.getPluralizedString(StringKey.userFollowerCountSuffix, user.followersCount);
  //...
}
```

### Naming Convention
- <feature_name>_<ui_element_name>
- Eg: `home_create_post_fab_label`, `poster_share_button_label`

### When to Use `listen: false` while using `context.getString()` or `context.getPluralizedString()`
- Now we have an optional parameter `listen` in `context.getString()` and `context.getPluralizedString()` which is `true` by default.
- If you are accessing the translation methods in `Event Handlers` or inside `initState`, `setState` or  `async operations` then you should use `listen: false`.
- When we set `listen: false`, then the widget won't rebuild even though the provider value changes.
- So use it only when required. Avoid as much as possible.

## UI Adjustments
Sometimes when we place a label beside an icon and want the text to be visually center aligned with the icon, we need to add a bit of top or bottom padding based on the language to get it visually center aligned.

Like:
`🖋️ Post` or `🖋️ పోస్ట్`

But it will depend upon each language (and font). So for convenience, we have created an extension function `BuildContext.verticalAdjustmentPadding()` to wrap the `Text` widget with a padding and achieve that visual center alignment based on the current locale being used.

```dart
Padding(
  padding: context.verticalAdjustmentPadding(fontSize: 18),
  child: Text(
    context.getString(
        StringKey.homeCreatePostFabLabel),
    textScaler: const TextScaler.linear(1.0),
    style: const TextStyle(fontSize: 18),
  ),
),
```

